#!/bin/bash

# Setup HTTPS for Kamikaze Backend on EC2
# This script sets up Nginx as a reverse proxy with SSL

echo "🚀 Setting up HTTPS for Kamikaze Backend..."

# Update system
echo "📦 Updating system packages..."
sudo apt update

# Install Nginx
echo "🔧 Installing Nginx..."
sudo apt install -y nginx

# Install Certbot for Let's Encrypt
echo "🔐 Installing Certbot..."
sudo apt install -y certbot python3-certbot-nginx

# Create Nginx configuration
echo "⚙️ Creating Nginx configuration..."
sudo tee /etc/nginx/sites-available/kamikaze > /dev/null <<EOF
server {
    listen 80;
    server_name YOUR_DOMAIN_HERE;  # Replace with your domain
    
    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name YOUR_DOMAIN_HERE;  # Replace with your domain
    
    # SSL configuration will be added by Certbot
    
    # Proxy settings
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
}
EOF

# Enable the site
echo "🔗 Enabling Nginx site..."
sudo ln -sf /etc/nginx/sites-available/kamikaze /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
echo "✅ Testing Nginx configuration..."
sudo nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Nginx configuration is valid"
    sudo systemctl restart nginx
    sudo systemctl enable nginx
else
    echo "❌ Nginx configuration error. Please check the configuration."
    exit 1
fi

echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Point your domain to this EC2 instance's public IP"
echo "2. Replace 'YOUR_DOMAIN_HERE' in /etc/nginx/sites-available/kamikaze with your actual domain"
echo "3. Run: sudo certbot --nginx -d your-domain.com"
echo "4. Update your frontend config to use https://your-domain.com"
echo ""
echo "Your backend should be running on port 8000"
echo "Nginx will proxy HTTPS traffic to your backend"

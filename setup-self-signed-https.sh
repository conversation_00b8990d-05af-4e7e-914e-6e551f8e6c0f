#!/bin/bash

# Quick HTTPS setup with self-signed certificate for testing
# WARNING: Self-signed certificates will show security warnings in browsers

echo "🚀 Setting up HTTPS with self-signed certificate for testing..."

# Update system
sudo apt update

# Install Nginx
sudo apt install -y nginx openssl

# Create self-signed certificate
echo "🔐 Creating self-signed certificate..."
sudo mkdir -p /etc/nginx/ssl
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/kamikaze.key \
    -out /etc/nginx/ssl/kamikaze.crt \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=*************"

# Create Nginx configuration
echo "⚙️ Creating Nginx configuration..."
sudo tee /etc/nginx/sites-available/kamikaze > /dev/null <<EOF
server {
    listen 80;
    server_name *************;
    
    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name *************;
    
    # SSL configuration
    ssl_certificate /etc/nginx/ssl/kamikaze.crt;
    ssl_certificate_key /etc/nginx/ssl/kamikaze.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    
    # Proxy settings
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # CORS headers (backup)
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        add_header Access-Control-Expose-Headers "Content-Length,Content-Range" always;
    }
    
    # Handle preflight requests
    location ~* \.(OPTIONS)$ {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type "text/plain; charset=utf-8";
        add_header Content-Length 0;
        return 204;
    }
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/kamikaze /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test and restart Nginx
sudo nginx -t && sudo systemctl restart nginx && sudo systemctl enable nginx

echo "🎉 Setup complete!"
echo ""
echo "⚠️  IMPORTANT: This uses a self-signed certificate!"
echo "Browsers will show security warnings that you'll need to accept."
echo ""
echo "Your backend is now available at:"
echo "- HTTPS: https://*************"
echo "- HTTP: http://************* (redirects to HTTPS)"
echo ""
echo "Update your frontend config to use: https://*************"

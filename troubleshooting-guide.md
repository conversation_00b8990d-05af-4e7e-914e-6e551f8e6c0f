# Kamikaze Network Error Troubleshooting Guide

## Problem
Getting "Network error. Please check your connection and try again." when signing in with `<EMAIL>` from Vercel deployment.

## Root Cause
**Mixed Content Security Issue**: Your frontend is deployed on Vercel (HTTPS) but trying to connect to your EC2 backend (HTTP). Modern browsers block HTTP requests from HTTPS sites for security reasons.

## Solutions (Choose One)

### Solution 1: Quick Fix - Self-Signed HTTPS (Recommended for Testing)

1. **SSH into your EC2 instance**
2. **Run the setup script:**
   ```bash
   chmod +x setup-self-signed-https.sh
   sudo ./setup-self-signed-https.sh
   ```
3. **Update your EC2 Security Group** to allow HTTPS traffic:
   - Go to AWS Console → EC2 → Security Groups
   - Find your instance's security group
   - Add inbound rule: HTTPS (443) from 0.0.0.0/0
4. **Test the backend:**
   ```bash
   curl -k https://*************/api/info
   ```
5. **Deploy your updated frontend** (config already updated to use HTTPS)

### Solution 2: Proper SSL with Domain (Production Ready)

1. **Get a domain name** (free options: freenom.com, or use AWS Route 53)
2. **Point domain to your EC2 IP** (*************)
3. **Run the proper HTTPS setup:**
   ```bash
   # Edit the script to replace YOUR_DOMAIN_HERE with your actual domain
   nano setup-https.sh
   chmod +x setup-https.sh
   sudo ./setup-https.sh
   ```
4. **Get SSL certificate:**
   ```bash
   sudo certbot --nginx -d your-domain.com
   ```
5. **Update frontend config** to use your domain instead of IP

### Solution 3: AWS Application Load Balancer (Enterprise)

1. Create an Application Load Balancer in AWS
2. Add SSL certificate via AWS Certificate Manager
3. Configure target group pointing to your EC2 instance
4. Update frontend config to use ALB endpoint

## Security Group Configuration

Ensure your EC2 security group has these inbound rules:
- **HTTP (80)**: 0.0.0.0/0 (for redirect to HTTPS)
- **HTTPS (443)**: 0.0.0.0/0 (for secure traffic)
- **Custom TCP (8000)**: 0.0.0.0/0 (for direct backend access if needed)

## Testing Steps

1. **Test backend directly:**
   ```bash
   # HTTP (should redirect to HTTPS)
   curl -I http://*************
   
   # HTTPS (should work)
   curl -k https://*************/api/info
   ```

2. **Test authentication endpoint:**
   ```bash
   curl -k -X POST https://*************/api/v1/auth/signin \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"your-password"}'
   ```

3. **Check browser console** for any remaining CORS or mixed content errors

## Current Configuration

- **Frontend (Vercel)**: https://kamikaze-8ovomajlg-ankitas-projects-77e2909a.vercel.app/
- **Backend (EC2)**: http://*************:8000 → https://************* (after setup)
- **CORS**: Already configured to allow your Vercel domain

## Next Steps

1. Choose and implement one of the solutions above
2. Test the authentication flow
3. Monitor for any remaining issues
4. Consider setting up monitoring and logging for production use

## Common Issues

- **Certificate warnings**: Normal with self-signed certificates, click "Advanced" → "Proceed"
- **Still getting network errors**: Check browser dev tools for specific error messages
- **CORS errors**: Verify your Vercel URL is in the backend CORS configuration
- **Connection refused**: Ensure Nginx is running and ports are open in security groups

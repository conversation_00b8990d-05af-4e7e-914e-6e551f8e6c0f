name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, cicd-fe ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'

jobs:
  # Quality checks job
  quality:
    name: Code Quality & Testing
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Type checking
        run: npm run type-check
        
      - name: Lint code
        run: npm run lint
        
      - name: Run tests
        run: npm run test:run
        
      - name: Generate test coverage
        run: npm run test:coverage
        
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

  # Build job
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: quality
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build for production
        run: npm run build
        env:
          NODE_ENV: production
          
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: dist/
          retention-days: 7

  # Deploy to Vercel
  deploy:
    name: Deploy to Vercel
    runs-on: ubuntu-latest
    needs: [quality, build]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/cicd-fe'
    environment: Production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Debug Secrets (remove after testing)
        run: |
          echo "VERCEL_TOKEN length: ${#VERCEL_TOKEN}"
          echo "VERCEL_ORG_ID length: ${#VERCEL_ORG_ID}"
          echo "VERCEL_PROJECT_ID length: ${#VERCEL_PROJECT_ID}"
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_ORG_ID: ${{ secrets.ORGID }}
          VERCEL_PROJECT_ID: ${{ secrets.PROJECTID }}

      - name: Deploy to Vercel
        run: vercel --prod --token=${{ secrets.VERCEL_TOKEN }} --yes
        env:
          VERCEL_ORG_ID: ${{ secrets.ORGID }}
          VERCEL_PROJECT_ID: ${{ secrets.PROJECTID }}

  # Preview deployment for PRs
  preview:
    name: Deploy Preview
    runs-on: ubuntu-latest
    needs: [quality, build]
    if: github.event_name == 'pull_request'
    environment: Production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
        
      - name: Deploy Preview to Vercel
        run: vercel --token=${{ secrets.VERCEL_TOKEN }} --yes
        env:
          VERCEL_ORG_ID: ${{ secrets.ORGID }}
          VERCEL_PROJECT_ID: ${{ secrets.PROJECTID }}

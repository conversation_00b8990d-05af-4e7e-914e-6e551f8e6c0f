# Vercel CI/CD Setup Guide

This guide will help you set up automatic deployments to Vercel using GitHub Actions.

## 🚀 Quick Setup

### 1. GitHub Secrets Configuration

You need to add your Vercel token as a GitHub secret:

1. Go to your GitHub repository
2. Navigate to **Settings** → **Secrets and variables** → **Actions**
3. Click **New repository secret**
4. Name: `VERCEL_TOKEN`
5. Value: `************************` (your token)

### 2. Vercel Project Configuration

The `.vercel/project.json` file contains placeholder values. You need to update them:

1. Go to your Vercel dashboard
2. Select your project
3. Go to **Settings** → **General**
4. Copy the **Project ID** and **Team ID** (or Personal Account ID)
5. Update `.vercel/project.json` with these values

### 3. Automatic Deployment

Once configured, your project will automatically deploy:

- **On push to `main` or `cicd-fe` branches**: Production deployment
- **On pull requests**: Preview deployment
- **On push to `develop`**: Quality checks and build (no deployment)

## 🔧 Manual Deployment

You can also deploy manually using the provided script:

```bash
# Set your Vercel token
export VERCEL_TOKEN=************************

# Deploy preview
./scripts/deploy-vercel.sh

# Deploy to production
./scripts/deploy-vercel.sh --prod
```

## 📋 What's Included

### CI/CD Pipeline (`.github/workflows/ci-cd.yml`)
- **Quality Checks**: Type checking, linting, testing, coverage
- **Build Process**: Production build with artifact storage
- **Deployment**: Automatic deployment to Vercel
- **Preview Deployments**: For pull requests

### Vercel Configuration
- **`vercel.json`**: Project settings and build configuration
- **`.vercel/project.json`**: Project identification and settings
- **`.vercelignore`**: Files to exclude from deployment

### Build Scripts
- **`scripts/deploy-vercel.sh`**: Local deployment script
- **Package.json scripts**: All necessary build and test commands

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**: Check the GitHub Actions logs for detailed error messages
2. **Deployment Failures**: Verify your Vercel token is correct and has proper permissions
3. **Missing Dependencies**: Ensure all dependencies are in `package.json`

### Debugging

1. **Check GitHub Actions**: Go to Actions tab in your repository
2. **Check Vercel Dashboard**: Monitor deployments in your Vercel project
3. **Local Testing**: Use the deployment script locally to test

## 🔒 Security Notes

- Your Vercel token is stored as a GitHub secret and is encrypted
- Never commit the token directly to your repository
- The token has access to deploy to your Vercel projects

## 📚 Additional Resources

- [Vercel CLI Documentation](https://vercel.com/docs/cli)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Vercel Deployment Documentation](https://vercel.com/docs/deployments)

## 🎯 Next Steps

1. Add the `VERCEL_TOKEN` secret to your GitHub repository
2. Update the project ID and org ID in `.vercel/project.json`
3. Push to your `main` or `cicd-fe` branch to trigger the first deployment
4. Monitor the deployment in GitHub Actions and Vercel dashboard

Your CI/CD pipeline is now ready! 🎉

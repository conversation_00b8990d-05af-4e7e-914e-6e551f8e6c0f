/**
 * Advanced Trading Bots Page
 * Professional trading bot management interface with comprehensive features
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { useTradingBots } from '@/hooks/useTradingBots';
import { useExchange } from '@/contexts/ExchangeContext';

// Import our new components
import { BotCreationWizard } from '@/components/trading-bots/BotManager/BotCreationWizard';
import { BotControlPanel } from '@/components/trading-bots/BotManager/BotControlPanel';
import { PerformanceDashboard } from '@/components/trading-bots/Performance/PerformanceDashboard';

import { TradingBot, BotStatus, StrategyType, RiskLevel } from '@/types/tradingBot';
import {
  Bot,
  Plus,
  Search,
  Filter,
  Settings,
  Activity,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Shield,
  Zap,
  AlertTriangle,
  CheckCircle,
  Wifi,
  WifiOff,
  RefreshCw,
  BarChart3,
  Target,
  Clock,
  Sparkles
} from 'lucide-react';

const TradingBotsAdvanced: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState('overview');
  const [selectedBot, setSelectedBot] = useState<TradingBot | null>(null);
  const [showCreateWizard, setShowCreateWizard] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [strategyFilter, setStrategyFilter] = useState<string>('all');

  const { toast } = useToast();
  const { hasConnectedExchange } = useExchange();

  const {
    bots,
    loading,
    error,
    isConnected,
    refreshBots,
    selectBot,
    deleteBot
  } = useTradingBots();

  // Filter bots based on search and filters
  const filteredBots = bots.filter(bot => {
    const matchesSearch = bot.config.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         bot.config.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' || bot.status === statusFilter;

    const matchesStrategy = strategyFilter === 'all' ||
                           bot.config.strategy.strategy_type === strategyFilter;

    return matchesSearch && matchesStatus && matchesStrategy;
  });

  // Calculate summary statistics
  const summaryStats = {
    total: bots.length,
    running: bots.filter(bot => bot.status === BotStatus.RUNNING).length,
    paused: bots.filter(bot => bot.status === BotStatus.PAUSED).length,
    stopped: bots.filter(bot => bot.status === BotStatus.STOPPED).length,
    error: bots.filter(bot => bot.status === BotStatus.ERROR).length,
    totalPnL: bots.reduce((sum, bot) => sum + bot.metrics.performance.total_return, 0),
    totalTrades: bots.reduce((sum, bot) => sum + bot.metrics.performance.total_trades, 0),
    avgWinRate: bots.length > 0
      ? bots.reduce((sum, bot) => sum + bot.metrics.performance.win_rate, 0) / bots.length
      : 0
  };

  const handleBotCreated = (bot: TradingBot) => {
    toast({
      title: 'Success',
      description: `Trading bot "${bot.config.name}" created successfully!`
    });
    refreshBots();
  };

  const handleDeleteBot = async (bot: TradingBot) => {
    if (window.confirm(`Are you sure you want to delete "${bot.config.name}"? This action cannot be undone.`)) {
      const success = await deleteBot(bot.id);
      if (success && selectedBot?.id === bot.id) {
        setSelectedBot(null);
      }
    }
  };

  const handleViewDetails = (bot: TradingBot) => {
    setSelectedBot(bot);
    setSelectedTab('details');
  };

  // Check if user has connected exchange (but don't block the page)
  const isExchangeConnected = hasConnectedExchange();

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Strategies</h1>
          <p className="text-muted-foreground">
            Professional automated trading with advanced strategies and risk management
          </p>
        </div>

        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {isConnected ? (
              <div className="flex items-center gap-2 text-green-600">
                <Wifi className="h-4 w-4" />
                <span className="text-sm">Connected</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-red-600">
                <WifiOff className="h-4 w-4" />
                <span className="text-sm">Disconnected</span>
              </div>
            )}
          </div>

          <Button
            variant="outline"
            onClick={refreshBots}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Button
            onClick={() => {
              if (!isExchangeConnected) {
                toast({
                  title: 'Exchange Connection Required',
                  description: 'You need to connect an exchange account to create trading bots. Please go to Settings to connect your exchange.',
                  variant: 'destructive'
                });
                return;
              }
              setShowCreateWizard(true);
            }}
            className="bg-gradient-to-r from-primary to-primary/80"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Bot
          </Button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Bot className="h-4 w-4 text-primary" />
              <span className="text-sm text-muted-foreground">Total Bots</span>
            </div>
            <div className="text-2xl font-bold mt-2">{summaryStats.total}</div>
            <div className="text-xs text-muted-foreground">
              {summaryStats.running} running • {summaryStats.paused} paused
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-green-600" />
              <span className="text-sm text-muted-foreground">Total P&L</span>
            </div>
            <div className={`text-2xl font-bold mt-2 ${summaryStats.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              ${summaryStats.totalPnL.toFixed(2)}
            </div>
            <div className="text-xs text-muted-foreground">
              Across all bots
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-blue-600" />
              <span className="text-sm text-muted-foreground">Total Trades</span>
            </div>
            <div className="text-2xl font-bold mt-2">{summaryStats.totalTrades}</div>
            <div className="text-xs text-muted-foreground">
              All time
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-purple-600" />
              <span className="text-sm text-muted-foreground">Avg Win Rate</span>
            </div>
            <div className="text-2xl font-bold mt-2">{summaryStats.avgWinRate.toFixed(1)}%</div>
            <div className="text-xs text-muted-foreground">
              Portfolio average
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Bot Overview</TabsTrigger>
          <TabsTrigger value="details" disabled={!selectedBot}>
            {selectedBot ? `${selectedBot.config.name} Details` : 'Bot Details'}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search bots by name or description..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value={BotStatus.RUNNING}>Running</SelectItem>
                <SelectItem value={BotStatus.PAUSED}>Paused</SelectItem>
                <SelectItem value={BotStatus.STOPPED}>Stopped</SelectItem>
                <SelectItem value={BotStatus.ERROR}>Error</SelectItem>
              </SelectContent>
            </Select>

            <Select value={strategyFilter} onValueChange={setStrategyFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Strategy" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Strategies</SelectItem>
                {Object.values(StrategyType).map(strategy => (
                  <SelectItem key={strategy} value={strategy}>
                    {strategy.replace('_', ' ').toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Bots Grid */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-3 w-1/2 mb-4" />
                    <Skeleton className="h-8 w-full mb-4" />
                    <div className="flex gap-2">
                      <Skeleton className="h-8 flex-1" />
                      <Skeleton className="h-8 flex-1" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredBots.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredBots.map((bot) => (
                <BotControlPanel
                  key={bot.id}
                  bot={bot}
                  onViewDetails={handleViewDetails}
                  onDelete={handleDeleteBot}
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Bot className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">
                  {bots.length === 0 ? 'No Trading Bots Yet' : 'No Bots Match Your Filters'}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {bots.length === 0
                    ? 'Create your first trading bot to start automated trading with professional strategies.'
                    : 'Try adjusting your search query or filters to find the bots you\'re looking for.'
                  }
                </p>
                {bots.length === 0 && (
                  <Button
                    onClick={() => {
                      if (!isExchangeConnected) {
                        toast({
                          title: 'Exchange Connection Required',
                          description: 'You need to connect an exchange account to create trading bots. Please go to Settings to connect your exchange.',
                          variant: 'destructive'
                        });
                        return;
                      }
                      setShowCreateWizard(true);
                    }}
                    className="bg-gradient-to-r from-primary to-primary/80"
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    Create Your First Bot
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="details">
          {selectedBot ? (
            <PerformanceDashboard bot={selectedBot} />
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Select a Bot</h3>
                <p className="text-muted-foreground">
                  Choose a trading bot from the overview to view detailed performance analytics.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Bot Creation Wizard */}
      <BotCreationWizard
        isOpen={showCreateWizard}
        onClose={() => setShowCreateWizard(false)}
        onBotCreated={handleBotCreated}
      />
    </div>
  );
};

export default TradingBotsAdvanced;
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { AlertTriangle, ArrowLeft, Play, Square, Activity, Wifi, WifiOff } from 'lucide-react';
import { agentService } from '@/services/agentService';
import { TradingCycleAnalysisPanel } from '@/components/agents/TradingCycleAnalysisPanel';
import { LiveTradeMonitoringCard } from '@/components/agents/LiveTradeMonitoringCard';
import { TradingEventsPanel } from '@/components/agents/TradingEventsPanel';
import { useFluxTraderRealTime } from '@/hooks/useFluxTraderRealTime';

interface AgentStatus {
  agent_id: string;
  status: 'stopped' | 'starting' | 'running' | 'stopping' | 'error';
  is_running: boolean;
  uptime_seconds: number;
  current_cycle: number;
  max_cycles: number;
  last_activity?: string;
  mcp_connected: boolean;
  binance_connected: boolean;
  groq_connected: boolean;
  error_message?: string;
}

const FluxTraderDashboard: React.FC = () => {
  const { agentId } = useParams<{ agentId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [agent, setAgent] = useState<AgentStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isStarting, setIsStarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);

  // Real-time data hook
  const {
    cycleAnalysis,
    tradeExecutions,
    tradingEvents,
    isConnected,
    connectionStatus,
    subscribeToAgent,
    unsubscribeFromAgent,
    clearData
  } = useFluxTraderRealTime();

  // Load agent data
  useEffect(() => {
    const loadAgentData = async () => {
      if (!agentId) return;
      
      try {
        setIsLoading(true);
        console.log('🔄 Loading agent data for:', agentId);
        const agentData = await agentService.getAgentStatus(agentId);
        console.log('✅ Agent data loaded:', agentData);
        setAgent(agentData);
      } catch (error) {
        console.error('❌ Failed to load agent data:', error);
        toast({
          title: "Error",
          description: "Failed to load agent data",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadAgentData();
  }, [agentId, toast]);

  // Real-time updates - poll agent status every 2 seconds
  useEffect(() => {
    if (!agentId) return;

    const pollAgentStatus = async () => {
      try {
        const agentData = await agentService.getAgentStatus(agentId);
        console.log('🔄 Real-time update:', agentData);
        setAgent(agentData);
      } catch (error) {
        console.error('❌ Failed to poll agent status:', error);
      }
    };

    // Start polling every 2 seconds
    const interval = setInterval(pollAgentStatus, 2000);

    // Cleanup interval on unmount
    return () => clearInterval(interval);
  }, [agentId]);

  // WebSocket subscription for real-time trading data
  useEffect(() => {
    if (!agentId) return;

    const setupWebSocketConnection = async () => {
      try {
        console.log('🔌 [FluxTrader] Setting up WebSocket connection for agent:', agentId);
        console.log('🔌 [FluxTrader] Connection status:', connectionStatus);
        console.log('🔌 [FluxTrader] Is connected:', isConnected);

        // Force subscription with multiple attempts
        const attemptSubscription = async (attempt = 1) => {
          console.log(`🔌 [FluxTrader] Subscription attempt ${attempt} for agent:`, agentId);
          try {
            await subscribeToAgent(agentId);
            console.log(`✅ [FluxTrader] Subscription attempt ${attempt} completed successfully`);
          } catch (error) {
            console.error(`❌ [FluxTrader] Subscription attempt ${attempt} failed:`, error);
            if (attempt < 3) {
              setTimeout(() => attemptSubscription(attempt + 1), 2000);
            }
          }
        };

        // Try immediate subscription
        attemptSubscription();

        // Also try after delays
        setTimeout(() => attemptSubscription(), 2000);
        setTimeout(() => attemptSubscription(), 5000);

      } catch (error) {
        console.error('🔌 [FluxTrader] Failed to setup WebSocket connection:', error);
        toast({
          title: "Connection Error",
          description: "Failed to connect to real-time updates",
          variant: "destructive"
        });
      }
    };

    setupWebSocketConnection();

    // Cleanup on unmount
    return () => {
      if (agentId) {
        unsubscribeFromAgent(agentId).catch(console.error);
      }
    };
  }, [agentId, subscribeToAgent, unsubscribeFromAgent, toast]);

  // Handle start agent
  const handleStartAgent = async () => {
    if (!agentId) return;
    
    try {
      setIsStarting(true);
      console.log('🚀 Starting agent:', agentId);
      await agentService.startAgent(agentId);
      toast({
        title: "Success",
        description: "Agent started successfully",
      });
      // Reload agent data
      const agentData = await agentService.getAgentStatus(agentId);
      setAgent(agentData);
    } catch (error) {
      console.error('❌ Failed to start agent:', error);
      toast({
        title: "Error",
        description: "Failed to start agent",
        variant: "destructive"
      });
    } finally {
      setIsStarting(false);
    }
  };

  // Handle stop agent
  const handleStopAgent = async () => {
    if (!agentId) return;
    
    try {
      setIsStopping(true);
      console.log('🛑 Stopping agent:', agentId);
      await agentService.stopAgent(agentId);
      toast({
        title: "Success",
        description: "Agent stopped successfully",
      });
      // Reload agent data
      const agentData = await agentService.getAgentStatus(agentId);
      setAgent(agentData);
    } catch (error) {
      console.error('❌ Failed to stop agent:', error);
      toast({
        title: "Error",
        description: "Failed to stop agent",
        variant: "destructive"
      });
    } finally {
      setIsStopping(false);
    }
  };

  if (!agentId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Invalid Agent ID</h2>
          <p className="text-muted-foreground mb-4">No agent ID provided in the URL.</p>
          <Button onClick={() => navigate('/dashboard/bots')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Bots
          </Button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="ml-4">Loading FluxTrader agent data...</p>
      </div>
    );
  }

  if (!agent) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => navigate('/dashboard/bots')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Bots
          </Button>
        </div>
        <Card className="glass-card">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Agent Not Found</h3>
            <p className="text-muted-foreground">The requested FluxTrader agent could not be found.</p>
            <p className="text-sm text-gray-500 mt-2">Agent ID: {agentId}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isRunning = agent.status === 'running' || agent.is_running;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => navigate('/dashboard/bots')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Bots
          </Button>
          <div>
            <h1 className="text-2xl font-bold">FluxTrader Dashboard</h1>
            <p className="text-muted-foreground">Agent ID: {agentId}</p>
          </div>
        </div>
        
        {/* Start/Stop Controls */}
        <div className="flex gap-2">
          {isRunning ? (
            <Button
              onClick={handleStopAgent}
              disabled={isStarting || isStopping}
              variant="destructive"
              size="sm"
              className="flex items-center gap-2"
            >
              <Square className="h-4 w-4" />
              {isStopping ? 'Stopping...' : 'Stop Trading'}
            </Button>
          ) : (
            <Button
              onClick={handleStartAgent}
              disabled={isStarting || isStopping}
              size="sm"
              className="flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              {isStarting ? 'Starting...' : 'Start Trading'}
            </Button>
          )}
        </div>
      </div>

      {/* Agent Status */}
      <Card className="glass-card">
        <CardContent className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Status</p>
              <p className="font-semibold">
                <span className={`inline-flex items-center gap-2 ${
                  isRunning ? 'text-green-400' : 'text-gray-400'
                }`}>
                  <div className={`w-2 h-2 rounded-full ${
                    isRunning ? 'bg-green-500' : 'bg-gray-400'
                  }`} />
                  {isRunning ? 'Running' : 'Stopped'}
                </span>
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Cycle</p>
              <p className="font-semibold text-foreground">{agent.current_cycle || 0}/{agent.max_cycles || 100}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Uptime</p>
              <p className="font-semibold text-foreground">{agent.uptime_seconds || 0}s</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Last Activity</p>
              <p className="font-semibold text-xs text-foreground">
                {agent.last_activity ? new Date(agent.last_activity).toLocaleTimeString() : 'N/A'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Connection Status */}
      <Card className="glass-card">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-foreground">Connection Status</h3>
          <div className="grid grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${agent.mcp_connected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm text-foreground">MCP Server</span>
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${agent.binance_connected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm text-foreground">Binance API</span>
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${agent.groq_connected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm text-foreground">Groq AI</span>
            </div>
            <div className="flex items-center gap-2">
              {isConnected ? (
                <Wifi className="w-4 h-4 text-green-500" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-500" />
              )}
              <span className="text-sm text-foreground">Real-time Data</span>
              <span className={`text-xs px-2 py-1 rounded ${
                connectionStatus === 'connected' ? 'bg-green-500/20 text-green-400 border border-green-500/30' :
                connectionStatus === 'connecting' ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30' :
                connectionStatus === 'error' ? 'bg-red-500/20 text-red-400 border border-red-500/30' :
                'bg-gray-500/20 text-gray-400 border border-gray-500/30'
              }`}>
                {connectionStatus}
              </span>
              <Button
                size="sm"
                variant="outline"
                className="glass-card border-primary/30 hover:border-primary/50"
                onClick={() => {
                  console.log('🔌 [FluxTrader] Manual subscription attempt for agent:', agentId);
                  if (agentId) {
                    subscribeToAgent(agentId).then(() => {
                      console.log('🔌 [FluxTrader] Manual subscription completed');
                    }).catch(error => {
                      console.error('🔌 [FluxTrader] Manual subscription failed:', error);
                    });
                  }
                }}
              >
                Subscribe
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Real-time Trading Cycle Analysis */}
      <TradingCycleAnalysisPanel
        cycleData={cycleAnalysis}
        className="w-full"
      />

      {/* Live Trade Monitoring */}
      <LiveTradeMonitoringCard
        trades={tradeExecutions}
        className="w-full"
      />

      {/* Trading Events Panel */}
      <TradingEventsPanel
        events={tradingEvents}
        className="w-full"
      />

      {/* Raw Agent Data for Debugging */}
      <Card className="glass-card">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-foreground">Debug Info</h3>
          <pre className="text-xs bg-card/50 border border-border/30 p-4 rounded overflow-auto text-foreground">
            {JSON.stringify(agent, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
};

export default FluxTraderDashboard;

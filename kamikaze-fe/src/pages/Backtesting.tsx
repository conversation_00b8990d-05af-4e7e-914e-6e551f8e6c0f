import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { BacktestingConfig } from '@/components/backtesting/BacktestingConfig';
import { BacktestingResults } from '@/components/backtesting/BacktestingResults';
import { BacktestingHistory } from '@/components/backtesting/BacktestingHistory';
import { BacktestingState, BacktestingResults as BacktestResults } from '@/types/backtesting';
import {
  Activity,
  Play,
  Square,
  Download,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  CheckCir<PERSON>,
  Clock,
  BarChart3
} from 'lucide-react';

const Backtesting: React.FC = () => {
  const [activeTab, setActiveTab] = useState('configure');
  const [backtestingState, setBacktestingState] = useState<BacktestingState>({
    results: [],
    isRunning: false
  });

  const [currentConfig, setCurrentConfig] = useState<Partial<BacktestingConfig>>({
    initialCapital: 10000,
    commission: 0.001,
    slippage: 0.001,
    riskManagement: {
      positionSizing: 'percentage',
      maxPositionSize: 10,
      maxDrawdown: 20
    }
  });

  const handleStartBacktest = async (config: BacktestingConfig) => {
    setBacktestingState(prev => ({ ...prev, isRunning: true, error: undefined }));
    setActiveTab('results');

    try {
      // Simulate backtesting process
      const newBacktest: BacktestResults = {
        id: `backtest_${Date.now()}`,
        config,
        startTime: new Date(),
        endTime: new Date(),
        status: 'running',
        progress: 0,
        trades: [],
        metrics: {
          totalReturn: 0,
          totalReturnPercent: 0,
          annualizedReturn: 0,
          sharpeRatio: 0,
          sortinoRatio: 0,
          maxDrawdown: 0,
          maxDrawdownPercent: 0,
          winRate: 0,
          profitFactor: 0,
          totalTrades: 0,
          winningTrades: 0,
          losingTrades: 0,
          averageWin: 0,
          averageLoss: 0,
          largestWin: 0,
          largestLoss: 0,
          averageTradeDuration: 0,
          volatility: 0,
          calmarRatio: 0,
          recoveryFactor: 0,
          payoffRatio: 0,
          expectedValue: 0
        },
        equity: [],
        monthlyReturns: []
      };

      setBacktestingState(prev => ({
        ...prev,
        currentBacktest: newBacktest,
        results: [newBacktest, ...prev.results]
      }));

      // Simulate progress updates
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 500));
        setBacktestingState(prev => ({
          ...prev,
          currentBacktest: prev.currentBacktest ? {
            ...prev.currentBacktest,
            progress: i,
            status: i === 100 ? 'completed' : 'running'
          } : undefined
        }));
      }

      // Generate mock results
      const completedBacktest: BacktestResults = {
        ...newBacktest,
        status: 'completed',
        progress: 100,
        endTime: new Date(),
        metrics: {
          totalReturn: 2847.32,
          totalReturnPercent: 28.47,
          annualizedReturn: 15.23,
          sharpeRatio: 1.42,
          sortinoRatio: 1.89,
          maxDrawdown: -1247.89,
          maxDrawdownPercent: -12.48,
          winRate: 64.2,
          profitFactor: 1.85,
          totalTrades: 156,
          winningTrades: 100,
          losingTrades: 56,
          averageWin: 89.34,
          averageLoss: -48.21,
          largestWin: 456.78,
          largestLoss: -234.56,
          averageTradeDuration: 4.2,
          volatility: 18.7,
          calmarRatio: 1.22,
          recoveryFactor: 2.28,
          payoffRatio: 1.85,
          expectedValue: 18.25
        },
        equity: generateMockEquityCurve(),
        monthlyReturns: generateMockMonthlyReturns()
      };

      setBacktestingState(prev => ({
        ...prev,
        currentBacktest: completedBacktest,
        isRunning: false,
        results: [completedBacktest, ...prev.results.slice(1)]
      }));

    } catch (error) {
      setBacktestingState(prev => ({
        ...prev,
        isRunning: false,
        error: 'Backtesting failed. Please try again.'
      }));
    }
  };

  const handleStopBacktest = () => {
    setBacktestingState(prev => ({
      ...prev,
      isRunning: false,
      currentBacktest: prev.currentBacktest ? {
        ...prev.currentBacktest,
        status: 'cancelled'
      } : undefined
    }));
  };

  const generateMockEquityCurve = () => {
    const data = [];
    let value = 10000;
    const startDate = new Date(currentConfig.startDate || new Date());
    
    for (let i = 0; i < 100; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      
      value += (Math.random() - 0.45) * 100;
      const drawdown = Math.max(0, (12000 - value) / 12000 * 100);
      
      data.push({
        timestamp: date,
        value: Math.max(value, 8000),
        drawdown: -drawdown
      });
    }
    
    return data;
  };

  const generateMockMonthlyReturns = () => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months.map(month => ({
      month,
      return: (Math.random() - 0.4) * 10
    }));
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Clock className="h-4 w-4 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-success" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-destructive" />;
      case 'cancelled':
        return <Square className="h-4 w-4 text-muted-foreground" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-blue-500/10 text-blue-500 border-blue-500/30';
      case 'completed':
        return 'bg-success/10 text-success border-success/30';
      case 'failed':
        return 'bg-destructive/10 text-destructive border-destructive/30';
      case 'cancelled':
        return 'bg-muted/10 text-muted-foreground border-muted/30';
      default:
        return 'bg-muted/10 text-muted-foreground border-muted/30';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold text-foreground flex items-center gap-3">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-chart-2/20 to-chart-2/30 border border-chart-2/30 flex items-center justify-center">
              <Activity className="h-5 w-5 text-chart-2" />
            </div>
            Strategy Backtesting
          </h1>
          <p className="text-muted-foreground">
            Test your trading strategies against historical market data
          </p>
        </div>

        <div className="flex items-center gap-3">
          {backtestingState.currentBacktest && (
            <Badge className={getStatusColor(backtestingState.currentBacktest.status)}>
              {getStatusIcon(backtestingState.currentBacktest.status)}
              {backtestingState.currentBacktest.status.charAt(0).toUpperCase() + 
               backtestingState.currentBacktest.status.slice(1)}
            </Badge>
          )}
          
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Results
          </Button>
          
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {backtestingState.error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{backtestingState.error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      {backtestingState.isRunning && backtestingState.currentBacktest && (
        <Card className="glass-card">
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Running Backtest</p>
                  <p className="text-xs text-muted-foreground">
                    Processing {backtestingState.currentBacktest.config.tradingPairs?.join(', ')} • 
                    {backtestingState.currentBacktest.config.strategy?.name}
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleStopBacktest}
                  className="text-destructive hover:text-destructive"
                >
                  <Square className="h-4 w-4 mr-2" />
                  Stop
                </Button>
              </div>
              <Progress value={backtestingState.currentBacktest.progress} className="h-2" />
              <p className="text-xs text-muted-foreground text-center">
                {backtestingState.currentBacktest.progress}% complete
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="glass-card p-1">
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Results
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="configure" className="space-y-6">
          <BacktestingConfig
            config={currentConfig}
            onConfigChange={setCurrentConfig}
            onStartBacktest={handleStartBacktest}
            isRunning={backtestingState.isRunning}
          />
        </TabsContent>

        <TabsContent value="results" className="space-y-6">
          <BacktestingResults
            results={backtestingState.currentBacktest}
            isLoading={backtestingState.isRunning}
          />
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <BacktestingHistory
            results={backtestingState.results}
            onSelectResult={(result) => {
              setBacktestingState(prev => ({ ...prev, currentBacktest: result }));
              setActiveTab('results');
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Backtesting;

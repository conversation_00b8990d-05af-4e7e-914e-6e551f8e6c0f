/**
 * Trading Bots Page
 * AI-powered bots and specialized trading agents
 * This page focuses on bot execution and management
 */

import { <PERSON><PERSON>, Play, Pause, Settings, Plus, TrendingUp, Target, Activity } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FluxTraderBotCard } from "@/components/dashboard/FluxTraderBotCard";

const TradingBots = () => {
  const bots = [
    {
      id: 1,
      name: "Conservative Growth",
      strategy: "DCA + Grid Trading",
      status: "active",
      profit: 1247.85,
      profitPercent: 8.3,
      trades: 156,
      successRate: 78,
      risk: "Low",
      description: "Long-term growth strategy with minimal risk. Perfect for steady portfolio growth.",
      balance: 15000,
      maxDrawdown: 2.5
    },
    {
      id: 2,
      name: "Aggressive Scalper",
      strategy: "Momentum + RSI",
      status: "active",
      profit: 2156.42,
      profitPercent: 15.7,
      trades: 324,
      successRate: 65,
      risk: "High",
      description: "High-frequency trading with momentum indicators for maximum profit potential.",
      balance: 25000,
      maxDrawdown: 12.8
    },
    {
      id: 3,
      name: "Balanced Trader",
      strategy: "Mean Reversion",
      status: "paused",
      profit: 856.23,
      profitPercent: 5.2,
      trades: 89,
      successRate: 72,
      risk: "Medium",
      description: "Balanced approach combining multiple strategies for optimal risk-reward ratio.",
      balance: 12000,
      maxDrawdown: 5.7
    }
  ];

  const botTemplates = [
    { name: "DCA Master", strategy: "Dollar Cost Averaging", risk: "Low", expectedReturn: "8-12%" },
    { name: "Grid Trader Pro", strategy: "Grid Trading", risk: "Medium", expectedReturn: "12-18%" },
    { name: "Momentum Hunter", strategy: "Momentum Trading", risk: "High", expectedReturn: "20-35%" },
    { name: "Arbitrage Expert", strategy: "Cross-Exchange", risk: "Low", expectedReturn: "5-8%" }
  ];

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "Low": return "text-profit bg-profit/10 border-profit/20";
      case "Medium": return "text-warning bg-warning/10 border-warning/20";
      case "High": return "text-destructive bg-destructive/10 border-destructive/20";
      default: return "text-neutral bg-neutral/10 border-neutral/20";
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Trading Bots</h1>
          <p className="text-muted-foreground">AI-powered bots and specialized trading agents for automated execution</p>
        </div>
        <Button className="btn-premium">
          <Plus className="w-4 h-4 mr-2" />
          Deploy New Bot
        </Button>
      </div>

      <Tabs defaultValue="active" className="space-y-6">
        <TabsList className="glass-container border border-card-border/60">
          <TabsTrigger value="active" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Active Bots ({bots.filter(b => b.status === 'active').length})
          </TabsTrigger>
          <TabsTrigger value="templates" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Bot Templates
          </TabsTrigger>
          <TabsTrigger value="performance" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Performance
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {/* FluxTrader AI Agent - Always show first */}
            <FluxTraderBotCard />

            {bots.map((bot) => (
              <Card key={bot.id} className="glass-card hover:shadow-elevated transition-all duration-300">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                        <Bot className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-lg text-foreground">{bot.name}</CardTitle>
                        <p className="text-sm text-muted-foreground">{bot.strategy}</p>
                      </div>
                    </div>
                    <Badge variant="secondary" className={getRiskColor(bot.risk)}>
                      {bot.risk} Risk
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{bot.description}</p>
                  
                  {/* Performance Metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-profit">+${bot.profit.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">Total Profit</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground">{bot.successRate}%</div>
                      <div className="text-sm text-muted-foreground">Success Rate</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Balance</span>
                      <span className="text-foreground">${bot.balance.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Total Trades</span>
                      <span className="text-foreground">{bot.trades}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Max Drawdown</span>
                      <span className="text-foreground">{bot.maxDrawdown}%</span>
                    </div>
                  </div>

                  <Progress value={bot.successRate} className="h-2" />

                  <div className="flex gap-2">
                    {bot.status === "active" ? (
                      <Button size="sm" variant="outline" className="flex-1">
                        <Pause className="w-4 h-4 mr-2" />
                        Pause
                      </Button>
                    ) : (
                      <Button size="sm" className="flex-1 bg-profit hover:bg-profit/90 text-white">
                        <Play className="w-4 h-4 mr-2" />
                        Start
                      </Button>
                    )}
                    <Button size="sm" variant="outline">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {botTemplates.map((template, index) => (
              <Card key={index} className="glass-card hover:shadow-elevated transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-card rounded-lg flex items-center justify-center">
                        <Target className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-foreground">{template.name}</h3>
                        <p className="text-sm text-muted-foreground">{template.strategy}</p>
                      </div>
                    </div>
                    <Badge variant="secondary" className={getRiskColor(template.risk)}>
                      {template.risk}
                    </Badge>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Expected Return</span>
                      <span className="text-sm font-medium text-profit">{template.expectedReturn}</span>
                    </div>
                    
                    <Button className="w-full bg-gradient-primary text-white">
                      Use Template
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="glass-card hover:shadow-elevated transition-all duration-300">
              <CardContent className="p-6 text-center">
                <TrendingUp className="w-8 h-8 text-success mx-auto mb-2" />
                <div className="text-2xl font-bold text-foreground">$4,260.50</div>
                <div className="text-sm text-muted-foreground">Total Profit</div>
                <div className="text-sm text-success">+18.7% this month</div>
              </CardContent>
            </Card>

            <Card className="glass-card hover:shadow-elevated transition-all duration-300">
              <CardContent className="p-6 text-center">
                <Activity className="w-8 h-8 text-primary mx-auto mb-2" />
                <div className="text-2xl font-bold text-foreground">569</div>
                <div className="text-sm text-muted-foreground">Total Trades</div>
                <div className="text-sm text-foreground">72% success rate</div>
              </CardContent>
            </Card>
            
            <Card className="glass-card hover:shadow-elevated transition-all duration-300">
              <CardContent className="p-6 text-center">
                <Bot className="w-8 h-8 text-primary mx-auto mb-2" />
                <div className="text-2xl font-bold text-foreground">3</div>
                <div className="text-sm text-muted-foreground">Active Bots</div>
                <div className="text-sm text-success">2 profitable</div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TradingBots;
import { Bar<PERSON>hart3 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const Analytics = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Analytics</h1>
          <p className="text-muted-foreground">Advanced trading analytics and insights</p>
        </div>
      </div>

      <Card className="bg-card shadow-card border-border">
        <CardContent className="p-12 text-center">
          <BarChart3 className="w-24 h-24 text-primary mx-auto mb-6" />
          <h2 className="text-2xl font-bold text-foreground mb-4">Advanced Analytics Dashboard</h2>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Get detailed insights into your trading performance, risk metrics, market analysis, 
            and AI recommendations with our comprehensive analytics suite.
          </p>
          <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20 text-lg px-4 py-2">
            Coming Soon
          </Badge>
        </CardContent>
      </Card>
    </div>
  );
};

export default Analytics;
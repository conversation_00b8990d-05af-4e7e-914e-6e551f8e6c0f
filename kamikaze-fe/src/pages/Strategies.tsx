import { Target, TrendingUp, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Play, AlertTriangle } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

const Strategies = () => {
  const strategies = [
    {
      id: 1,
      name: "DCA Premium",
      type: "Dollar Cost Averaging",
      description: "Systematic investment strategy with risk management",
      performance: 12.5,
      winRate: 78,
      maxDrawdown: 3.2,
      totalTrades: 245,
      avgHoldTime: "12h",
      risk: "Low",
      status: "active",
      assets: ["BTC", "ETH"],
      allocation: { BTC: 60, ETH: 40 }
    },
    {
      id: 2,
      name: "Grid Master Pro",
      type: "Grid Trading",
      description: "Advanced grid strategy with dynamic adjustments",
      performance: 18.7,
      winRate: 65,
      maxDrawdown: 8.5,
      totalTrades: 512,
      avgHoldTime: "4h",
      risk: "Medium",
      status: "active",
      assets: ["BTC", "ETH", "ADA"],
      allocation: { BTC: 50, ETH: 30, ADA: 20 }
    },
    {
      id: 3,
      name: "Momentum Scalper",
      type: "Momentum Trading",
      description: "High-frequency momentum-based strategy",
      performance: 25.3,
      winRate: 58,
      maxDrawdown: 15.2,
      totalTrades: 1248,
      avgHoldTime: "45m",
      risk: "High",
      status: "paused",
      assets: ["BTC", "ETH", "SOL", "ADA"],
      allocation: { BTC: 40, ETH: 25, SOL: 20, ADA: 15 }
    }
  ];

  const strategyTemplates = [
    {
      name: "Conservative DCA",
      type: "Dollar Cost Averaging",
      description: "Low-risk, steady growth strategy",
      expectedReturn: "8-12%",
      risk: "Low",
      timeframe: "Long-term",
      features: ["Stop-loss protection", "Rebalancing", "Risk management"]
    },
    {
      name: "Aggressive Momentum",
      type: "Momentum Trading",
      description: "High-risk, high-reward momentum strategy",
      expectedReturn: "20-35%",
      risk: "High",
      timeframe: "Short-term",
      features: ["RSI signals", "MACD crossovers", "Volume analysis"]
    },
    {
      name: "Mean Reversion Pro",
      type: "Mean Reversion",
      description: "Profit from price corrections",
      expectedReturn: "12-18%",
      risk: "Medium",
      timeframe: "Medium-term",
      features: ["Bollinger Bands", "Oversold/Overbought", "Support/Resistance"]
    }
  ];

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "Low": return "text-profit bg-profit/10 border-profit/20";
      case "Medium": return "text-warning bg-warning/10 border-warning/20";
      case "High": return "text-destructive bg-destructive/10 border-destructive/20";
      default: return "text-neutral bg-neutral/10 border-neutral/20";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Trading Strategies</h1>
          <p className="text-muted-foreground">Create and manage your AI trading strategies</p>
        </div>
        <Button className="bg-gradient-primary text-white">
          <Target className="w-4 h-4 mr-2" />
          Create Strategy
        </Button>
      </div>

      <Tabs defaultValue="active" className="space-y-6">
        <TabsList className="bg-card border border-border">
          <TabsTrigger value="active" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            My Strategies
          </TabsTrigger>
          <TabsTrigger value="templates" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Strategy Templates
          </TabsTrigger>
          <TabsTrigger value="backtest" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Backtesting
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-6">
          <div className="space-y-6">
            {strategies.map((strategy) => (
              <Card key={strategy.id} className="bg-card shadow-card border-border">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                        <Target className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-xl text-foreground">{strategy.name}</CardTitle>
                        <p className="text-sm text-muted-foreground">{strategy.type}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className={getRiskColor(strategy.risk)}>
                        {strategy.risk} Risk
                      </Badge>
                      <Badge variant="secondary" className={
                        strategy.status === "active" 
                          ? "text-profit bg-profit/10 border-profit/20" 
                          : "text-muted-foreground bg-muted/10 border-muted/20"
                      }>
                        {strategy.status}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <p className="text-muted-foreground">{strategy.description}</p>
                  
                  {/* Performance Metrics */}
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-profit">+{strategy.performance}%</div>
                      <div className="text-sm text-muted-foreground">Performance</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground">{strategy.winRate}%</div>
                      <div className="text-sm text-muted-foreground">Win Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground">{strategy.maxDrawdown}%</div>
                      <div className="text-sm text-muted-foreground">Max Drawdown</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground">{strategy.totalTrades}</div>
                      <div className="text-sm text-muted-foreground">Total Trades</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground">{strategy.avgHoldTime}</div>
                      <div className="text-sm text-muted-foreground">Avg Hold</div>
                    </div>
                  </div>

                  {/* Asset Allocation */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-foreground">Asset Allocation</h4>
                    <div className="space-y-2">
                      {Object.entries(strategy.allocation).map(([asset, percentage]) => (
                        <div key={asset} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-6 h-6 bg-gradient-primary rounded text-white text-xs flex items-center justify-center">
                              {asset}
                            </div>
                            <span className="text-sm text-foreground">{asset}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-20 h-2 bg-secondary rounded-full">
                              <div 
                                className="h-2 bg-primary rounded-full transition-all duration-500"
                                style={{ width: `${percentage}%` }}
                              />
                            </div>
                            <span className="text-sm text-muted-foreground w-8 text-right">{percentage}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    {strategy.status === "active" ? (
                      <>
                        <Button size="sm" variant="outline" className="flex-1">
                          <Settings className="w-4 h-4 mr-2" />
                          Configure
                        </Button>
                        <Button size="sm" variant="outline">
                          <BarChart3 className="w-4 h-4 mr-2" />
                          Analytics
                        </Button>
                      </>
                    ) : (
                      <Button size="sm" className="flex-1 bg-profit hover:bg-profit/90 text-white">
                        <Play className="w-4 h-4 mr-2" />
                        Activate
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {strategyTemplates.map((template, index) => (
              <Card key={index} className="bg-card shadow-card border-border">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-card rounded-lg flex items-center justify-center">
                          <Target className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-foreground">{template.name}</h3>
                          <p className="text-sm text-muted-foreground">{template.type}</p>
                        </div>
                      </div>
                      <Badge variant="secondary" className={getRiskColor(template.risk)}>
                        {template.risk}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-muted-foreground">{template.description}</p>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Expected Return</span>
                        <span className="text-sm font-medium text-profit">{template.expectedReturn}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Timeframe</span>
                        <span className="text-sm font-medium text-foreground">{template.timeframe}</span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-foreground">Features</h4>
                      <div className="space-y-1">
                        {template.features.map((feature, idx) => (
                          <div key={idx} className="text-xs text-muted-foreground">
                            ✓ {feature}
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <Button className="w-full bg-gradient-primary text-white">
                      Use Template
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="backtest" className="space-y-6">
          <Card className="bg-card shadow-card border-border">
            <CardContent className="p-6 text-center">
              <BarChart3 className="w-16 h-16 text-primary mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">Strategy Backtesting</h3>
              <p className="text-muted-foreground mb-6">Test your strategies against historical data to optimize performance</p>
              <Button className="bg-gradient-primary text-white">
                Start Backtest
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Strategies;
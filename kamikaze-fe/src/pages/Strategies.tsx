/**
 * Strategies Page
 * Professional trading strategies and bot management
 * Combines professional quantitative strategies with AI-powered bots
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { useTradingBots } from '@/hooks/useTradingBots';
import { useExchange } from '@/contexts/ExchangeContext';

// Import our professional trading bot components
import { BotCreationWizard } from '@/components/trading-bots/BotManager/BotCreationWizard';
import { BotControlPanel } from '@/components/trading-bots/BotManager/BotControlPanel';
import { PerformanceDashboard } from '@/components/trading-bots/Performance/PerformanceDashboard';

// Import existing components
import { FluxTraderBotCard } from '@/components/dashboard/FluxTraderBotCard';

import { TradingBot, BotStatus, StrategyType, RiskLevel } from '@/types/tradingBot';
import {
  Bot,
  Plus,
  Search,
  Filter,
  Settings,
  Activity,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Shield,
  Zap,
  AlertTriangle,
  CheckCircle,
  Wifi,
  WifiOff,
  RefreshCw,
  BarChart3,
  Target,
  Clock,
  Sparkles,
  Brain,
  Cpu,
  Play,
  Pause
} from 'lucide-react';

const Strategies = () => {
  const [selectedTab, setSelectedTab] = useState('professional');
  const [selectedBot, setSelectedBot] = useState<TradingBot | null>(null);
  const [showCreateWizard, setShowCreateWizard] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [strategyFilter, setStrategyFilter] = useState<string>('all');

  const { toast } = useToast();
  const { hasConnectedExchange } = useExchange();

  const {
    bots,
    loading,
    error,
    isConnected,
    refreshBots,
    selectBot,
    deleteBot
  } = useTradingBots();

  // Filter professional bots based on search and filters
  const filteredBots = bots.filter(bot => {
    const matchesSearch = bot.config.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         bot.config.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' || bot.status === statusFilter;

    const matchesStrategy = strategyFilter === 'all' ||
                           bot.config.strategy.strategy_type === strategyFilter;

    return matchesSearch && matchesStatus && matchesStrategy;
  });

  // Mock AI-powered bots data (in real implementation, this would come from API)
  const aiPoweredBots = [
    {
      id: 'flux-trader',
      name: 'FluxTrader AI',
      type: 'AI Agent',
      description: 'Advanced AI-powered trading agent with market analysis and autonomous decision making',
      performance: 24.8,
      winRate: 73,
      maxDrawdown: 8.2,
      totalTrades: 342,
      avgHoldTime: '6h',
      risk: 'Medium',
      status: 'active',
      assets: ['BTC', 'ETH', 'SOL'],
      features: ['Market Analysis', 'Sentiment Analysis', 'Risk Management', 'Portfolio Optimization']
    }
  ];

  // Calculate summary statistics for professional bots
  const professionalStats = {
    total: bots.length,
    running: bots.filter(bot => bot.status === BotStatus.RUNNING).length,
    paused: bots.filter(bot => bot.status === BotStatus.PAUSED).length,
    stopped: bots.filter(bot => bot.status === BotStatus.STOPPED).length,
    error: bots.filter(bot => bot.status === BotStatus.ERROR).length,
    totalPnL: bots.reduce((sum, bot) => sum + bot.metrics.performance.total_return, 0),
    totalTrades: bots.reduce((sum, bot) => sum + bot.metrics.performance.total_trades, 0),
    avgWinRate: bots.length > 0
      ? bots.reduce((sum, bot) => sum + bot.metrics.performance.win_rate, 0) / bots.length
      : 0
  };

  const handleBotCreated = (bot: TradingBot) => {
    toast({
      title: 'Success',
      description: `Trading strategy "${bot.config.name}" created successfully!`
    });
    refreshBots();
  };

  const handleDeleteBot = async (bot: TradingBot) => {
    if (window.confirm(`Are you sure you want to delete "${bot.config.name}"? This action cannot be undone.`)) {
      const success = await deleteBot(bot.id);
      if (success && selectedBot?.id === bot.id) {
        setSelectedBot(null);
      }
    }
  };

  const handleViewDetails = (bot: TradingBot) => {
    setSelectedBot(bot);
    setSelectedTab('details');
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'Low': return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900';
      case 'Medium': return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900';
      case 'High': return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900';
      default: return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900';
    }
  };

  // Check if user has connected exchange (but don't block the page)
  const isExchangeConnected = hasConnectedExchange();


  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Trading Strategies</h1>
          <p className="text-muted-foreground">
            Professional trading strategies and AI-powered bots for automated trading
          </p>
        </div>

        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {isConnected ? (
              <div className="flex items-center gap-2 text-green-600">
                <Wifi className="h-4 w-4" />
                <span className="text-sm">Connected</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-red-600">
                <WifiOff className="h-4 w-4" />
                <span className="text-sm">Disconnected</span>
              </div>
            )}
          </div>

          <Button
            variant="outline"
            onClick={refreshBots}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Button
            onClick={() => {
              if (!isExchangeConnected) {
                toast({
                  title: 'Exchange Connection Required',
                  description: 'You need to connect an exchange account to create trading strategies. Please go to Settings to connect your exchange.',
                  variant: 'destructive'
                });
                return;
              }
              setShowCreateWizard(true);
            }}
            className="bg-gradient-to-r from-primary to-primary/80"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Strategy
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="professional">Professional Strategies</TabsTrigger>
          <TabsTrigger value="ai-powered">AI-Powered Bots</TabsTrigger>
          <TabsTrigger value="overview">Portfolio Overview</TabsTrigger>
          <TabsTrigger value="details" disabled={!selectedBot}>
            {selectedBot ? `${selectedBot.config.name} Details` : 'Strategy Details'}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="professional" className="space-y-6">
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search strategies by name or description..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value={BotStatus.RUNNING}>Running</SelectItem>
                <SelectItem value={BotStatus.PAUSED}>Paused</SelectItem>
                <SelectItem value={BotStatus.STOPPED}>Stopped</SelectItem>
                <SelectItem value={BotStatus.ERROR}>Error</SelectItem>
              </SelectContent>
            </Select>

            <Select value={strategyFilter} onValueChange={setStrategyFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Strategy" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Strategies</SelectItem>
                {Object.values(StrategyType).map(strategy => (
                  <SelectItem key={strategy} value={strategy}>
                    {strategy.replace('_', ' ').toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Professional Strategies Grid */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="animate-pulse space-y-4">
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                      <div className="h-8 bg-muted rounded"></div>
                      <div className="flex gap-2">
                        <div className="h-8 bg-muted rounded flex-1"></div>
                        <div className="h-8 bg-muted rounded flex-1"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredBots.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredBots.map((bot) => (
                <BotControlPanel
                  key={bot.id}
                  bot={bot}
                  onViewDetails={handleViewDetails}
                  onDelete={handleDeleteBot}
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Target className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">
                  {bots.length === 0 ? 'No Professional Strategies Yet' : 'No Strategies Match Your Filters'}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {bots.length === 0
                    ? 'Create your first professional trading strategy with advanced quantitative techniques.'
                    : 'Try adjusting your search query or filters to find the strategies you\'re looking for.'
                  }
                </p>
                {bots.length === 0 && (
                  <Button
                    onClick={() => {
                      if (!isExchangeConnected) {
                        toast({
                          title: 'Exchange Connection Required',
                          description: 'You need to connect an exchange account to create trading strategies. Please go to Settings to connect your exchange.',
                          variant: 'destructive'
                        });
                        return;
                      }
                      setShowCreateWizard(true);
                    }}
                    className="bg-gradient-to-r from-primary to-primary/80"
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    Create Your First Strategy
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="ai-powered" className="space-y-6">
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">AI-Powered Trading Bots</h2>
            <p className="text-muted-foreground">
              Advanced AI agents with autonomous decision-making capabilities and market analysis
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {/* FluxTrader AI Agent - Always show first */}
            <FluxTraderBotCard />

            {/* Additional AI-powered bots */}
            {aiPoweredBots.map((bot) => (
              <Card key={bot.id} className="hover:shadow-lg transition-all duration-300">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                        <Brain className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{bot.name}</CardTitle>
                        <p className="text-sm text-muted-foreground">{bot.type}</p>
                      </div>
                    </div>
                    <Badge className={getRiskColor(bot.risk)}>
                      {bot.risk} Risk
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{bot.description}</p>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">+{bot.performance}%</div>
                      <div className="text-sm text-muted-foreground">Performance</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{bot.winRate}%</div>
                      <div className="text-sm text-muted-foreground">Win Rate</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="text-sm font-medium">AI Features:</div>
                    <div className="flex flex-wrap gap-1">
                      {bot.features.map((feature, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <Progress value={bot.winRate} className="h-2" />

                  <div className="flex gap-2">
                    {bot.status === "active" ? (
                      <Button size="sm" variant="outline" className="flex-1">
                        <Pause className="w-4 h-4 mr-2" />
                        Pause
                      </Button>
                    ) : (
                      <Button size="sm" className="flex-1 bg-gradient-to-r from-purple-500 to-blue-500 text-white">
                        <Play className="w-4 h-4 mr-2" />
                        Start
                      </Button>
                    )}
                    <Button size="sm" variant="outline">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6 text-center">
                <Bot className="h-8 w-8 mx-auto text-primary mb-2" />
                <div className="text-2xl font-bold">{professionalStats.total}</div>
                <div className="text-sm text-muted-foreground">Professional Strategies</div>
                <div className="text-xs text-green-600">{professionalStats.running} running</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <DollarSign className="h-8 w-8 mx-auto text-green-600 mb-2" />
                <div className="text-2xl font-bold text-green-600">
                  {professionalStats.totalPnL >= 0 ? '+' : ''}${professionalStats.totalPnL.toFixed(2)}
                </div>
                <div className="text-sm text-muted-foreground">Total P&L</div>
                <div className="text-xs text-muted-foreground">All strategies</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <Activity className="h-8 w-8 mx-auto text-blue-600 mb-2" />
                <div className="text-2xl font-bold">{professionalStats.totalTrades}</div>
                <div className="text-sm text-muted-foreground">Total Trades</div>
                <div className="text-xs text-blue-600">{professionalStats.avgWinRate.toFixed(1)}% avg win rate</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <Brain className="h-8 w-8 mx-auto text-purple-600 mb-2" />
                <div className="text-2xl font-bold">{aiPoweredBots.length}</div>
                <div className="text-sm text-muted-foreground">AI-Powered Bots</div>
                <div className="text-xs text-purple-600">Advanced AI agents</div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {selectedBot && (
          <TabsContent value="details" className="space-y-6">
            <PerformanceDashboard bot={selectedBot} />
          </TabsContent>
        )}
      </Tabs>

      {/* Bot Creation Wizard Modal */}
      {showCreateWizard && (
        <BotCreationWizard
          onClose={() => setShowCreateWizard(false)}
          onBotCreated={handleBotCreated}
        />
      )}
    </div>
  );
};

export default Strategies;
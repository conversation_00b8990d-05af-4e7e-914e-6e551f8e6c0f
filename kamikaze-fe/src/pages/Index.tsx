import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import { TopNavbar } from "@/components/TopNavbar";
import { DashboardGrid } from "@/components/DashboardGrid";
import { Toaster } from "@/components/ui/toaster";

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      <SidebarProvider defaultOpen={true}>
        <div className="flex min-h-screen w-full">
          <AppSidebar />
          <div className="flex-1 flex flex-col">
            <TopNavbar />
            <main className="flex-1 p-6">
              <DashboardGrid />
            </main>
          </div>
        </div>
      </SidebarProvider>
      <Toaster />
    </div>
  );
};

export default Index;
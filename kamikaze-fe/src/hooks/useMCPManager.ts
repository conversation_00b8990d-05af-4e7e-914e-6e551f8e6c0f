import { useCallback, useRef, useEffect } from 'react';
import { mcpClient } from '@/services/mcpClient';
import { ExchangeCredentials, MCPResponse, BinanceAccountInfo } from '@/types/exchange';

interface MCPManagerHook {
  connectToMCP: () => Promise<boolean>;
  disconnectFromMCP: () => void;
  testBinanceConnection: (credentials: ExchangeCredentials) => Promise<MCPResponse>;
  getBinanceAccountInfo: (credentials: ExchangeCredentials) => Promise<BinanceAccountInfo | null>;
  getServerStatus: () => Promise<MCPResponse>;
  isConnected: () => boolean;
}

export const useMCPManager = (): MCPManagerHook => {
  const isInitializedRef = useRef(false);

  // Initialize MCP connection on first use
  useEffect(() => {
    const initializeMCP = async () => {
      if (!isInitializedRef.current) {
        try {
          await mcpClient.connect();
          isInitializedRef.current = true;
        } catch (error) {
          console.error('Failed to initialize MCP client:', error);
        }
      }
    };

    initializeMCP();

    // Cleanup on unmount
    return () => {
      mcpClient.disconnect();
      isInitializedRef.current = false;
    };
  }, []);

  const connectToMCP = useCallback(async (): Promise<boolean> => {
    try {
      const connected = await mcpClient.connect();
      if (connected) {
        isInitializedRef.current = true;
      }
      return connected;
    } catch (error) {
      console.error('Failed to connect to MCP server:', error);
      return false;
    }
  }, []);

  const disconnectFromMCP = useCallback(() => {
    mcpClient.disconnect();
    isInitializedRef.current = false;
  }, []);

  const testBinanceConnection = useCallback(async (credentials: ExchangeCredentials): Promise<MCPResponse> => {
    try {
      return await mcpClient.testBinanceConnection(credentials);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, []);

  const getBinanceAccountInfo = useCallback(async (credentials: ExchangeCredentials): Promise<BinanceAccountInfo | null> => {
    try {
      return await mcpClient.getBinanceAccountInfo(credentials);
    } catch (error) {
      console.error('Failed to get Binance account info:', error);
      return null;
    }
  }, []);

  const getServerStatus = useCallback(async (): Promise<MCPResponse> => {
    try {
      return await mcpClient.getServerStatus();
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, []);

  const isConnected = useCallback((): boolean => {
    return mcpClient.isConnected();
  }, []);

  return {
    connectToMCP,
    disconnectFromMCP,
    testBinanceConnection,
    getBinanceAccountInfo,
    getServerStatus,
    isConnected
  };
};

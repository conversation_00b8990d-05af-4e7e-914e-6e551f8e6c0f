/**
 * Dashboard Data Management Hook
 * Manages real-time dashboard data with caching and error handling
 * Includes automatic updates every second for portfolio data
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { dashboardApi, DashboardOverview, QuickStats, PortfolioPerformanceResponse } from '@/services/dashboardApi';
import { websocketClient, WebSocketMessage, PortfolioUpdateData } from '@/services/websocketClient';

interface DashboardState {
  overview: DashboardOverview | null;
  quickStats: QuickStats | null;
  portfolioPerformance: PortfolioPerformanceResponse | null;
  loading: boolean;
  error: string | null;
  lastUpdated: number | null;
  isRealTimeActive: boolean;
  connectionStatus: 'connected' | 'disconnected' | 'connecting' | 'error';
}

interface UseDashboardDataReturn extends DashboardState {
  refreshDashboard: () => Promise<void>;
  refreshQuickStats: () => Promise<void>;
  refreshPortfolioPerformance: (period?: string) => Promise<void>;
  clearError: () => void;
  isStale: boolean;
  startRealTimeUpdates: () => void;
  stopRealTimeUpdates: () => void;
  toggleRealTimeUpdates: () => void;
}

const STALE_TIME = 5 * 60 * 1000; // 5 minutes
const REAL_TIME_INTERVAL = 10000; // 10 seconds for real-time updates (reduced to prevent rate limiting)
const QUICK_REFRESH_INTERVAL = 30000; // 30 seconds for quick stats (reduced to prevent rate limiting)

export function useDashboardData(): UseDashboardDataReturn {
  const [state, setState] = useState<DashboardState>({
    overview: null,
    quickStats: null,
    portfolioPerformance: null,
    loading: false,
    error: null,
    lastUpdated: null,
    isRealTimeActive: false,
    connectionStatus: 'disconnected',
  });

  const mountedRef = useRef(true);
  const realTimeIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const quickStatsIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef(0);

  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
      // Cleanup intervals on unmount
      if (realTimeIntervalRef.current) {
        clearInterval(realTimeIntervalRef.current);
      }
      if (quickStatsIntervalRef.current) {
        clearInterval(quickStatsIntervalRef.current);
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  // Auto-refresh on mount and start real-time updates
  useEffect(() => {
    refreshDashboard();
    startRealTimeUpdates();
    setupWebSocketConnection();

    return () => {
      // Cleanup WebSocket on unmount
      websocketClient.disconnect();
    };
  }, []);

  // Setup WebSocket connection for real-time updates
  const setupWebSocketConnection = useCallback(() => {
    // WebSocket event handlers
    const handleWebSocketConnected = (message: WebSocketMessage) => {
      console.log('🔌 [useDashboardData] WebSocket connected');
      setState(prev => ({ ...prev, connectionStatus: 'connected' }));
      websocketClient.subscribeToPortfolio();
    };

    const handleWebSocketDisconnected = (message: WebSocketMessage) => {
      console.log('🔌 [useDashboardData] WebSocket disconnected');
      setState(prev => ({ ...prev, connectionStatus: 'disconnected' }));
    };

    const handleWebSocketError = (message: WebSocketMessage) => {
      console.error('💥 [useDashboardData] WebSocket error:', message.error);
      setState(prev => ({ ...prev, connectionStatus: 'error' }));
    };

    const handlePortfolioData = (message: WebSocketMessage) => {
      if (message.data && mountedRef.current) {
        console.log('📊 [useDashboardData] Received portfolio update via WebSocket');

        // Update portfolio data in overview
        setState(prev => {
          if (!prev.overview) return prev;

          return {
            ...prev,
            overview: {
              ...prev.overview,
              portfolio: {
                ...prev.overview.portfolio,
                ...message.data
              }
            },
            lastUpdated: Date.now()
          };
        });
      }
    };

    const handleAuthenticated = (message: WebSocketMessage) => {
      console.log('✅ [useDashboardData] WebSocket authenticated');
      setState(prev => ({ ...prev, connectionStatus: 'connected' }));
    };

    // Register event handlers
    websocketClient.on('connected', handleWebSocketConnected);
    websocketClient.on('disconnected', handleWebSocketDisconnected);
    websocketClient.on('error', handleWebSocketError);
    websocketClient.on('portfolio_data', handlePortfolioData);
    websocketClient.on('authenticated', handleAuthenticated);

    // Connect to WebSocket
    websocketClient.connectPortfolio().catch(error => {
      console.error('💥 [useDashboardData] Failed to connect WebSocket:', error);
      setState(prev => ({ ...prev, connectionStatus: 'error' }));
    });

    // Cleanup function
    return () => {
      websocketClient.off('connected', handleWebSocketConnected);
      websocketClient.off('disconnected', handleWebSocketDisconnected);
      websocketClient.off('error', handleWebSocketError);
      websocketClient.off('portfolio_data', handlePortfolioData);
      websocketClient.off('authenticated', handleAuthenticated);
    };
  }, []);

  // Real-time update management
  const startRealTimeUpdates = useCallback(() => {
    if (!mountedRef.current) return;

    setState(prev => ({ ...prev, isRealTimeActive: true, connectionStatus: 'connecting' }));

    // Clear existing intervals
    if (realTimeIntervalRef.current) {
      clearInterval(realTimeIntervalRef.current);
    }
    if (quickStatsIntervalRef.current) {
      clearInterval(quickStatsIntervalRef.current);
    }

    // Use WebSocket for portfolio updates if connected, otherwise fall back to polling
    if (websocketClient.isConnected) {
      console.log('🚀 [useDashboardData] Using WebSocket for real-time updates');
      websocketClient.requestPortfolioData(); // Request initial data

      // Only update quick stats via polling
      quickStatsIntervalRef.current = setInterval(async () => {
        if (!mountedRef.current) return;

        try {
          await refreshQuickStats();
        } catch (error) {
          console.warn('🔄 [useDashboardData] Quick stats update failed');
        }
      }, QUICK_REFRESH_INTERVAL);
    } else {
      console.log('🚀 [useDashboardData] Using polling for real-time updates');

      // Start portfolio data updates every second via polling
      realTimeIntervalRef.current = setInterval(async () => {
        if (!mountedRef.current) return;

        try {
          await refreshDashboardSilent();
          setState(prev => ({ ...prev, connectionStatus: 'connected' }));
          retryCountRef.current = 0; // Reset retry count on success
        } catch (error) {
          console.warn('🔄 [useDashboardData] Real-time update failed, will retry...');
          handleRealTimeError();
        }
      }, REAL_TIME_INTERVAL);

      // Start quick stats updates every 5 seconds
      quickStatsIntervalRef.current = setInterval(async () => {
        if (!mountedRef.current) return;

        try {
          await refreshQuickStats();
        } catch (error) {
          console.warn('🔄 [useDashboardData] Quick stats update failed');
        }
      }, QUICK_REFRESH_INTERVAL);
    }

    console.log('🚀 [useDashboardData] Real-time updates started');
  }, []);

  const stopRealTimeUpdates = useCallback(() => {
    setState(prev => ({ ...prev, isRealTimeActive: false, connectionStatus: 'disconnected' }));

    if (realTimeIntervalRef.current) {
      clearInterval(realTimeIntervalRef.current);
      realTimeIntervalRef.current = null;
    }
    if (quickStatsIntervalRef.current) {
      clearInterval(quickStatsIntervalRef.current);
      quickStatsIntervalRef.current = null;
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }

    console.log('⏹️ [useDashboardData] Real-time updates stopped');
  }, []);

  const toggleRealTimeUpdates = useCallback(() => {
    if (state.isRealTimeActive) {
      stopRealTimeUpdates();
    } else {
      startRealTimeUpdates();
    }
  }, [state.isRealTimeActive, startRealTimeUpdates, stopRealTimeUpdates]);

  const handleRealTimeError = useCallback(() => {
    retryCountRef.current += 1;
    const maxRetries = 5;
    const retryDelay = Math.min(1000 * Math.pow(2, retryCountRef.current), 30000); // Exponential backoff, max 30s

    setState(prev => ({ ...prev, connectionStatus: 'error' }));

    if (retryCountRef.current >= maxRetries) {
      console.error('💥 [useDashboardData] Max retries reached, stopping real-time updates');
      stopRealTimeUpdates();
      setState(prev => ({
        ...prev,
        error: 'Real-time updates failed. Please refresh the page.',
        connectionStatus: 'error'
      }));
      return;
    }

    // Retry with exponential backoff
    retryTimeoutRef.current = setTimeout(() => {
      if (mountedRef.current && state.isRealTimeActive) {
        console.log(`🔄 [useDashboardData] Retrying real-time updates (attempt ${retryCountRef.current + 1}/${maxRetries})`);
        startRealTimeUpdates();
      }
    }, retryDelay);
  }, [state.isRealTimeActive, startRealTimeUpdates, stopRealTimeUpdates]);

  // Refresh dashboard overview (with loading state)
  const refreshDashboard = useCallback(async () => {
    if (!mountedRef.current) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      console.log('🔄 [useDashboardData] Refreshing dashboard overview...');
      const overview = await dashboardApi.getDashboardOverview();

      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          overview,
          loading: false,
          lastUpdated: Date.now(),
        }));
        console.log('✅ [useDashboardData] Dashboard overview updated');
      }
    } catch (error: any) {
      console.error('💥 [useDashboardData] Failed to refresh dashboard:', error);
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          loading: false,
          error: error.message || 'Failed to load dashboard data',
        }));
      }
    }
  }, []);

  // Silent refresh for real-time updates (no loading state)
  const refreshDashboardSilent = useCallback(async () => {
    if (!mountedRef.current) return;

    const overview = await dashboardApi.getDashboardOverview();

    if (mountedRef.current) {
      setState(prev => ({
        ...prev,
        overview,
        lastUpdated: Date.now(),
      }));
    }
  }, []);

  // Refresh quick stats
  const refreshQuickStats = useCallback(async () => {
    if (!mountedRef.current) return;

    try {
      console.log('🔄 [useDashboardData] Refreshing quick stats...');
      const quickStats = await dashboardApi.getQuickStats();
      
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          quickStats,
          lastUpdated: Date.now(),
        }));
        console.log('✅ [useDashboardData] Quick stats updated');
      }
    } catch (error: any) {
      console.error('💥 [useDashboardData] Failed to refresh quick stats:', error);
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          error: error.message || 'Failed to load quick stats',
        }));
      }
    }
  }, []);

  // Refresh portfolio performance
  const refreshPortfolioPerformance = useCallback(async (period: string = '1M') => {
    if (!mountedRef.current) return;

    try {
      console.log('🔄 [useDashboardData] Refreshing portfolio performance...', { period });
      const portfolioPerformance = await dashboardApi.getPortfolioPerformance(period);
      
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          portfolioPerformance,
          lastUpdated: Date.now(),
        }));
        console.log('✅ [useDashboardData] Portfolio performance updated');
      }
    } catch (error: any) {
      console.error('💥 [useDashboardData] Failed to refresh portfolio performance:', error);
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          error: error.message || 'Failed to load portfolio performance',
        }));
      }
    }
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Check if data is stale
  const isStale = state.lastUpdated ? Date.now() - state.lastUpdated > STALE_TIME : true;

  return {
    ...state,
    refreshDashboard,
    refreshQuickStats,
    refreshPortfolioPerformance,
    clearError,
    isStale,
    startRealTimeUpdates,
    stopRealTimeUpdates,
    toggleRealTimeUpdates,
  };
}

// Real data hooks for individual components
export function usePortfolioValue() {
  const { overview } = useDashboardData();
  
  if (!overview?.portfolio) {
    return {
      totalValueUsd: 0,
      dailyPnl: 0,
      dailyPnlPercent: 0
    };
  }
  
  return {
    totalValueUsd: overview.portfolio.total_value_usd,
    dailyPnl: overview.portfolio.daily_pnl,
    dailyPnlPercent: overview.portfolio.daily_pnl_percent
  };
}

export function useTradingBots() {
  const { overview } = useDashboardData();
  
  if (!overview?.trading_bots) {
    return {
      bots: [],
      activeBotCount: 0,
      totalBots: 0,
      averageWinRate: 0
    };
  }
  
  const activeBots = overview.trading_bots.filter(bot => bot.status === 'active');
  const totalWinRate = overview.trading_bots.reduce((sum, bot) => sum + bot.win_rate, 0);
  const averageWinRate = overview.trading_bots.length > 0 ? totalWinRate / overview.trading_bots.length : 0;
  
  return {
    bots: overview.trading_bots,
    activeBotCount: activeBots.length,
    totalBots: overview.trading_bots.length,
    averageWinRate
  };
}

export function useAssetAllocation() {
  const { overview } = useDashboardData();

  if (!overview?.portfolio?.asset_allocation) {
    return [];
  }

  // Convert asset allocation to chart format with proper data structure
  const colors = ['#F7931A', '#627EEA', '#F3BA2F', '#8B5CF6', '#10B981', '#F59E0B'];

  return overview.portfolio.asset_allocation.map((allocation, index) => ({
    name: allocation.asset,
    value: parseFloat(allocation.percentage.toFixed(5)), // 5 decimal places for percentage
    amount: allocation.usd_value,
    changePercent: allocation.price_change_24h || 0.0, // Real 24h price change
    color: colors[index % colors.length]
  }));
}

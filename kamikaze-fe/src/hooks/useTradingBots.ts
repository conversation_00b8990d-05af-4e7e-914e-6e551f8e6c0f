/**
 * Trading Bots Hook
 * React hook for managing trading bot state and operations
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useToast } from '@/hooks/use-toast';
import { tradingBotApi } from '@/services/tradingBotApi';
import { websocketClient } from '@/services/websocketClient';
import {
  TradingBot,
  TradingBotConfig,
  BotStatus,
  TradingBotMetrics,
  BotWebSocketMessage,
  StrategyConfig
} from '@/types/tradingBot';

interface UseTradingBotsState {
  bots: TradingBot[];
  loading: boolean;
  error: string | null;
  selectedBot: TradingBot | null;
  isConnected: boolean;
}

interface UseTradingBotsReturn extends UseTradingBotsState {
  // Bot management
  createBot: (config: TradingBotConfig) => Promise<TradingBot | null>;
  updateBot: (botId: string, config: Partial<TradingBotConfig>) => Promise<TradingBot | null>;
  deleteBot: (botId: string) => Promise<boolean>;

  // Bot control
  startBot: (botId: string) => Promise<boolean>;
  stopBot: (botId: string) => Promise<boolean>;
  pauseBot: (botId: string) => Promise<boolean>;
  resumeBot: (botId: string) => Promise<boolean>;

  // Data operations
  refreshBots: () => Promise<void>;
  selectBot: (botId: string | null) => void;
  getBotMetrics: (botId: string) => Promise<TradingBotMetrics | null>;

  // Strategy operations
  getStrategyTemplates: () => Promise<StrategyConfig[]>;
  validateStrategy: (strategy: StrategyConfig) => Promise<{ valid: boolean; errors: string[] }>;
  backtestStrategy: (strategy: StrategyConfig, config: any) => Promise<any>;

  // Real-time updates
  subscribeToBot: (botId: string) => Promise<void>;
  unsubscribeFromBot: (botId: string) => Promise<void>;
}

export const useTradingBots = (): UseTradingBotsReturn => {
  const [state, setState] = useState<UseTradingBotsState>({
    bots: [],
    loading: false,
    error: null,
    selectedBot: null,
    isConnected: false
  });

  const { toast } = useToast();
  const wsSubscriptions = useRef<Set<string>>(new Set());

  // Initialize bots on mount
  useEffect(() => {
    refreshBots();
    setupWebSocketConnection();

    return () => {
      // Cleanup WebSocket subscriptions
      wsSubscriptions.current.forEach(botId => {
        unsubscribeFromBot(botId);
      });
    };
  }, []);

  const setupWebSocketConnection = useCallback(async () => {
    try {
      await websocketClient.connect();
      setState(prev => ({ ...prev, isConnected: true }));

      // Set up message handler for bot updates
      websocketClient.onMessage((message: any) => {
        if (message.type?.startsWith('bot_') || message.type === 'signal' || message.type === 'trade') {
          handleWebSocketMessage(message as BotWebSocketMessage);
        }
      });
    } catch (error) {
      console.error('Failed to setup WebSocket connection:', error);
      setState(prev => ({ ...prev, isConnected: false }));
    }
  }, []);

  const handleWebSocketMessage = useCallback((message: BotWebSocketMessage) => {
    setState(prev => {
      const updatedBots = [...prev.bots];
      const botIndex = updatedBots.findIndex(bot => bot.id === message.bot_id);

      if (botIndex === -1) return prev;

      switch (message.type) {
        case 'bot_status':
          updatedBots[botIndex] = {
            ...updatedBots[botIndex],
            status: message.status,
            error_message: message.message
          };
          break;

        case 'signal':
          updatedBots[botIndex] = {
            ...updatedBots[botIndex],
            metrics: {
              ...updatedBots[botIndex].metrics,
              recent_signals: [message.signal, ...updatedBots[botIndex].metrics.recent_signals.slice(0, 9)]
            },
            last_signal_time: message.timestamp
          };
          break;

        case 'trade':
          updatedBots[botIndex] = {
            ...updatedBots[botIndex],
            metrics: {
              ...updatedBots[botIndex].metrics,
              recent_trades: [message.trade, ...updatedBots[botIndex].metrics.recent_trades.slice(0, 9)]
            }
          };
          break;

        case 'performance':
          updatedBots[botIndex] = {
            ...updatedBots[botIndex],
            metrics: {
              ...updatedBots[botIndex].metrics,
              performance: message.metrics
            }
          };
          break;
      }

      return {
        ...prev,
        bots: updatedBots,
        selectedBot: prev.selectedBot?.id === message.bot_id ? updatedBots[botIndex] : prev.selectedBot
      };
    });
  }, []);

  const refreshBots = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await tradingBotApi.getBots();
      if (response.success) {
        setState(prev => ({
          ...prev,
          bots: response.bots,
          loading: false
        }));
      } else {
        throw new Error('Failed to fetch bots');
      }
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'Failed to fetch trading bots'
      }));
      toast({
        title: 'Error',
        description: error.message || 'Failed to fetch trading bots',
        variant: 'destructive'
      });
    }
  }, [toast]);

  const createBot = useCallback(async (config: TradingBotConfig): Promise<TradingBot | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await tradingBotApi.createBot(config);
      if (response.success) {
        setState(prev => ({
          ...prev,
          bots: [...prev.bots, response.bot],
          loading: false
        }));

        toast({
          title: 'Success',
          description: `Trading bot "${config.name}" created successfully`
        });

        return response.bot;
      } else {
        throw new Error(response.message || 'Failed to create bot');
      }
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'Failed to create trading bot'
      }));
      toast({
        title: 'Error',
        description: error.message || 'Failed to create trading bot',
        variant: 'destructive'
      });
      return null;
    }
  }, [toast]);

  const updateBot = useCallback(async (botId: string, config: Partial<TradingBotConfig>): Promise<TradingBot | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const updatedBot = await tradingBotApi.updateBot(botId, config);
      setState(prev => ({
        ...prev,
        bots: prev.bots.map(bot => bot.id === botId ? updatedBot : bot),
        selectedBot: prev.selectedBot?.id === botId ? updatedBot : prev.selectedBot,
        loading: false
      }));

      toast({
        title: 'Success',
        description: 'Trading bot updated successfully'
      });

      return updatedBot;
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'Failed to update trading bot'
      }));
      toast({
        title: 'Error',
        description: error.message || 'Failed to update trading bot',
        variant: 'destructive'
      });
      return null;
    }
  }, [toast]);

  const deleteBot = useCallback(async (botId: string): Promise<boolean> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      await tradingBotApi.deleteBot(botId);
      setState(prev => ({
        ...prev,
        bots: prev.bots.filter(bot => bot.id !== botId),
        selectedBot: prev.selectedBot?.id === botId ? null : prev.selectedBot,
        loading: false
      }));

      // Unsubscribe from WebSocket updates
      unsubscribeFromBot(botId);

      toast({
        title: 'Success',
        description: 'Trading bot deleted successfully'
      });

      return true;
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'Failed to delete trading bot'
      }));
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete trading bot',
        variant: 'destructive'
      });
      return false;
    }
  }, [toast]);

  const startBot = useCallback(async (botId: string): Promise<boolean> => {
    try {
      const response = await tradingBotApi.startBot(botId);
      if (response.success) {
        setState(prev => ({
          ...prev,
          bots: prev.bots.map(bot =>
            bot.id === botId
              ? { ...bot, status: BotStatus.STARTING, started_at: new Date().toISOString() }
              : bot
          ),
          selectedBot: prev.selectedBot?.id === botId
            ? { ...prev.selectedBot, status: BotStatus.STARTING, started_at: new Date().toISOString() }
            : prev.selectedBot
        }));

        // Subscribe to real-time updates
        subscribeToBot(botId);

        toast({
          title: 'Success',
          description: 'Trading bot started successfully'
        });

        return true;
      } else {
        throw new Error(response.message || 'Failed to start bot');
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to start trading bot',
        variant: 'destructive'
      });
      return false;
    }
  }, [toast]);

  const stopBot = useCallback(async (botId: string): Promise<boolean> => {
    try {
      const response = await tradingBotApi.stopBot(botId);
      if (response.success) {
        setState(prev => ({
          ...prev,
          bots: prev.bots.map(bot =>
            bot.id === botId
              ? { ...bot, status: BotStatus.STOPPING, stopped_at: new Date().toISOString() }
              : bot
          ),
          selectedBot: prev.selectedBot?.id === botId
            ? { ...prev.selectedBot, status: BotStatus.STOPPING, stopped_at: new Date().toISOString() }
            : prev.selectedBot
        }));

        // Unsubscribe from real-time updates
        unsubscribeFromBot(botId);

        toast({
          title: 'Success',
          description: 'Trading bot stopped successfully'
        });

        return true;
      } else {
        throw new Error(response.message || 'Failed to stop bot');
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to stop trading bot',
        variant: 'destructive'
      });
      return false;
    }
  }, [toast]);

  const pauseBot = useCallback(async (botId: string): Promise<boolean> => {
    try {
      const response = await tradingBotApi.pauseBot(botId);
      if (response.success) {
        setState(prev => ({
          ...prev,
          bots: prev.bots.map(bot =>
            bot.id === botId ? { ...bot, status: BotStatus.PAUSED } : bot
          ),
          selectedBot: prev.selectedBot?.id === botId
            ? { ...prev.selectedBot, status: BotStatus.PAUSED }
            : prev.selectedBot
        }));

        toast({
          title: 'Success',
          description: 'Trading bot paused successfully'
        });

        return true;
      } else {
        throw new Error(response.message || 'Failed to pause bot');
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to pause trading bot',
        variant: 'destructive'
      });
      return false;
    }
  }, [toast]);

  const resumeBot = useCallback(async (botId: string): Promise<boolean> => {
    try {
      const response = await tradingBotApi.resumeBot(botId);
      if (response.success) {
        setState(prev => ({
          ...prev,
          bots: prev.bots.map(bot =>
            bot.id === botId ? { ...bot, status: BotStatus.RUNNING } : bot
          ),
          selectedBot: prev.selectedBot?.id === botId
            ? { ...prev.selectedBot, status: BotStatus.RUNNING }
            : prev.selectedBot
        }));

        toast({
          title: 'Success',
          description: 'Trading bot resumed successfully'
        });

        return true;
      } else {
        throw new Error(response.message || 'Failed to resume bot');
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to resume trading bot',
        variant: 'destructive'
      });
      return false;
    }
  }, [toast]);

  const selectBot = useCallback((botId: string | null) => {
    setState(prev => ({
      ...prev,
      selectedBot: botId ? prev.bots.find(bot => bot.id === botId) || null : null
    }));
  }, []);

  const getBotMetrics = useCallback(async (botId: string): Promise<TradingBotMetrics | null> => {
    try {
      const response = await tradingBotApi.getBotMetrics(botId);
      if (response.success) {
        return response.metrics;
      } else {
        throw new Error('Failed to fetch bot metrics');
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to fetch bot metrics',
        variant: 'destructive'
      });
      return null;
    }
  }, [toast]);

  const getStrategyTemplates = useCallback(async (): Promise<StrategyConfig[]> => {
    try {
      return await tradingBotApi.getStrategyTemplates();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to fetch strategy templates',
        variant: 'destructive'
      });
      return [];
    }
  }, [toast]);

  const validateStrategy = useCallback(async (strategy: StrategyConfig): Promise<{ valid: boolean; errors: string[] }> => {
    try {
      return await tradingBotApi.validateStrategy(strategy);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to validate strategy',
        variant: 'destructive'
      });
      return { valid: false, errors: [error.message || 'Validation failed'] };
    }
  }, [toast]);

  const backtestStrategy = useCallback(async (strategy: StrategyConfig, config: any): Promise<any> => {
    try {
      return await tradingBotApi.backtestStrategy(strategy, config);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to backtest strategy',
        variant: 'destructive'
      });
      return null;
    }
  }, [toast]);

  const subscribeToBot = useCallback(async (botId: string): Promise<void> => {
    if (wsSubscriptions.current.has(botId)) return;

    try {
      await websocketClient.send({
        type: 'subscribe',
        channel: `bot_${botId}`,
        data: { bot_id: botId }
      });
      wsSubscriptions.current.add(botId);
    } catch (error) {
      console.error(`Failed to subscribe to bot ${botId}:`, error);
    }
  }, []);

  const unsubscribeFromBot = useCallback(async (botId: string): Promise<void> => {
    if (!wsSubscriptions.current.has(botId)) return;

    try {
      await websocketClient.send({
        type: 'unsubscribe',
        channel: `bot_${botId}`,
        data: { bot_id: botId }
      });
      wsSubscriptions.current.delete(botId);
    } catch (error) {
      console.error(`Failed to unsubscribe from bot ${botId}:`, error);
    }
  }, []);

  return {
    ...state,
    createBot,
    updateBot,
    deleteBot,
    startBot,
    stopBot,
    pauseBot,
    resumeBot,
    refreshBots,
    selectBot,
    getBotMetrics,
    getStrategyTemplates,
    validateStrategy,
    backtestStrategy,
    subscribeToBot,
    unsubscribeFromBot
  };
};
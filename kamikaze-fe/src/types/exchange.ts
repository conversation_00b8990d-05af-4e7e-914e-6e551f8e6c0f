export interface ExchangeCredentials {
  apiKey: string;
  secretKey: string;
}

export interface ExchangeConnection {
  id: string;
  name: string;
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  credentials?: ExchangeCredentials;
  lastConnected?: number;
  error?: string;
  isAvailable: boolean;
  comingSoon?: boolean;
}

export interface ExchangeInfo {
  id: string;
  name: string;
  displayName: string;
  logo: string;
  description: string;
  isAvailable: boolean;
  comingSoon?: boolean;
  features: string[];
}

export type TradingEnvironment = 'testnet' | 'mainnet';

export interface EnvironmentState {
  currentEnvironment: TradingEnvironment;
  isTestnetMode: boolean;
  environmentHistory: Array<{
    environment: TradingEnvironment;
    timestamp: Date;
    exchangeId: string;
  }>;
}

export interface ExchangeContextType {
  exchanges: Record<string, ExchangeConnection>;
  selectedExchange: string | null;
  isConnecting: boolean;
  connectExchange: (exchangeId: string, credentials: ExchangeCredentials) => Promise<boolean>;
  disconnectExchange: (exchangeId: string) => void;
  testConnection: (exchangeId: string, credentials: ExchangeCredentials) => Promise<boolean>;
  getConnectionStatus: (exchangeId: string) => ExchangeConnection['status'];
  hasConnectedExchange: () => boolean;
  showConnectionModal: boolean;
  setShowConnectionModal: (show: boolean) => void;
  // Environment management
  environmentState: EnvironmentState;
  setEnvironment: (environment: TradingEnvironment, exchangeId?: string) => void;
  getEnvironmentForExchange: (exchangeId: string) => TradingEnvironment | null;
  isTestnetMode: () => boolean;
}

export interface MCPResponse {
  success: boolean;
  data?: Record<string, unknown>;
  error?: string;
}

export interface BinanceAccountInfo {
  balances: Array<{
    asset: string;
    free: string;
    locked: string;
  }>;
  canTrade: boolean;
  canWithdraw: boolean;
  canDeposit: boolean;
}

/**
 * Trading Bot Types and Interfaces
 * Professional-grade trading bot type definitions
 */

// Strategy Types
export enum StrategyType {
  MEAN_REVERSION = 'mean_reversion',
  MOMENTUM = 'momentum',
  MULTI_TIMEFRAME = 'multi_timeframe',
  VOLATILITY = 'volatility',
  GRID_TRADING = 'grid_trading',
  DCA = 'dca',
  ARBITRAGE = 'arbitrage'
}

export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

export enum BotStatus {
  STOPPED = 'stopped',
  STARTING = 'starting',
  RUNNING = 'running',
  PAUSED = 'paused',
  ERROR = 'error',
  STOPPING = 'stopping'
}

export enum TimeFrame {
  M1 = '1m',
  M5 = '5m',
  M15 = '15m',
  M30 = '30m',
  H1 = '1h',
  H4 = '4h',
  D1 = '1d'
}

export enum SignalType {
  BUY = 'BUY',
  SELL = 'SELL',
  HOLD = 'HOLD'
}

export enum PositionSizingMethod {
  FIXED_AMOUNT = 'FIXED_AMOUNT',
  FIXED_PERCENTAGE = 'FIXED_PERCENTAGE',
  VOLATILITY_ADJUSTED = 'VOLATILITY_ADJUSTED',
  SIGNAL_STRENGTH = 'SIGNAL_STRENGTH',
  KELLY_CRITERION = 'KELLY_CRITERION'
}

// Technical Indicator Configuration
export interface IndicatorConfig {
  name: string;
  parameters: Record<string, number | string | boolean>;
  weight?: number;
  enabled: boolean;
}

export interface IndicatorCondition {
  indicator: string;
  operator: 'GT' | 'LT' | 'EQ' | 'GTE' | 'LTE' | 'CROSS_ABOVE' | 'CROSS_BELOW';
  value: number | string;
  lookback?: number;
}

export interface ConditionGroup {
  conditions: IndicatorCondition[];
  operator: 'AND' | 'OR';
}

// Strategy Configuration
export interface StrategyRule {
  name: string;
  entry_conditions: ConditionGroup;
  exit_conditions?: ConditionGroup;
  signal_type: SignalType;
  min_confidence: number;
  max_holding_period?: number;
  stop_loss_pct?: number;
  take_profit_pct?: number;
  description?: string;
}

export interface StrategyConfig {
  name: string;
  description: string;
  strategy_type: StrategyType;
  rules: StrategyRule[];
  indicators: IndicatorConfig[];
  timeframes: TimeFrame[];
  risk_level: RiskLevel;
  parameters: Record<string, any>;
}

// Risk Management Configuration
export interface RiskManagementConfig {
  max_portfolio_risk: number;
  max_position_risk: number;
  max_drawdown: number;
  position_sizing_method: PositionSizingMethod;
  risk_level: RiskLevel;
  enable_stop_loss: boolean;
  enable_take_profit: boolean;
  enable_trailing_stop: boolean;
  correlation_limit: number;
  max_positions: number;
  daily_loss_limit: number;
}

// Trading Engine Configuration
export interface TradingEngineConfig {
  max_concurrent_orders: number;
  order_timeout_seconds: number;
  position_update_interval: number;
  signal_generation_interval: number;
  enable_paper_trading: boolean;
  enable_order_validation: boolean;
  enable_risk_checks: boolean;
  max_daily_trades: number;
  max_daily_loss_pct: number;
  emergency_stop_loss_pct: number;
  slippage_tolerance_pct: number;
  commission_rate: number;
}

// Signal Processing
export interface SignalFilter {
  min_confidence: number;
  min_consensus_score: number;
  min_quality: 'POOR' | 'FAIR' | 'GOOD' | 'EXCELLENT';
  require_trend_alignment: boolean;
  min_trend_alignment: number;
  max_signals_per_timeframe: number;
  enable_volume_filter: boolean;
  min_volume_ratio: number;
}

export interface TradingSignal {
  id: string;
  symbol: string;
  signal_type: SignalType;
  confidence: number;
  strength: number;
  timeframe: TimeFrame;
  timestamp: string;
  price: number;
  indicators: Record<string, number>;
  metadata: Record<string, any>;
  strategy_name: string;
  risk_reward_ratio?: number;
  stop_loss?: number;
  take_profit?: number;
}

export interface MultiTimeframeSignal {
  primary_signal: TradingSignal;
  supporting_signals: Record<TimeFrame, TradingSignal>;
  consensus_score: number;
  trend_alignment: number;
  quality_score: number;
  risk_assessment: RiskAssessment;
}

export interface RiskAssessment {
  position_risk: number;
  portfolio_impact: number;
  correlation_risk: number;
  volatility_risk: number;
  liquidity_risk: number;
  overall_risk: RiskLevel;
}

// Trading Bot Configuration
export interface TradingBotConfig {
  id?: string;
  name: string;
  description: string;
  strategy: StrategyConfig;
  risk_management: RiskManagementConfig;
  trading_engine: TradingEngineConfig;
  signal_filter: SignalFilter;
  symbols: string[];
  initial_capital: number;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

// Performance Metrics
export interface PerformanceMetrics {
  total_return: number;
  total_return_pct: number;
  annualized_return: number;
  sharpe_ratio: number;
  sortino_ratio: number;
  max_drawdown: number;
  max_drawdown_pct: number;
  win_rate: number;
  profit_factor: number;
  total_trades: number;
  winning_trades: number;
  losing_trades: number;
  avg_trade_return: number;
  avg_winning_trade: number;
  avg_losing_trade: number;
  avg_trade_duration: number;
  volatility: number;
  beta: number;
  alpha: number;
  var_95: number;
  calmar_ratio: number;
  information_ratio: number;
  treynor_ratio: number;
}

export interface TradingBotMetrics {
  bot_id: string;
  performance: PerformanceMetrics;
  current_positions: Position[];
  recent_trades: Trade[];
  recent_signals: TradingSignal[];
  balance: number;
  equity: number;
  margin_used: number;
  margin_available: number;
  unrealized_pnl: number;
  realized_pnl: number;
  timestamp: string;
}

// Trading Data
export interface Position {
  id: string;
  symbol: string;
  side: 'LONG' | 'SHORT';
  size: number;
  entry_price: number;
  current_price: number;
  unrealized_pnl: number;
  unrealized_pnl_pct: number;
  margin_used: number;
  timestamp: string;
  strategy_name: string;
  stop_loss?: number;
  take_profit?: number;
}

export interface Trade {
  id: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  size: number;
  price: number;
  total_value: number;
  commission: number;
  pnl: number;
  pnl_pct: number;
  timestamp: string;
  strategy_name: string;
  signal_id?: string;
  duration?: number;
}

export interface TradingOrder {
  id: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  type: 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';
  size: number;
  price?: number;
  stop_price?: number;
  status: 'PENDING' | 'FILLED' | 'CANCELLED' | 'REJECTED';
  timestamp: string;
  filled_size?: number;
  filled_price?: number;
  strategy_name: string;
  signal_id?: string;
}

// Trading Bot Instance
export interface TradingBot {
  id: string;
  config: TradingBotConfig;
  status: BotStatus;
  metrics: TradingBotMetrics;
  created_at: string;
  started_at?: string;
  stopped_at?: string;
  error_message?: string;
  last_signal_time?: string;
  next_signal_time?: string;
}

// API Response Types
export interface CreateBotRequest {
  config: TradingBotConfig;
}

export interface CreateBotResponse {
  success: boolean;
  bot: TradingBot;
  message?: string;
}

export interface BotControlRequest {
  action: 'start' | 'stop' | 'pause' | 'resume';
  bot_id: string;
}

export interface BotControlResponse {
  success: boolean;
  bot: TradingBot;
  message?: string;
}

export interface BotListResponse {
  success: boolean;
  bots: TradingBot[];
  total: number;
}

export interface BotMetricsResponse {
  success: boolean;
  metrics: TradingBotMetrics;
}

// WebSocket Message Types
export interface BotStatusMessage {
  type: 'bot_status';
  bot_id: string;
  status: BotStatus;
  timestamp: string;
  message?: string;
}

export interface SignalMessage {
  type: 'signal';
  bot_id: string;
  signal: TradingSignal;
  timestamp: string;
}

export interface TradeMessage {
  type: 'trade';
  bot_id: string;
  trade: Trade;
  timestamp: string;
}

export interface PerformanceMessage {
  type: 'performance';
  bot_id: string;
  metrics: PerformanceMetrics;
  timestamp: string;
}

export type BotWebSocketMessage = BotStatusMessage | SignalMessage | TradeMessage | PerformanceMessage;
export interface BacktestingStrategy {
  id: string;
  name: string;
  description: string;
  category: 'trend_following' | 'mean_reversion' | 'momentum' | 'arbitrage' | 'custom';
  parameters: Record<string, any>;
}

export interface TradingPair {
  symbol: string;
  baseAsset: string;
  quoteAsset: string;
  active: boolean;
}

export interface BacktestingConfig {
  strategy: BacktestingStrategy;
  tradingPairs: string[];
  timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d' | '1w';
  startDate: Date;
  endDate: Date;
  initialCapital: number;
  commission: number;
  slippage: number;
  riskManagement: {
    stopLoss?: number;
    takeProfit?: number;
    positionSizing: 'fixed' | 'percentage' | 'kelly' | 'volatility';
    maxPositionSize: number;
    maxDrawdown: number;
  };
  benchmark?: string;
}

export interface Trade {
  id: string;
  timestamp: Date;
  symbol: string;
  side: 'buy' | 'sell';
  quantity: number;
  price: number;
  commission: number;
  pnl?: number;
  reason: string;
}

export interface BacktestingResults {
  id: string;
  config: BacktestingConfig;
  startTime: Date;
  endTime: Date;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  trades: Trade[];
  metrics: {
    totalReturn: number;
    totalReturnPercent: number;
    annualizedReturn: number;
    sharpeRatio: number;
    sortinoRatio: number;
    maxDrawdown: number;
    maxDrawdownPercent: number;
    winRate: number;
    profitFactor: number;
    totalTrades: number;
    winningTrades: number;
    losingTrades: number;
    averageWin: number;
    averageLoss: number;
    largestWin: number;
    largestLoss: number;
    averageTradeDuration: number;
    volatility: number;
    calmarRatio: number;
    recoveryFactor: number;
    payoffRatio: number;
    expectedValue: number;
  };
  equity: Array<{
    timestamp: Date;
    value: number;
    drawdown: number;
  }>;
  monthlyReturns: Array<{
    month: string;
    return: number;
  }>;
  benchmarkComparison?: {
    benchmark: string;
    benchmarkReturn: number;
    alpha: number;
    beta: number;
    correlation: number;
  };
}

export interface BacktestingState {
  results: BacktestingResults[];
  currentBacktest?: BacktestingResults;
  isRunning: boolean;
  error?: string;
}

export interface MarketCondition {
  id: string;
  name: string;
  description: string;
  parameters: {
    volatilityMultiplier: number;
    trendStrength: number;
    noiseLevel: number;
  };
}

export interface BacktestingFilters {
  dateRange: {
    start: Date;
    end: Date;
  };
  strategies: string[];
  tradingPairs: string[];
  minReturn?: number;
  maxDrawdown?: number;
  minSharpeRatio?: number;
}

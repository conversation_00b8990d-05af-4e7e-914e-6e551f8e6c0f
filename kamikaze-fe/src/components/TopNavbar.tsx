import { <PERSON>, Search, User, <PERSON>, <PERSON>, <PERSON>u, LogOut } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/contexts/AuthContext";
import { ExchangeStatusIndicator } from "@/components/exchange/ExchangeStatusIndicator";
import { ThemeToggle } from "@/components/ThemeToggle";

import { useExchange } from "@/contexts/ExchangeContext";
import { usePortfolioValue } from "@/hooks/useDashboardData";

export function TopNavbar() {
  const { user, logout, isLoading } = useAuth();
  const { hasConnectedExchange, isTestnetMode } = useExchange();
  const { dailyPnl } = usePortfolioValue();
  const navigate = useNavigate();

  // Fallback values while loading or if user is not available
  const displayName = user?.name || 'User';
  const displayInitials = user?.initials || 'U';
  const displayRole = user?.role || 'Trader';
  const displayPortfolioValue = user?.portfolioValue || 0;
  const displayAvatar = user?.avatar;

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const isTestnet = hasConnectedExchange() ? isTestnetMode() : false;

  // Format P&L value with proper sign and color
  const formatPnL = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}$${Math.abs(value).toFixed(2)}`;
  };

  const getPnLColor = (value: number) => {
    return value >= 0 ? 'text-success' : 'text-destructive';
  };

  const getPnLBadgeClass = (value: number) => {
    return value >= 0
      ? 'glass-card bg-success/10 text-success border-success/30 font-medium text-xs px-2 py-1'
      : 'glass-card bg-destructive/10 text-destructive border-destructive/30 font-medium text-xs px-2 py-1';
  };

  return (
    <div className="sticky top-0 z-40">

      <header className={`glass-card border-b border-card-border/60 hover:shadow-elevated transition-all duration-300 relative ${
        hasConnectedExchange()
          ? isTestnet
            ? 'bg-gradient-to-r from-orange-500/8 via-yellow-500/6 to-orange-600/8 border-orange-400/20'
            : 'bg-gradient-to-r from-green-600/8 via-emerald-500/6 to-blue-600/8 border-green-400/20'
          : 'bg-gradient-to-r from-accent-orange/8 via-warning/6 to-destructive/8'
      }`}>
        {/* Header background overlay */}
        <div className={`absolute inset-0 opacity-70 pointer-events-none ${
          hasConnectedExchange()
            ? isTestnet
              ? 'bg-gradient-to-r from-orange-500/8 via-yellow-500/6 to-orange-600/6'
              : 'bg-gradient-to-r from-green-600/8 via-emerald-500/6 to-blue-600/6'
            : 'bg-gradient-to-r from-warning/8 via-accent-orange/6 to-destructive/6'
        }`}></div>

      {/* Main header content */}
      <div className="relative z-10 px-4 sm:px-6 py-3">
        <div className="flex items-center justify-between h-12">
          {/* Left side - Sidebar trigger and search */}
          <div className="flex items-center gap-3 sm:gap-4 min-w-0 flex-1">
            <SidebarTrigger className="flex-shrink-0 glass-card hover:shadow-elevated text-muted-foreground hover:text-foreground hover:border-primary/20 transition-all duration-300 p-2 rounded-lg" />

            <div className="relative glass-card hover:shadow-elevated transition-all duration-300 rounded-lg p-1 min-w-0 flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4 z-10" />
              <Input
                placeholder="Search assets, bots, strategies..."
                className="glass-input pl-10 pr-4 h-10 w-full transition-all duration-300 border-0 bg-transparent focus:bg-card-glass/30 text-sm"
              />
            </div>
          </div>

          {/* Center - Real-time P&L indicator */}
          <div className="hidden xl:flex items-center gap-2 glass-card hover:shadow-elevated transition-all duration-300 px-4 py-2 rounded-xl mx-4">
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full animate-pulse ${dailyPnl >= 0 ? 'bg-success glow-success' : 'bg-destructive glow-destructive'}`}></div>
              <span className="text-xs text-muted-foreground font-medium">24h P&L:</span>
              <Badge className={getPnLBadgeClass(dailyPnl)}>
                {formatPnL(dailyPnl)}
              </Badge>
            </div>
          </div>

          {/* Right side - Actions and user */}
          <div className="flex items-center gap-2 sm:gap-3 flex-shrink-0">
            {/* Action buttons */}
            <div className="flex items-center gap-1 sm:gap-2">
              {/* Notifications */}
              <div className="relative">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 glass-card hover:shadow-elevated hover:border-primary/20 transition-all duration-300 rounded-lg hover:bg-card-glass/50"
                >
                  <Bell className="h-5 w-5 text-foreground hover:text-primary transition-colors duration-200" />
                </Button>
                <Badge
                  className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs bg-destructive text-destructive-foreground border-2 border-background rounded-full shadow-lg z-10"
                >
                  3
                </Badge>
              </div>

              {/* Exchange Status */}
              <ExchangeStatusIndicator />

              {/* Theme toggle */}
              <ThemeToggle />
            </div>

            {/* Separator */}
            <div className="w-px h-6 bg-card-border/30 mx-1"></div>

            {/* User info section */}
            <div className="flex items-center gap-3">
              {/* User name display - visible on larger screens */}
              <div className="hidden md:block text-right">
                <p className="text-sm font-semibold text-foreground leading-tight">{displayName}</p>
                <p className="text-xs text-muted-foreground leading-tight">{displayRole}</p>
              </div>

              {/* User avatar and menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-10 w-10 rounded-full glass-card hover:shadow-elevated hover:border-primary/20 transition-all duration-300 p-0">
                    <Avatar className="h-9 w-9 ring-2 ring-primary/20 ring-offset-1 ring-offset-background">
                      <AvatarImage src={displayAvatar} alt={displayName} />
                      <AvatarFallback className="bg-gradient-to-br from-primary to-primary/80 text-primary-foreground font-semibold text-sm glow-primary">
                        {displayInitials}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-64 glass-card border-card-border/60 backdrop-blur-md hover:shadow-elevated transition-all duration-300" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal p-4">
                    <div className="flex flex-col space-y-3">
                      <div className="flex items-center gap-3 glass-card hover:shadow-elevated transition-all duration-300 p-3 rounded-lg">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={displayAvatar} alt={displayName} />
                          <AvatarFallback className="bg-primary text-primary-foreground text-sm glow-primary">{displayInitials}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-semibold text-foreground truncate">{displayName}</p>
                          <p className="text-xs text-muted-foreground">{displayRole}</p>
                        </div>
                      </div>
                      <div className="glass-card hover:shadow-elevated transition-all duration-300 p-3 border border-card-border/30 rounded-lg">
                        <p className="text-xs text-muted-foreground mb-1">Portfolio Value</p>
                        <p className="text-sm font-bold text-success">${displayPortfolioValue.toLocaleString()}</p>
                      </div>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-card-border/30" />
                  <DropdownMenuItem className="text-foreground glass-card hover:shadow-elevated hover:bg-card-glass/50 hover:border-primary/20 transition-all duration-300 m-1 rounded-lg">
                    Profile Settings
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-foreground glass-card hover:shadow-elevated hover:bg-card-glass/50 hover:border-primary/20 transition-all duration-300 m-1 rounded-lg">
                    Billing & Plans
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-foreground glass-card hover:shadow-elevated hover:bg-card-glass/50 hover:border-primary/20 transition-all duration-300 m-1 rounded-lg">
                    Trading Settings
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="bg-card-border/30" />
                  <DropdownMenuItem
                    className="text-destructive glass-card hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30 transition-all duration-300 m-1 rounded-lg cursor-pointer"
                    onClick={handleLogout}
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
        </div>
      </header>
    </div>
  );
}
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { BacktestingConfig as BacktestConfig, BacktestingStrategy, TradingPair } from '@/types/backtesting';
import {
  Play,
  Settings,
  Calendar,
  DollarSign,
  Shield,
  ChevronDown,
  ChevronRight,
  Target,
  TrendingUp,
  BarChart3,
  Zap
} from 'lucide-react';

interface BacktestingConfigProps {
  config: Partial<BacktestConfig>;
  onConfigChange: (config: Partial<BacktestConfig>) => void;
  onStartBacktest: (config: BacktestConfig) => void;
  isRunning: boolean;
}

const mockStrategies: BacktestingStrategy[] = [
  {
    id: 'sma_crossover',
    name: 'SMA Crossover',
    description: 'Simple Moving Average crossover strategy',
    category: 'trend_following',
    parameters: { fastPeriod: 10, slowPeriod: 30 }
  },
  {
    id: 'rsi_mean_reversion',
    name: 'RSI Mean Reversion',
    description: 'RSI-based mean reversion strategy',
    category: 'mean_reversion',
    parameters: { rsiPeriod: 14, oversold: 30, overbought: 70 }
  },
  {
    id: 'momentum_breakout',
    name: 'Momentum Breakout',
    description: 'Momentum-based breakout strategy',
    category: 'momentum',
    parameters: { lookbackPeriod: 20, breakoutThreshold: 2.0 }
  }
];

const mockTradingPairs: TradingPair[] = [
  { symbol: 'BTCUSDT', baseAsset: 'BTC', quoteAsset: 'USDT', active: true },
  { symbol: 'ETHUSDT', baseAsset: 'ETH', quoteAsset: 'USDT', active: true },
  { symbol: 'BNBUSDT', baseAsset: 'BNB', quoteAsset: 'USDT', active: true },
  { symbol: 'ADAUSDT', baseAsset: 'ADA', quoteAsset: 'USDT', active: true },
  { symbol: 'SOLUSDT', baseAsset: 'SOL', quoteAsset: 'USDT', active: true },
  { symbol: 'DOTUSDT', baseAsset: 'DOT', quoteAsset: 'USDT', active: true }
];

export const BacktestingConfig: React.FC<BacktestingConfigProps> = ({
  config,
  onConfigChange,
  onStartBacktest,
  isRunning
}) => {
  const [selectedPairs, setSelectedPairs] = useState<string[]>(config.tradingPairs || ['BTCUSDT']);
  const [advancedOpen, setAdvancedOpen] = useState(false);
  const [riskManagementOpen, setRiskManagementOpen] = useState(false);

  const handleStrategyChange = (strategyId: string) => {
    const strategy = mockStrategies.find(s => s.id === strategyId);
    if (strategy) {
      onConfigChange({ ...config, strategy });
    }
  };

  const handlePairToggle = (symbol: string, checked: boolean) => {
    const newPairs = checked 
      ? [...selectedPairs, symbol]
      : selectedPairs.filter(p => p !== symbol);
    
    setSelectedPairs(newPairs);
    onConfigChange({ ...config, tradingPairs: newPairs });
  };

  const handleInputChange = (field: string, value: any) => {
    onConfigChange({ ...config, [field]: value });
  };

  const handleRiskManagementChange = (field: string, value: any) => {
    onConfigChange({
      ...config,
      riskManagement: {
        ...config.riskManagement,
        [field]: value
      }
    });
  };

  const handleStartBacktest = () => {
    if (!config.strategy) return;

    const fullConfig: BacktestConfig = {
      strategy: config.strategy,
      tradingPairs: selectedPairs,
      timeframe: (config.timeframe as any) || '1h',
      startDate: config.startDate || new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
      endDate: config.endDate || new Date(),
      initialCapital: config.initialCapital || 10000,
      commission: config.commission || 0.001,
      slippage: config.slippage || 0.001,
      riskManagement: {
        positionSizing: 'percentage',
        maxPositionSize: 10,
        maxDrawdown: 20,
        ...config.riskManagement
      }
    };

    onStartBacktest(fullConfig);
  };

  const isConfigValid = config.strategy && selectedPairs.length > 0 && config.initialCapital;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Strategy Configuration */}
      <div className="lg:col-span-2 space-y-6">
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-primary" />
              Strategy Selection
            </CardTitle>
            <CardDescription>
              Choose a trading strategy to backtest
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Trading Strategy</Label>
              <Select
                value={config.strategy?.id || ''}
                onValueChange={handleStrategyChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a strategy" />
                </SelectTrigger>
                <SelectContent>
                  {mockStrategies.map(strategy => (
                    <SelectItem key={strategy.id} value={strategy.id}>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {strategy.category.replace('_', ' ')}
                        </Badge>
                        {strategy.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {config.strategy && (
              <div className="p-4 rounded-lg bg-muted/30 border border-border/50">
                <p className="text-sm text-muted-foreground">
                  {config.strategy.description}
                </p>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Timeframe</Label>
                <Select
                  value={config.timeframe || '1h'}
                  onValueChange={(value) => handleInputChange('timeframe', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1m">1 Minute</SelectItem>
                    <SelectItem value="5m">5 Minutes</SelectItem>
                    <SelectItem value="15m">15 Minutes</SelectItem>
                    <SelectItem value="1h">1 Hour</SelectItem>
                    <SelectItem value="4h">4 Hours</SelectItem>
                    <SelectItem value="1d">1 Day</SelectItem>
                    <SelectItem value="1w">1 Week</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Initial Capital ($)</Label>
                <Input
                  type="number"
                  value={config.initialCapital || 10000}
                  onChange={(e) => handleInputChange('initialCapital', parseFloat(e.target.value))}
                  placeholder="10000"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Start Date</Label>
                <Input
                  type="date"
                  value={config.startDate?.toISOString().split('T')[0] || ''}
                  onChange={(e) => handleInputChange('startDate', new Date(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label>End Date</Label>
                <Input
                  type="date"
                  value={config.endDate?.toISOString().split('T')[0] || ''}
                  onChange={(e) => handleInputChange('endDate', new Date(e.target.value))}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Trading Pairs */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-chart-1" />
              Trading Pairs
            </CardTitle>
            <CardDescription>
              Select trading pairs to include in the backtest
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {mockTradingPairs.map(pair => (
                <div key={pair.symbol} className="flex items-center space-x-2">
                  <Checkbox
                    id={pair.symbol}
                    checked={selectedPairs.includes(pair.symbol)}
                    onCheckedChange={(checked) => handlePairToggle(pair.symbol, checked as boolean)}
                  />
                  <Label htmlFor={pair.symbol} className="text-sm font-medium">
                    {pair.symbol}
                  </Label>
                </div>
              ))}
            </div>
            
            {selectedPairs.length > 0 && (
              <div className="mt-4 pt-4 border-t border-border/50">
                <p className="text-sm text-muted-foreground mb-2">Selected pairs:</p>
                <div className="flex flex-wrap gap-2">
                  {selectedPairs.map(pair => (
                    <Badge key={pair} variant="secondary">
                      {pair}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Advanced Parameters */}
        <Collapsible open={advancedOpen} onOpenChange={setAdvancedOpen}>
          <Card className="glass-card">
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-muted/30 transition-colors">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-muted-foreground" />
                    Advanced Parameters
                  </div>
                  {advancedOpen ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Commission (%)</Label>
                    <Input
                      type="number"
                      step="0.001"
                      value={config.commission || 0.001}
                      onChange={(e) => handleInputChange('commission', parseFloat(e.target.value))}
                      placeholder="0.001"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Slippage (%)</Label>
                    <Input
                      type="number"
                      step="0.001"
                      value={config.slippage || 0.001}
                      onChange={(e) => handleInputChange('slippage', parseFloat(e.target.value))}
                      placeholder="0.001"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Benchmark</Label>
                  <Select
                    value={config.benchmark || ''}
                    onValueChange={(value) => handleInputChange('benchmark', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select benchmark (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="BTCUSDT">Bitcoin (BTC)</SelectItem>
                      <SelectItem value="ETHUSDT">Ethereum (ETH)</SelectItem>
                      <SelectItem value="SPY">S&P 500 (SPY)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Card>
        </Collapsible>
      </div>

      {/* Sidebar */}
      <div className="space-y-6">
        {/* Risk Management */}
        <Collapsible open={riskManagementOpen} onOpenChange={setRiskManagementOpen}>
          <Card className="glass-card">
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-muted/30 transition-colors">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-destructive" />
                    Risk Management
                  </div>
                  {riskManagementOpen ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Position Sizing</Label>
                  <Select
                    value={config.riskManagement?.positionSizing || 'percentage'}
                    onValueChange={(value) => handleRiskManagementChange('positionSizing', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fixed">Fixed Amount</SelectItem>
                      <SelectItem value="percentage">Percentage</SelectItem>
                      <SelectItem value="kelly">Kelly Criterion</SelectItem>
                      <SelectItem value="volatility">Volatility Based</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Max Position Size (%)</Label>
                  <Input
                    type="number"
                    value={config.riskManagement?.maxPositionSize || 10}
                    onChange={(e) => handleRiskManagementChange('maxPositionSize', parseFloat(e.target.value))}
                    placeholder="10"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Stop Loss (%)</Label>
                  <Input
                    type="number"
                    value={config.riskManagement?.stopLoss || ''}
                    onChange={(e) => handleRiskManagementChange('stopLoss', parseFloat(e.target.value))}
                    placeholder="Optional"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Take Profit (%)</Label>
                  <Input
                    type="number"
                    value={config.riskManagement?.takeProfit || ''}
                    onChange={(e) => handleRiskManagementChange('takeProfit', parseFloat(e.target.value))}
                    placeholder="Optional"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Max Drawdown (%)</Label>
                  <Input
                    type="number"
                    value={config.riskManagement?.maxDrawdown || 20}
                    onChange={(e) => handleRiskManagementChange('maxDrawdown', parseFloat(e.target.value))}
                    placeholder="20"
                  />
                </div>
              </CardContent>
            </CollapsibleContent>
          </Card>
        </Collapsible>

        {/* Start Backtest */}
        <Card className="glass-card">
          <CardContent className="pt-6">
            <Button
              onClick={handleStartBacktest}
              disabled={!isConfigValid || isRunning}
              className="w-full h-12 text-base font-semibold"
              size="lg"
            >
              {isRunning ? (
                <>
                  <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Running...
                </>
              ) : (
                <>
                  <Play className="h-5 w-5 mr-2" />
                  Start Backtest
                </>
              )}
            </Button>

            {!isConfigValid && (
              <p className="text-xs text-muted-foreground mt-2 text-center">
                Please select a strategy and trading pairs
              </p>
            )}
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-sm">
              <TrendingUp className="h-4 w-4" />
              Configuration Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Strategy:</span>
              <span className="font-medium">
                {config.strategy?.name || 'Not selected'}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Pairs:</span>
              <span className="font-medium">{selectedPairs.length}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Capital:</span>
              <span className="font-medium">
                ${config.initialCapital?.toLocaleString() || '10,000'}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Timeframe:</span>
              <span className="font-medium">{config.timeframe || '1h'}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

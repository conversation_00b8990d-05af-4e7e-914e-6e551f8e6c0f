import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { BacktestingResults as BacktestResults } from '@/types/backtesting';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  Shield,
  BarChart3,
  PieChart,
  Activity,
  Calendar,
  Percent
} from 'lucide-react';

interface BacktestingResultsProps {
  results?: BacktestResults;
  isLoading: boolean;
}

export const BacktestingResults: React.FC<BacktestingResultsProps> = ({
  results,
  isLoading
}) => {
  if (isLoading || !results) {
    return <BacktestingResultsSkeleton />;
  }

  if (results.status !== 'completed') {
    return (
      <Card className="glass-card">
        <CardContent className="pt-6">
          <div className="text-center py-12">
            <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Results Available</h3>
            <p className="text-muted-foreground">
              {results.status === 'running' 
                ? 'Backtest is currently running...' 
                : 'Configure and run a backtest to see results'}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatNumber = (value: number, decimals: number = 2) => {
    return value.toFixed(decimals);
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="glass-card">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Return</p>
                <p className="text-2xl font-bold text-foreground">
                  {formatCurrency(results.metrics.totalReturn)}
                </p>
                <p className={`text-sm ${results.metrics.totalReturnPercent >= 0 ? 'text-success' : 'text-destructive'}`}>
                  {formatPercent(results.metrics.totalReturnPercent)}
                </p>
              </div>
              <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                results.metrics.totalReturnPercent >= 0 
                  ? 'bg-success/10 text-success' 
                  : 'bg-destructive/10 text-destructive'
              }`}>
                {results.metrics.totalReturnPercent >= 0 ? (
                  <TrendingUp className="h-6 w-6" />
                ) : (
                  <TrendingDown className="h-6 w-6" />
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Sharpe Ratio</p>
                <p className="text-2xl font-bold text-foreground">
                  {formatNumber(results.metrics.sharpeRatio)}
                </p>
                <p className="text-sm text-muted-foreground">Risk-adjusted return</p>
              </div>
              <div className="w-12 h-12 rounded-xl bg-primary/10 text-primary flex items-center justify-center">
                <Target className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Max Drawdown</p>
                <p className="text-2xl font-bold text-destructive">
                  {formatCurrency(results.metrics.maxDrawdown)}
                </p>
                <p className="text-sm text-destructive">
                  {formatPercent(results.metrics.maxDrawdownPercent)}
                </p>
              </div>
              <div className="w-12 h-12 rounded-xl bg-destructive/10 text-destructive flex items-center justify-center">
                <Shield className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Win Rate</p>
                <p className="text-2xl font-bold text-foreground">
                  {formatNumber(results.metrics.winRate)}%
                </p>
                <p className="text-sm text-muted-foreground">
                  {results.metrics.winningTrades}/{results.metrics.totalTrades} trades
                </p>
              </div>
              <div className="w-12 h-12 rounded-xl bg-chart-1/10 text-chart-1 flex items-center justify-center">
                <PieChart className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Results */}
      <Tabs defaultValue="performance" className="space-y-6">
        <TabsList className="glass-card p-1">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="risk">Risk Analysis</TabsTrigger>
          <TabsTrigger value="trades">Trade History</TabsTrigger>
          <TabsTrigger value="monthly">Monthly Returns</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance Metrics */}
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-primary" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Total Return</p>
                    <p className="text-lg font-semibold">{formatCurrency(results.metrics.totalReturn)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Return %</p>
                    <p className={`text-lg font-semibold ${
                      results.metrics.totalReturnPercent >= 0 ? 'text-success' : 'text-destructive'
                    }`}>
                      {formatPercent(results.metrics.totalReturnPercent)}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Annualized Return</p>
                    <p className="text-lg font-semibold">{formatPercent(results.metrics.annualizedReturn)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Volatility</p>
                    <p className="text-lg font-semibold">{formatPercent(results.metrics.volatility)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Risk Metrics */}
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-destructive" />
                  Risk Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Sharpe Ratio</p>
                    <p className="text-lg font-semibold">{formatNumber(results.metrics.sharpeRatio)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Sortino Ratio</p>
                    <p className="text-lg font-semibold">{formatNumber(results.metrics.sortinoRatio)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Max Drawdown</p>
                    <p className="text-lg font-semibold text-destructive">
                      {formatPercent(results.metrics.maxDrawdownPercent)}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Calmar Ratio</p>
                    <p className="text-lg font-semibold">{formatNumber(results.metrics.calmarRatio)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Equity Curve Placeholder */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-success" />
                Equity Curve
              </CardTitle>
              <CardDescription>
                Portfolio value over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-muted/20 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">Interactive chart would be displayed here</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Showing equity curve from {results.startTime.toLocaleDateString()} to {results.endTime.toLocaleDateString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="risk" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="glass-card">
              <CardHeader>
                <CardTitle>Risk Analysis</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Value at Risk (95%)</span>
                    <span className="text-sm font-medium">-$234.56</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Expected Shortfall</span>
                    <span className="text-sm font-medium">-$345.67</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Recovery Factor</span>
                    <span className="text-sm font-medium">{formatNumber(results.metrics.recoveryFactor)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Profit Factor</span>
                    <span className="text-sm font-medium">{formatNumber(results.metrics.profitFactor)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-card">
              <CardHeader>
                <CardTitle>Drawdown Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-48 bg-muted/20 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <TrendingDown className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">Drawdown chart</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trades" className="space-y-6">
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-chart-1" />
                Trade Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 rounded-lg bg-muted/20">
                  <p className="text-2xl font-bold text-foreground">{results.metrics.totalTrades}</p>
                  <p className="text-sm text-muted-foreground">Total Trades</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-success/10">
                  <p className="text-2xl font-bold text-success">{results.metrics.winningTrades}</p>
                  <p className="text-sm text-muted-foreground">Winning</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-destructive/10">
                  <p className="text-2xl font-bold text-destructive">{results.metrics.losingTrades}</p>
                  <p className="text-sm text-muted-foreground">Losing</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-primary/10">
                  <p className="text-2xl font-bold text-primary">{formatNumber(results.metrics.averageTradeDuration)}h</p>
                  <p className="text-sm text-muted-foreground">Avg Duration</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card">
            <CardHeader>
              <CardTitle>Recent Trades</CardTitle>
              <CardDescription>Latest trading activity from the backtest</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Trade history table would be displayed here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monthly" className="space-y-6">
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-chart-2" />
                Monthly Returns
              </CardTitle>
              <CardDescription>
                Month-by-month performance breakdown
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                {results.monthlyReturns.map((month, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg text-center ${
                      month.return >= 0 
                        ? 'bg-success/10 text-success' 
                        : 'bg-destructive/10 text-destructive'
                    }`}
                  >
                    <p className="text-xs font-medium">{month.month}</p>
                    <p className="text-sm font-bold">{formatPercent(month.return)}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

const BacktestingResultsSkeleton: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="glass-card">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-8 w-24" />
                  <Skeleton className="h-3 w-16" />
                </div>
                <Skeleton className="h-12 w-12 rounded-xl" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <Card className="glass-card">
        <CardContent className="pt-6">
          <Skeleton className="h-64 w-full" />
        </CardContent>
      </Card>
    </div>
  );
};

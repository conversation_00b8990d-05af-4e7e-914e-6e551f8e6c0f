import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { BacktestingResults as BacktestResults } from '@/types/backtesting';
import {
  Search,
  Filter,
  Eye,
  Download,
  Trash2,
  Calendar,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  AlertCircle,
  Square,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface BacktestingHistoryProps {
  results: BacktestResults[];
  onSelectResult: (result: BacktestResults) => void;
}

export const BacktestingHistory: React.FC<BacktestingHistoryProps> = ({
  results,
  onSelectResult
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('date');

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Clock className="h-4 w-4 animate-pulse text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-success" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-destructive" />;
      case 'cancelled':
        return <Square className="h-4 w-4 text-muted-foreground" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-blue-500/10 text-blue-500 border-blue-500/30';
      case 'completed':
        return 'bg-success/10 text-success border-success/30';
      case 'failed':
        return 'bg-destructive/10 text-destructive border-destructive/30';
      case 'cancelled':
        return 'bg-muted/10 text-muted-foreground border-muted/30';
      default:
        return 'bg-muted/10 text-muted-foreground border-muted/30';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const filteredResults = results
    .filter(result => {
      const matchesSearch = result.config.strategy?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           result.config.tradingPairs?.some(pair => pair.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesStatus = statusFilter === 'all' || result.status === statusFilter;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.startTime).getTime() - new Date(a.startTime).getTime();
        case 'return':
          return (b.metrics?.totalReturnPercent || 0) - (a.metrics?.totalReturnPercent || 0);
        case 'sharpe':
          return (b.metrics?.sharpeRatio || 0) - (a.metrics?.sharpeRatio || 0);
        default:
          return 0;
      }
    });

  if (results.length === 0) {
    return (
      <Card className="glass-card">
        <CardContent className="pt-6">
          <div className="text-center py-12">
            <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Backtest History</h3>
            <p className="text-muted-foreground">
              Run your first backtest to see results here
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters and Search */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-primary" />
            Backtest History
          </CardTitle>
          <CardDescription>
            View and manage your previous backtesting results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by strategy or trading pair..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="running">Running</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date</SelectItem>
                <SelectItem value="return">Return</SelectItem>
                <SelectItem value="sharpe">Sharpe Ratio</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Results Table */}
      <Card className="glass-card">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-border/50">
                  <TableHead>Strategy</TableHead>
                  <TableHead>Pairs</TableHead>
                  <TableHead>Period</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Return</TableHead>
                  <TableHead className="text-right">Sharpe</TableHead>
                  <TableHead className="text-right">Max DD</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredResults.map((result) => (
                  <TableRow 
                    key={result.id} 
                    className="border-border/50 hover:bg-muted/30 cursor-pointer"
                    onClick={() => onSelectResult(result)}
                  >
                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium">{result.config.strategy?.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {result.config.timeframe} • {formatCurrency(result.config.initialCapital)}
                        </p>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {result.config.tradingPairs?.slice(0, 2).map(pair => (
                          <Badge key={pair} variant="outline" className="text-xs">
                            {pair}
                          </Badge>
                        ))}
                        {(result.config.tradingPairs?.length || 0) > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{(result.config.tradingPairs?.length || 0) - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm">{result.startTime.toLocaleDateString()}</p>
                        <p className="text-xs text-muted-foreground">
                          {result.endTime.toLocaleDateString()}
                        </p>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Badge className={getStatusColor(result.status)}>
                        {getStatusIcon(result.status)}
                        {result.status.charAt(0).toUpperCase() + result.status.slice(1)}
                      </Badge>
                    </TableCell>
                    
                    <TableCell className="text-right">
                      {result.status === 'completed' ? (
                        <div className="space-y-1">
                          <p className={`font-medium ${
                            (result.metrics?.totalReturnPercent || 0) >= 0 ? 'text-success' : 'text-destructive'
                          }`}>
                            {formatPercent(result.metrics?.totalReturnPercent || 0)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {formatCurrency(result.metrics?.totalReturn || 0)}
                          </p>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    
                    <TableCell className="text-right">
                      {result.status === 'completed' ? (
                        <span className="font-medium">
                          {(result.metrics?.sharpeRatio || 0).toFixed(2)}
                        </span>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    
                    <TableCell className="text-right">
                      {result.status === 'completed' ? (
                        <span className="font-medium text-destructive">
                          {formatPercent(result.metrics?.maxDrawdownPercent || 0)}
                        </span>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-8 w-8 p-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            onSelectResult(result);
                          }}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                            <Download className="h-4 w-4 mr-2" />
                            Export Results
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={(e) => e.stopPropagation()}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      {filteredResults.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="glass-card">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Backtests</p>
                  <p className="text-2xl font-bold text-foreground">{filteredResults.length}</p>
                </div>
                <Calendar className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg Return</p>
                  <p className="text-2xl font-bold text-foreground">
                    {formatPercent(
                      filteredResults
                        .filter(r => r.status === 'completed')
                        .reduce((sum, r) => sum + (r.metrics?.totalReturnPercent || 0), 0) /
                      Math.max(filteredResults.filter(r => r.status === 'completed').length, 1)
                    )}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-success" />
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                  <p className="text-2xl font-bold text-foreground">
                    {Math.round(
                      (filteredResults.filter(r => r.status === 'completed' && (r.metrics?.totalReturnPercent || 0) > 0).length /
                       Math.max(filteredResults.filter(r => r.status === 'completed').length, 1)) * 100
                    )}%
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-success" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

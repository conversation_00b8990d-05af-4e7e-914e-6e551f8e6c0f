import React from 'react';
import { DollarSign, Shield } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useExchange } from '@/contexts/ExchangeContext';
import { useDashboardData } from '@/hooks/useDashboardData';
import { cn } from '@/lib/utils';

export const EnvironmentBanner: React.FC = () => {
  const { environmentState, hasConnectedExchange } = useExchange();
  const { overview } = useDashboardData();

  // Only show banner if user has connected an exchange
  // Check both the exchange connection status and that the backend confirms connection
  const hasValidConnection = hasConnectedExchange() && overview?.environment !== 'disconnected';

  if (!environmentState || !hasValidConnection) {
    return null;
  }

  const config = {
    icon: DollarSign,
    text: '💰 LIVE TRADING MODE',
    description: 'Real money trading environment',
    bgClass: 'bg-gradient-to-r from-green-600/20 via-emerald-500/15 to-blue-600/20',
    borderClass: 'border-green-400/40',
    textClass: 'text-green-100',
    badgeClass: 'bg-green-500/20 text-green-100 border-green-400/50',
    glowClass: 'shadow-green-500/25',
    pulseClass: 'bg-green-400'
  };

  const IconComponent = config.icon;

  return (
    <div className={cn(
      'relative overflow-hidden border-b transition-all duration-300',
      config.bgClass,
      config.borderClass,
      config.glowClass
    )}>
      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className={cn(
          'absolute inset-0 bg-gradient-to-r animate-pulse',
          'from-green-400/10 via-emerald-400/5 to-blue-500/10'
        )}></div>
      </div>

      <div className="relative z-10 px-4 sm:px-6 py-2">
        <div className="flex items-center justify-center gap-3">
          {/* Status indicator */}
          <div className="flex items-center gap-2">
            <div className={cn(
              'w-2 h-2 rounded-full animate-pulse',
              config.pulseClass
            )}></div>
            <IconComponent className={cn('w-4 h-4', config.textClass)} />
          </div>

          {/* Main text */}
          <span className={cn(
            'text-sm font-bold tracking-wide',
            config.textClass
          )}>
            {config.text}
          </span>

          {/* Environment badge */}
          <Badge className={cn(
            'text-xs font-medium px-2 py-1 border',
            config.badgeClass
          )}>
            LIVE
          </Badge>

          {/* Warning icon for mainnet */}
          <div className="flex items-center gap-1">
            <Shield className="w-3 h-3 text-green-200" />
            <span className="text-xs text-green-200 font-medium hidden sm:inline">
              Real Money
            </span>
          </div>
        </div>

        {/* Description text for larger screens */}
        <div className="hidden md:flex justify-center mt-1">
          <span className={cn(
            'text-xs opacity-80',
            config.textClass
          )}>
            {config.description}
          </span>
        </div>
      </div>

      {/* Bottom glow effect */}
      <div className={cn(
        'absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r',
        'from-transparent via-green-400/50 to-transparent'
      )}></div>
    </div>
  );
};
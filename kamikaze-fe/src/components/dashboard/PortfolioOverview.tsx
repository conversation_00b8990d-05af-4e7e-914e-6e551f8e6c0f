import { TrendingUp, TrendingDown, DollarSign, Percent } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export function PortfolioOverview() {
  const portfolioStats = {
    totalValue: 125420.50,
    change24h: 2847.35,
    changePercent: 2.32,
    profitLoss: 8523.42,
    profitLossPercent: 7.25
  };

  return (
    <Card className="relative overflow-hidden group hover:shadow-elevated transition-all duration-500">
      <div className="absolute inset-0 bg-gradient-primary opacity-5 group-hover:opacity-10 transition-opacity duration-500"></div>
      <CardHeader className="pb-3 relative z-10">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-semibold text-foreground flex items-center gap-2">
            <div className="w-2 h-2 bg-gradient-primary rounded-full animate-pulse-glow"></div>
            Portfolio Overview
          </CardTitle>
          <Badge variant="secondary" className="bg-gradient-primary/20 text-primary border-primary/30 shadow-glow animate-pulse">
            Live
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6 relative z-10">
        {/* Main Balance */}
        <div className="text-center space-y-2 p-6 rounded-xl bg-gradient-glass backdrop-blur-sm border border-border/30 shadow-inner">
          <div className="text-5xl font-bold text-foreground mb-2 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            ${portfolioStats.totalValue.toLocaleString('en-US', { minimumFractionDigits: 2 })}
          </div>
          <div className="flex items-center justify-center gap-3 p-3 rounded-lg bg-gradient-card/50 backdrop-blur-sm">
            <div className={`p-2 rounded-full ${portfolioStats.change24h > 0 ? 'bg-gradient-profit' : 'bg-gradient-loss'} shadow-glow`}>
              {portfolioStats.change24h > 0 ? (
                <TrendingUp className="w-5 h-5 text-white" />
              ) : (
                <TrendingDown className="w-5 h-5 text-white" />
              )}
            </div>
            <div className="text-center">
              <span className={`text-lg font-bold ${portfolioStats.change24h > 0 ? 'text-profit' : 'text-loss'}`}>
                ${Math.abs(portfolioStats.change24h).toLocaleString('en-US', { minimumFractionDigits: 2 })} 
              </span>
              <div className={`text-sm font-medium ${portfolioStats.change24h > 0 ? 'text-profit' : 'text-loss'}`}>
                ({portfolioStats.changePercent > 0 ? '+' : ''}{portfolioStats.changePercent}%) 24h
              </div>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Total Value */}
          <div className="bg-gradient-glass backdrop-blur-sm rounded-xl p-5 text-center border border-border/30 shadow-glass hover:shadow-elevated transition-all duration-300 group">
            <div className="p-3 bg-gradient-primary rounded-full mx-auto mb-3 w-fit shadow-glow group-hover:scale-110 transition-transform duration-300">
              <DollarSign className="w-6 h-6 text-white" />
            </div>
            <div className="text-sm text-muted-foreground mb-1">Total Value</div>
            <div className="text-xl font-bold text-foreground">
              ${portfolioStats.totalValue.toLocaleString('en-US')}
            </div>
          </div>

          {/* P&L */}
          <div className="bg-gradient-glass backdrop-blur-sm rounded-xl p-5 text-center border border-border/30 shadow-glass hover:shadow-elevated transition-all duration-300 group">
            <div className="p-3 bg-gradient-profit rounded-full mx-auto mb-3 w-fit shadow-glow group-hover:scale-110 transition-transform duration-300">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <div className="text-sm text-muted-foreground mb-1">Total P&L</div>
            <div className="text-xl font-bold text-profit">
              +${portfolioStats.profitLoss.toLocaleString('en-US', { minimumFractionDigits: 2 })}
            </div>
          </div>

          {/* P&L Percentage */}
          <div className="bg-gradient-glass backdrop-blur-sm rounded-xl p-5 text-center border border-border/30 shadow-glass hover:shadow-elevated transition-all duration-300 group">
            <div className="p-3 bg-gradient-profit rounded-full mx-auto mb-3 w-fit shadow-glow group-hover:scale-110 transition-transform duration-300">
              <Percent className="w-6 h-6 text-white" />
            </div>
            <div className="text-sm text-muted-foreground mb-1">P&L %</div>
            <div className="text-xl font-bold text-profit">
              +{portfolioStats.profitLossPercent}%
            </div>
          </div>

          {/* Active Bots */}
          <div className="bg-gradient-glass backdrop-blur-sm rounded-xl p-5 text-center border border-border/30 shadow-glass hover:shadow-elevated transition-all duration-300 group">
            <div className="p-3 bg-gradient-primary rounded-full mx-auto mb-3 w-fit shadow-glow group-hover:scale-110 transition-transform duration-300 relative">
              <div className="w-6 h-6 flex items-center justify-center">
                <div className="w-3 h-3 bg-white rounded-full animate-pulse-glow"></div>
              </div>
            </div>
            <div className="text-sm text-muted-foreground mb-1">Active Bots</div>
            <div className="text-xl font-bold text-foreground">3</div>
          </div>
        </div>

        {/* Portfolio Allocation */}
        <div className="bg-gradient-glass backdrop-blur-sm rounded-xl p-6 border border-border/30 shadow-glass">
          <h4 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
            <div className="w-1 h-6 bg-gradient-primary rounded-full"></div>
            Portfolio Allocation
          </h4>
          <div className="space-y-4">
            {[
              { name: 'Bitcoin (BTC)', percentage: 45, value: 56439.23, color: 'bg-chart-1', gradient: 'from-chart-1 to-chart-1/70' },
              { name: 'Ethereum (ETH)', percentage: 30, value: 37626.15, color: 'bg-chart-2', gradient: 'from-chart-2 to-chart-2/70' },
              { name: 'Cardano (ADA)', percentage: 15, value: 18813.08, color: 'bg-chart-3', gradient: 'from-chart-3 to-chart-3/70' },
              { name: 'Others', percentage: 10, value: 12541.05, color: 'bg-chart-4', gradient: 'from-chart-4 to-chart-4/70' }
            ].map((asset, index) => (
              <div key={index} className="group hover:bg-gradient-card/30 rounded-lg p-3 transition-all duration-300">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className={`w-4 h-4 rounded-full ${asset.color} shadow-glow group-hover:scale-125 transition-transform duration-300`}></div>
                    <span className="text-sm font-medium text-foreground group-hover:text-primary transition-colors duration-300">{asset.name}</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-sm font-bold text-foreground">
                      ${asset.value.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                    </span>
                    <span className="text-sm font-semibold text-primary bg-primary/10 px-2 py-1 rounded-md min-w-[50px] text-center">
                      {asset.percentage}%
                    </span>
                  </div>
                </div>
                <div className="mt-2 w-full bg-secondary/30 rounded-full h-2 overflow-hidden">
                  <div 
                    className={`h-full bg-gradient-to-r ${asset.gradient} transition-all duration-500 ease-out shadow-inner`}
                    style={{ width: `${asset.percentage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
import { Clock, TrendingUp, TrendingDown, Bo<PERSON> } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

export function RecentTrades() {
  const recentTrades = [
    {
      id: 1,
      pair: "BTC/USDT",
      type: "BUY",
      amount: 0.0245,
      price: 44890.50,
      total: 1099.82,
      profit: 127.45,
      time: "2 min ago",
      bot: "Conservative Growth",
      status: "completed"
    },
    {
      id: 2,
      pair: "ETH/USDT",
      type: "SELL",
      amount: 1.5,
      price: 2875.30,
      total: 4312.95,
      profit: 243.80,
      time: "8 min ago",
      bot: "Aggressive Scalper",
      status: "completed"
    },
    {
      id: 3,
      pair: "ADA/USDT",
      type: "BUY",
      amount: 1250,
      price: 0.478,
      total: 597.50,
      profit: -15.25,
      time: "15 min ago",
      bot: "Balanced Trader",
      status: "completed"
    },
    {
      id: 4,
      pair: "BTC/USDT",
      type: "SELL",
      amount: 0.0189,
      price: 45120.00,
      total: 852.77,
      profit: 89.30,
      time: "22 min ago",
      bot: "Conservative Growth",
      status: "completed"
    },
    {
      id: 5,
      pair: "SOL/USDT",
      type: "BUY",
      amount: 12.5,
      price: 102.35,
      total: 1279.38,
      profit: 0,
      time: "1 min ago",
      bot: "Aggressive Scalper",
      status: "pending"
    }
  ];

  const getTypeColor = (type: string) => {
    return type === "BUY" 
      ? "text-profit bg-profit/10 border-profit/20" 
      : "text-destructive bg-destructive/10 border-destructive/20";
  };

  const getProfitColor = (profit: number) => {
    if (profit > 0) return "text-profit";
    if (profit < 0) return "text-loss";
    return "text-muted-foreground";
  };

  return (
    <Card className="bg-card shadow-card border-border">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <Clock className="w-5 h-5 text-primary" />
            Recent Trades
          </CardTitle>
          <Button size="sm" variant="outline" className="border-border text-foreground">
            View All
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {recentTrades.map((trade) => (
          <div key={trade.id} className="bg-secondary/50 rounded-lg p-3">
            {/* Trade Header */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-3">
                <Badge variant="secondary" className={getTypeColor(trade.type)}>
                  {trade.type}
                </Badge>
                <div>
                  <div className="font-medium text-foreground">{trade.pair}</div>
                  <div className="text-xs text-muted-foreground flex items-center gap-1">
                    <Bot className="w-3 h-3" />
                    {trade.bot}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-muted-foreground">{trade.time}</div>
                <Badge 
                  variant="secondary" 
                  className={trade.status === "completed" 
                    ? "bg-profit/10 text-profit border-profit/20" 
                    : "bg-warning/10 text-warning border-warning/20"
                  }
                >
                  {trade.status}
                </Badge>
              </div>
            </div>

            {/* Trade Details */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
              <div>
                <div className="text-muted-foreground">Amount</div>
                <div className="font-medium text-foreground">
                  {trade.amount.toLocaleString()}
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">Price</div>
                <div className="font-medium text-foreground">
                  ${trade.price.toLocaleString()}
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">Total</div>
                <div className="font-medium text-foreground">
                  ${trade.total.toLocaleString()}
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">P&L</div>
                <div className={`font-medium flex items-center gap-1 ${getProfitColor(trade.profit)}`}>
                  {trade.profit > 0 ? (
                    <TrendingUp className="w-3 h-3" />
                  ) : trade.profit < 0 ? (
                    <TrendingDown className="w-3 h-3" />
                  ) : null}
                  {trade.profit !== 0 && (trade.profit > 0 ? '+' : '')}
                  ${Math.abs(trade.profit).toLocaleString()}
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Trade Summary */}
        <div className="bg-gradient-card rounded-lg p-4 border border-border/50">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-sm text-muted-foreground">Today's Trades</div>
              <div className="text-lg font-semibold text-foreground">24</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Success Rate</div>
              <div className="text-lg font-semibold text-profit">78%</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Total P&L</div>
              <div className="text-lg font-semibold text-profit">+$445.30</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
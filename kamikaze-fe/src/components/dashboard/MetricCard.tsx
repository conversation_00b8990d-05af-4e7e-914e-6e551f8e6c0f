import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeType?: 'percentage' | 'absolute';
  trend?: 'up' | 'down' | 'neutral';
  icon?: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  className?: string;
  subtitle?: string;
  loading?: boolean;
}

export function MetricCard({
  title,
  value,
  change,
  changeType = 'percentage',
  trend,
  icon,
  variant = 'default',
  className,
  subtitle,
  loading = false,
}: MetricCardProps) {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4" />;
      case 'down':
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <Minus className="h-4 w-4" />;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-success';
      case 'down':
        return 'text-destructive';
      default:
        return 'text-muted-foreground';
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'border-success/30 bg-success/5';
      case 'warning':
        return 'border-warning/30 bg-warning/5';
      case 'destructive':
        return 'border-destructive/30 bg-destructive/5';
      default:
        return '';
    }
  };

  if (loading) {
    return (
      <Card className={cn('glass-card', getVariantStyles(), className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="h-4 w-24 bg-muted/30 rounded animate-pulse" />
            {icon && (
              <div className="h-5 w-5 bg-muted/30 rounded animate-pulse" />
            )}
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="h-8 w-32 bg-muted/30 rounded animate-pulse" />
            <div className="h-4 w-20 bg-muted/30 rounded animate-pulse" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('glass-card hover:shadow-elevated transition-all duration-300', getVariantStyles(), className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          {icon && (
            <div className="text-muted-foreground opacity-70">
              {icon}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="text-2xl font-bold text-foreground">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </div>
          
          {(change !== undefined || subtitle) && (
            <div className="flex items-center justify-between">
              {change !== undefined && (
                <div className={cn('flex items-center gap-1 text-sm', getTrendColor())}>
                  {getTrendIcon()}
                  <span>
                    {changeType === 'percentage' && change > 0 && '+'}
                    {changeType === 'percentage' ? change.toFixed(5) : change}
                    {changeType === 'percentage' && '%'}
                  </span>
                </div>
              )}
              
              {subtitle && (
                <span className="text-xs text-muted-foreground">
                  {subtitle}
                </span>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Preset metric card variants for common use cases
export function ProfitLossCard({ profit, change, ...props }: { profit: number; change?: number } & Omit<MetricCardProps, 'value' | 'trend' | 'variant'>) {
  const isProfit = profit >= 0;
  return (
    <MetricCard
      value={`$${Math.abs(profit).toLocaleString()}`}
      change={change}
      trend={isProfit ? 'up' : 'down'}
      variant={isProfit ? 'success' : 'destructive'}
      {...props}
    />
  );
}

export function PercentageCard({ percentage, ...props }: { percentage: number } & Omit<MetricCardProps, 'value' | 'trend' | 'changeType'>) {
  const trend = percentage > 0 ? 'up' : percentage < 0 ? 'down' : 'neutral';
  return (
    <MetricCard
      value={`${percentage > 0 ? '+' : ''}${percentage.toFixed(5)}%`}
      trend={trend}
      changeType="percentage"
      {...props}
    />
  );
}

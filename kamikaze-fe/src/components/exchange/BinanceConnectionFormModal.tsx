import React, { useState, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { ExchangeCredentials } from '@/types/exchange';
import {
  validateApiKey,
  validateSecretKey,
  getApiKeyValidationMessage,
  getSecretKeyValidationMessage
} from '@/lib/exchangeUtils';
import {
  Eye,
  EyeOff,
  CheckCircle2,
  Info,
  Loader2,
  Zap,
  Lock,
  Key,
  Shield,
  AlertCircle,
  Wifi,
  TrendingUp
} from 'lucide-react';

// Enhanced validation schema
const binanceCredentialsSchema = z.object({
  apiKey: z
    .string()
    .min(1, 'API Key is required')
    .refine((val) => {
      return val && val.length > 0;
    }, {
      message: 'Invalid API Key format. Please enter a valid Binance API key'
    }),
  secretKey: z
    .string()
    .min(1, 'Secret Key is required')
    .refine((val) => {
      return val && val.length > 0;
    }, {
      message: 'Invalid Secret Key format. Please enter a valid Binance secret key'
    })
});

type BinanceCredentialsForm = z.infer<typeof binanceCredentialsSchema>;

interface BinanceConnectionFormModalProps {
  onSubmit: (credentials: ExchangeCredentials) => Promise<boolean>;
  onCancel: () => void;
  isConnecting: boolean;
  environment?: 'mainnet';
}

export const BinanceConnectionFormModal: React.FC<BinanceConnectionFormModalProps> = ({
  onSubmit,
  onCancel,
  isConnecting,
  environment = 'mainnet'
}) => {
  const [showSecretKey, setShowSecretKey] = useState(false);
  const [testingConnection, setTestingConnection] = useState(false);
  const [connectionProgress, setConnectionProgress] = useState(0);
  const [validationMessages, setValidationMessages] = useState<{
    apiKey?: string;
    secretKey?: string;
  }>({});

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid },
    setValue,
    getValues,
    trigger
  } = useForm<BinanceCredentialsForm>({
    resolver: zodResolver(binanceCredentialsSchema),
    mode: 'onChange',
    defaultValues: {
      apiKey: '',
      secretKey: ''
    }
  });

  const apiKey = watch('apiKey');
  const secretKey = watch('secretKey');

  // Enhanced form validation state
  const isFormValid = useMemo(() => {
    const hasApiKey = apiKey && apiKey.length > 0;
    const hasSecretKey = secretKey && secretKey.length > 0;
    const apiKeyValid = hasApiKey && validateApiKey(apiKey, false);
    const secretKeyValid = hasSecretKey && validateSecretKey(secretKey, false);

    return apiKeyValid && secretKeyValid;
  }, [apiKey, secretKey, isValid]);

  // Real-time validation feedback
  useEffect(() => {
    if (apiKey && apiKey.length > 0) {
      const isApiKeyValid = validateApiKey(apiKey, false);
      if (!isApiKeyValid) {
        setValidationMessages(prev => ({
          ...prev,
          apiKey: getApiKeyValidationMessage(apiKey, false)
        }));
      } else {
        setValidationMessages(prev => ({
          ...prev,
          apiKey: ''
        }));
      }
    } else {
      setValidationMessages(prev => ({
        ...prev,
        apiKey: ''
      }));
    }

    if (secretKey && secretKey.length > 0) {
      const isSecretKeyValid = validateSecretKey(secretKey, false);
      if (!isSecretKeyValid) {
        setValidationMessages(prev => ({
          ...prev,
          secretKey: getSecretKeyValidationMessage(secretKey, false)
        }));
      } else {
        setValidationMessages(prev => ({
          ...prev,
          secretKey: ''
        }));
      }
    } else {
      setValidationMessages(prev => ({
        ...prev,
        secretKey: ''
      }));
    }
  }, [apiKey, secretKey]);

  const handleFormSubmit = async (data: BinanceCredentialsForm) => {
    setTestingConnection(true);
    setConnectionProgress(0);

    // Simulate connection progress
    const progressInterval = setInterval(() => {
      setConnectionProgress(prev => {
        const newProgress = Math.min(prev + 10, 90);
        return newProgress;
      });
    }, 200);

    try {
      const success = await onSubmit(data as ExchangeCredentials);
      setConnectionProgress(100);

      if (!success) {
        setTestingConnection(false);
        setConnectionProgress(0);
      } else {
        setTimeout(() => {
          setTestingConnection(false);
          setConnectionProgress(0);
        }, 1000);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setTestingConnection(false);
      setConnectionProgress(0);
    } finally {
      clearInterval(progressInterval);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      {/* Header Section */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-6">
          <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/30 border border-primary/30 flex items-center justify-center backdrop-blur-sm shadow-lg">
            <TrendingUp className="h-8 w-8 text-primary" />
          </div>
        </div>
        <h2 className="text-2xl font-bold text-foreground mb-2">Connect Your Exchange</h2>
        <p className="text-muted-foreground">Enter your Binance API credentials to start automated trading</p>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-8">
        {/* API Key Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/30 border border-blue-400/30 flex items-center justify-center backdrop-blur-sm">
              <Key className="h-5 w-5 text-blue-400" />
            </div>
            <div className="flex-1">
              <Label htmlFor="apiKey" className="text-base font-semibold text-foreground block mb-1">
                API Key
              </Label>
              <p className="text-sm text-muted-foreground">
                Your Binance API key (exactly 64 characters)
              </p>
            </div>
          </div>

          <div className="relative">
            <Input
              id="apiKey"
              type="text"
              placeholder="Enter your Binance API Key"
              {...register('apiKey')}
              className={`h-12 text-base pr-12 transition-all duration-200 rounded-xl border-2 ${
                errors.apiKey
                  ? 'border-red-500/50 bg-red-50/50 text-red-900 placeholder:text-red-400'
                  : apiKey && !validationMessages.apiKey
                    ? 'border-green-500/50 bg-green-50/50 text-green-900 placeholder:text-green-400'
                    : 'border-border bg-background/50 hover:border-primary/30 focus:border-primary/50'
              } focus:outline-none focus:ring-4 focus:ring-primary/10`}
            />

            {/* Validation Icon */}
            <div className="absolute right-4 top-1/2 -translate-y-1/2">
              {!errors.apiKey && apiKey && !validationMessages.apiKey && (
                <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                </div>
              )}
              {validationMessages.apiKey && (
                <div className="w-8 h-8 rounded-full bg-amber-500/20 flex items-center justify-center">
                  <AlertCircle className="h-5 w-5 text-amber-500" />
                </div>
              )}
            </div>
          </div>

          {/* Validation Message */}
          {validationMessages.apiKey && (
            <div className="flex items-start gap-3 p-4 rounded-xl bg-amber-50/50 border border-amber-200/50">
              <Info className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-amber-700">{validationMessages.apiKey}</p>
            </div>
          )}
        </div>

        {/* Secret Key Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500/20 to-purple-600/30 border border-purple-400/30 flex items-center justify-center backdrop-blur-sm">
              <Lock className="h-5 w-5 text-purple-400" />
            </div>
            <div className="flex-1">
              <Label htmlFor="secretKey" className="text-base font-semibold text-foreground block mb-1">
                Secret Key
              </Label>
              <p className="text-sm text-muted-foreground flex items-center gap-2">
                <Shield className="h-3 w-3" />
                Keep secure and private
              </p>
            </div>
          </div>

          <div className="relative">
            <Input
              id="secretKey"
              type={showSecretKey ? "text" : "password"}
              placeholder="Enter your Binance Secret Key"
              {...register('secretKey')}
              className={`h-12 text-base pr-20 transition-all duration-200 rounded-xl border-2 ${
                errors.secretKey
                  ? 'border-red-500/50 bg-red-50/50 text-red-900 placeholder:text-red-400'
                  : secretKey && !validationMessages.secretKey
                    ? 'border-green-500/50 bg-green-50/50 text-green-900 placeholder:text-green-400'
                    : 'border-border bg-background/50 hover:border-primary/30 focus:border-primary/50'
              } focus:outline-none focus:ring-4 focus:ring-primary/10`}
            />

            {/* Validation Icon and Toggle */}
            <div className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center gap-2">
              {!errors.secretKey && secretKey && !validationMessages.secretKey && (
                <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                </div>
              )}
              {validationMessages.secretKey && (
                <div className="w-8 h-8 rounded-full bg-amber-500/20 flex items-center justify-center">
                  <AlertCircle className="h-5 w-5 text-amber-500" />
                </div>
              )}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-muted/50 rounded-lg transition-colors"
                onClick={() => setShowSecretKey(!showSecretKey)}
              >
                {showSecretKey ? (
                  <EyeOff className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <Eye className="h-4 w-4 text-muted-foreground" />
                )}
              </Button>
            </div>
          </div>

          {/* Validation Message */}
          {validationMessages.secretKey && (
            <div className="flex items-start gap-3 p-4 rounded-xl bg-amber-50/50 border border-amber-200/50">
              <Info className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-amber-700">{validationMessages.secretKey}</p>
            </div>
          )}
        </div>

        <Separator className="my-8" />

        {/* Connection Progress */}
        {testingConnection && (
          <div className="space-y-6 py-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 border border-primary/30 flex items-center justify-center animate-pulse">
                <Wifi className="h-6 w-6 text-primary" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-foreground">Connecting to Binance</h3>
                <p className="text-sm text-muted-foreground">Testing your credentials...</p>
              </div>
              <Badge variant="secondary" className="text-lg font-bold px-3 py-1">
                {connectionProgress}%
              </Badge>
            </div>

            <div className="space-y-4">
              <Progress
                value={connectionProgress}
                className="h-3 bg-muted/30 rounded-full"
              />

              <div className="text-center p-4 rounded-xl bg-primary/5 border border-primary/10">
                <p className="text-sm font-medium text-primary">
                  {connectionProgress < 30 && "🔍 Validating credentials..."}
                  {connectionProgress >= 30 && connectionProgress < 60 && "🌐 Connecting to Binance..."}
                  {connectionProgress >= 60 && connectionProgress < 90 && "🔑 Testing API permissions..."}
                  {connectionProgress >= 90 && "✨ Finalizing connection..."}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex gap-4 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isConnecting || testingConnection}
            className="px-8 h-12 text-base font-medium rounded-xl transition-all duration-200"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={!isFormValid || isConnecting || testingConnection}
            className={`flex-1 h-12 text-base font-semibold rounded-xl transition-all duration-200 ${
              isFormValid && !testingConnection
                ? 'bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl'
                : 'opacity-50 cursor-not-allowed'
            }`}
          >
            {testingConnection ? (
              <>
                <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                Connecting...
              </>
            ) : isFormValid ? (
              <>
                <Zap className="h-5 w-5 mr-2" />
                Connect Exchange
              </>
            ) : (
              <>
                <Lock className="h-5 w-5 mr-2" />
                Enter Credentials
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
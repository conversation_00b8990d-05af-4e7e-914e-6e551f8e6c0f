import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ExchangeInfo, ExchangeConnection } from '@/types/exchange';
import { formatConnectionStatus, getExchangeLogoComponent } from '@/lib/exchangeUtils';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle,
  Zap,
  TrendingUp,
  Shield,
  BarChart3
} from 'lucide-react';

interface ExchangeCardProps {
  exchangeInfo: ExchangeInfo;
  connection: ExchangeConnection;
  onConnect: () => void;
  onDisconnect: () => void;
  isConnecting: boolean;
}

const getStatusIcon = (status: ExchangeConnection['status']) => {
  switch (status) {
    case 'connected':
      return <CheckCircle className="h-4 w-4 text-success" />;
    case 'connecting':
      return <Clock className="h-4 w-4 text-warning animate-spin" />;
    case 'error':
      return <XCircle className="h-4 w-4 text-destructive" />;
    default:
      return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
  }
};

const getFeatureIcon = (feature: string) => {
  switch (feature.toLowerCase()) {
    case 'spot trading':
      return <TrendingUp className="h-3.5 w-3.5" />;
    case 'futures trading':
      return <BarChart3 className="h-3.5 w-3.5" />;
    case 'real-time data':
      return <Zap className="h-3.5 w-3.5" />;
    case 'advanced orders':
      return <Shield className="h-3.5 w-3.5" />;
    default:
      return <CheckCircle className="h-3.5 w-3.5" />;
  }
};

export const ExchangeCard: React.FC<ExchangeCardProps> = ({
  exchangeInfo,
  connection,
  onConnect,
  onDisconnect,
  isConnecting
}) => {
  const statusInfo = formatConnectionStatus(connection.status);
  const isConnected = connection.status === 'connected';
  const canConnect = exchangeInfo.isAvailable && !isConnecting;
  const LogoComponent = getExchangeLogoComponent(exchangeInfo.logo);

  return (
    <Card className={`glass-card hover:shadow-elevated transition-all duration-300 h-full flex flex-col ${
      isConnected ? 'border-success/50 bg-success/5' :
      exchangeInfo.comingSoon ? 'border-muted/30 bg-muted/5' :
      'border-card-border/60'
    }`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2.5">
            <div className="relative">
              <div className="transform transition-transform duration-300 group-hover:scale-110">
                {LogoComponent ? (
                  <LogoComponent size={38} className="drop-shadow-sm" />
                ) : (
                  <div className="w-10 h-10 rounded-lg bg-muted/20 flex items-center justify-center">
                    <span className="text-base font-bold text-muted-foreground">
                      {exchangeInfo.displayName.charAt(0)}
                    </span>
                  </div>
                )}
              </div>
              {isConnected && (
                <div className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-success rounded-full animate-pulse"></div>
              )}
            </div>
            <div>
              <h3 className="text-lg font-bold text-foreground">
                {exchangeInfo.displayName}
              </h3>
              <div className="flex items-center gap-1.5 mt-0.5">
                {getStatusIcon(connection.status)}
                <span className={`text-xs font-medium ${statusInfo.color}`}>
                  {statusInfo.text}
                </span>
              </div>
            </div>
          </div>
          
          {exchangeInfo.comingSoon ? (
            <Badge variant="secondary" className="bg-muted/20 text-muted-foreground text-xs px-2 py-0.5">
              Coming Soon
            </Badge>
          ) : isConnected ? (
            <Badge className="bg-success/10 text-success border-success/30 text-xs px-2 py-0.5">
              Active
            </Badge>
          ) : (
            <Badge className="text-xs px-3 py-1 backdrop-blur-lg bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-400 border border-green-400/40 shadow-lg shadow-green-500/20">
              Available
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col">
        <div className="space-y-3 flex-1">
          <p className="text-muted-foreground text-xs leading-relaxed">
            {exchangeInfo.description}
          </p>

          {/* Features */}
          <div className="space-y-1.5">
            <h4 className="text-xs font-semibold text-foreground">Features</h4>
            <div className="grid grid-cols-2 gap-1.5">
              {exchangeInfo.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-1.5 text-xs text-muted-foreground">
                  {getFeatureIcon(feature)}
                  <span>{feature}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Connection Error */}
          {connection.error && (
            <div className="p-2.5 rounded-lg bg-destructive/10 border border-destructive/20">
              <p className="text-xs text-destructive font-medium">
                Connection Error
              </p>
              <p className="text-xs text-destructive/80 mt-0.5">
                {connection.error}
              </p>
            </div>
          )}

          {/* Last Connected */}
          {connection.lastConnected && (
            <div className="text-xs text-muted-foreground">
              Last connected: {new Date(connection.lastConnected).toLocaleString()}
            </div>
          )}
        </div>

        {/* Action Buttons - Always at bottom */}
        <div className="pt-3 mt-auto">
          {exchangeInfo.comingSoon ? (
            <Button
              variant="outline"
              className="w-full h-8 text-xs"
              disabled
            >
              Coming Soon
            </Button>
          ) : isConnected ? (
            <Button
              variant="outline"
              className="w-full h-8 text-xs"
              onClick={onDisconnect}
            >
              Disconnect
            </Button>
          ) : (
            <Button
              className="w-full h-8 text-xs"
              onClick={onConnect}
              disabled={!canConnect}
            >
              {isConnecting ? (
                <>
                  <Clock className="h-3.5 w-3.5 mr-1.5 animate-spin" />
                  Connecting...
                </>
              ) : (
                'Connect Exchange'
              )}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

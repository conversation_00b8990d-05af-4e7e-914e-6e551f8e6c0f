import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ExchangeCredentials } from '@/types/exchange';
import {
  validateApiKey,
  validateSecretKey,
  getEnvironmentStyles,
  BINANCE_ENVIRONMENTS,
  BINANCE_PERMISSIONS,
  getApiKeyValidationMessage,
  getSecretKeyValidationMessage
} from '@/lib/exchangeUtils';
import {
  Eye,
  EyeOff,
  Shield,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  Info,
  TestTube,
  DollarSign,
  Loader2,
  Copy,
  Check,
  AlertCircle,
  HelpCircle,
  Zap,
  Lock,
  Globe,
  BookOpen,
  Settings,
  Lightbulb,
  Key,
  Sparkles,
  Rocket,
  Star,
  TrendingUp,
  Wifi,
  CheckCircle2,
  X
} from 'lucide-react';

// Enhanced validation schema with environment-specific rules
const binanceCredentialsSchema = z.object({
  apiKey: z
    .string()
    .min(1, 'API Key is required')
    .refine((val, ctx) => {
      const isTestnet = ctx.parent?.testnet || false;
      return validateApiKey(val, isTestnet);
    }, {
      message: 'Invalid API Key format. Testnet keys: 32-128 chars, Mainnet keys: exactly 64 chars'
    }),
  secretKey: z
    .string()
    .min(1, 'Secret Key is required')
    .refine((val, ctx) => {
      const isTestnet = ctx.parent?.testnet || false;
      return validateSecretKey(val, isTestnet);
    }, {
      message: 'Invalid Secret Key format. Must be base64-encoded string'
    }),
  testnet: z.boolean().default(false)
});

type BinanceCredentialsForm = z.infer<typeof binanceCredentialsSchema>;

interface BinanceConnectionFormProps {
  onSubmit: (credentials: ExchangeCredentials) => Promise<boolean>;
  onCancel: () => void;
  isConnecting: boolean;
  isModal?: boolean; // Add prop to detect modal usage
}

export const BinanceConnectionForm: React.FC<BinanceConnectionFormProps> = ({
  onSubmit,
  onCancel,
  isConnecting,
  isModal = false
}) => {
  const [showSecretKey, setShowSecretKey] = useState(false);
  const [testingConnection, setTestingConnection] = useState(false);
  const [showMainnetWarning, setShowMainnetWarning] = useState(false);
  const [connectionProgress, setConnectionProgress] = useState(0);
  const [validationMessages, setValidationMessages] = useState<{
    apiKey?: string;
    secretKey?: string;
  }>({});

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid },
    setValue,
    getValues,
    trigger
  } = useForm<BinanceCredentialsForm>({
    resolver: zodResolver(binanceCredentialsSchema),
    mode: 'onChange',
    defaultValues: {
      apiKey: '',
      secretKey: '',
      testnet: true // Default to testnet for safety
    }
  });

  const testnet = watch('testnet');
  const apiKey = watch('apiKey');
  const secretKey = watch('secretKey');

  // Real-time validation feedback
  useEffect(() => {
    const apiKeyMessage = getApiKeyValidationMessage(apiKey, testnet);
    const secretKeyMessage = getSecretKeyValidationMessage(secretKey, testnet);

    setValidationMessages({
      apiKey: apiKeyMessage,
      secretKey: secretKeyMessage
    });
  }, [apiKey, secretKey, testnet]);

  // Enhanced environment styles with rich colors
  const envStyles = getEnvironmentStyles(testnet);
  const currentEnv = testnet ? BINANCE_ENVIRONMENTS.TESTNET : BINANCE_ENVIRONMENTS.MAINNET;

  // Use website theme colors consistently
  const isTestnet = testnet;

  // Define base classes using website theme colors
  const bgGradient = "glass-page";

  const cardGradient = "glass-card";

  const iconBg = "bg-gradient-to-br from-primary to-primary/80";

  const buttonGradient = "bg-primary hover:bg-primary/90";

  const primaryGradient = "from-primary to-primary/80";

  const glowShadow = "shadow-primary/25";

  const handleFormSubmit = async (data: BinanceCredentialsForm) => {
    // Show mainnet warning if switching to mainnet
    if (!testnet && !showMainnetWarning) {
      setShowMainnetWarning(true);
      return;
    }

    setTestingConnection(true);
    setConnectionProgress(0);

    // Simulate connection progress
    const progressInterval = setInterval(() => {
      setConnectionProgress(prev => Math.min(prev + 10, 90));
    }, 200);

    try {
      const success = await onSubmit(data);
      setConnectionProgress(100);

      if (!success) {
        setTestingConnection(false);
        setConnectionProgress(0);
      }
    } catch (error) {
      setTestingConnection(false);
      setConnectionProgress(0);
    } finally {
      clearInterval(progressInterval);
    }
  };

  const handleTestnetToggle = (checked: boolean) => {
    setValue('testnet', checked, { shouldValidate: true });
    // Clear form when switching environments
    setValue('apiKey', '');
    setValue('secretKey', '');
    trigger(); // Re-validate form
  };

  const handleMainnetConfirm = () => {
    setShowMainnetWarning(false);
    handleFormSubmit(getValues());
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // Conditional rendering based on modal context
  if (isModal) {
    // Modal version - simplified layout
    return (
      <div className="space-y-6">
        {/* Simplified Header for Modal */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className={`p-3 rounded-xl ${iconBg} text-white`}>
              <div className="text-2xl">
                {currentEnv.icon}
              </div>
            </div>
            <div>
              <h2 className="text-2xl font-bold text-foreground">
                Connect to Binance
              </h2>
              <Badge
                variant="outline"
                className={`bg-gradient-to-r ${primaryGradient} text-white border-0 shadow-md mt-2`}
              >
                <Sparkles className="h-3 w-3 mr-1" />
                {currentEnv.name}
              </Badge>
            </div>
          </div>
        </div>

        {/* Modal Form Content */}
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Environment Toggle */}
          <div className="p-4 rounded-xl glass-card">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${iconBg} text-white`}>
                    {currentEnv.icon}
                  </div>
                  <div>
                    <Label className="text-lg font-semibold text-foreground">
                      Environment Selection
                    </Label>
                    {currentEnv.warningLevel === 'high' && (
                      <Badge variant="destructive" className="text-xs animate-pulse mt-1">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Real Funds - High Risk
                      </Badge>
                    )}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground max-w-md">
                  {testnet
                    ? "Testing environment for development and strategy validation"
                    : "Live trading environment with real funds - Use with extreme caution as losses are permanent"
                  }
                </p>
                <div className="flex items-center gap-2 text-xs">
                  <div className="w-2 h-2 rounded-full bg-primary animate-pulse"></div>
                  <span className="font-medium text-foreground">
                    {testnet ? 'Testnet Active' : 'Mainnet Active'}
                  </span>
                </div>
              </div>
              <div className="flex flex-col items-center gap-3">
                <Switch
                  checked={!testnet}
                  onCheckedChange={(checked) => handleTestnetToggle(!checked)}
                  className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-primary/60"
                />
                <span className="text-xs font-medium text-muted-foreground">
                  {testnet ? 'Switch to Live' : 'Switch to Test'}
                </span>
              </div>
            </div>
          </div>

          {/* API Key Input */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${iconBg} text-white`}>
                <Key className="h-4 w-4" />
              </div>
              <div>
                <Label htmlFor="apiKey" className="text-lg font-semibold text-foreground">
                  API Key
                </Label>
                <div className="flex items-center gap-2 mt-1">
                  {testnet && (
                    <Badge variant="outline" className={`text-xs bg-gradient-to-r ${primaryGradient} text-white border-0`}>
                      <TestTube className="h-3 w-3 mr-1" />
                      Testnet
                    </Badge>
                  )}
                  <span className="text-xs text-gray-500">
                    {testnet ? '32-128 characters' : 'Exactly 64 characters'}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="relative">
                <Input
                  id="apiKey"
                  type="text"
                  placeholder={testnet ? "Enter your Binance Testnet API Key" : "Enter your Binance API Key"}
                  {...register('apiKey')}
                  className="h-12 text-base pr-12 glass-card border-card-border/60 focus:border-primary/50 transition-all duration-300"
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-2">
                  {!errors.apiKey && apiKey && !validationMessages.apiKey && (
                    <div className="flex items-center gap-1">
                      <CheckCircle2 className="h-5 w-5 text-green-500 animate-pulse" />
                      <span className="text-xs text-green-600 font-medium">Valid</span>
                    </div>
                  )}
                  {validationMessages.apiKey && (
                    <AlertCircle className="h-5 w-5 text-warning" />
                  )}
                </div>
              </div>

              {/* Enhanced validation feedback */}
              {validationMessages.apiKey && (
                <div className="p-3 rounded-lg bg-warning/10 border border-warning/30">
                  <p className="text-sm text-warning-foreground flex items-center gap-2">
                    <Info className="h-4 w-4" />
                    {validationMessages.apiKey}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Secret Key Input */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${iconBg} text-white`}>
                <Lock className="h-4 w-4" />
              </div>
              <div>
                <Label htmlFor="secretKey" className="text-lg font-semibold text-foreground">
                  Secret Key
                </Label>
                <div className="flex items-center gap-2 mt-1">
                  {testnet && (
                    <Badge variant="outline" className={`text-xs bg-gradient-to-r ${primaryGradient} text-white border-0`}>
                      <TestTube className="h-3 w-3 mr-1" />
                      Testnet
                    </Badge>
                  )}
                  <span className="text-xs text-gray-500">
                    Keep this secure and private
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="relative">
                <Input
                  id="secretKey"
                  type={showSecretKey ? "text" : "password"}
                  placeholder={testnet ? "Enter your Binance Testnet Secret Key" : "Enter your Binance Secret Key"}
                  {...register('secretKey')}
                  className="h-12 text-base pr-20 glass-card border-card-border/60 focus:border-primary/50 transition-all duration-300"
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-2">
                  {!errors.secretKey && secretKey && !validationMessages.secretKey && (
                    <div className="flex items-center gap-1">
                      <CheckCircle2 className="h-5 w-5 text-green-500 animate-pulse" />
                      <span className="text-xs text-green-600 font-medium">Valid</span>
                    </div>
                  )}
                  {validationMessages.secretKey && (
                    <AlertCircle className="h-5 w-5 text-warning" />
                  )}
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-gray-100 rounded-lg transition-colors"
                    onClick={() => setShowSecretKey(!showSecretKey)}
                  >
                    {showSecretKey ? (
                      <EyeOff className="h-4 w-4 text-gray-600" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-600" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Enhanced validation feedback */}
              {validationMessages.secretKey && (
                <div className="p-3 rounded-lg bg-warning/10 border border-warning/30">
                  <p className="text-sm text-warning-foreground flex items-center gap-2">
                    <Info className="h-4 w-4" />
                    {validationMessages.secretKey}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Connection Progress */}
          {testingConnection && (
            <div className="p-4 rounded-xl glass-card">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${iconBg} text-white animate-pulse`}>
                      <Wifi className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">Testing Connection</p>
                      <p className="text-sm text-gray-600">{connectionProgress}% Complete</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-2xl font-bold bg-gradient-to-r ${primaryGradient} bg-clip-text text-transparent`}>
                      {connectionProgress}%
                    </div>
                  </div>
                </div>

                <Progress
                  value={connectionProgress}
                  className={`h-3 bg-gray-200 [&>div]:bg-gradient-to-r [&>div]:${primaryGradient} [&>div]:transition-all [&>div]:duration-500`}
                />

                <div className="text-center">
                  <p className="text-sm font-medium text-gray-700">
                    {connectionProgress < 30 && "🔍 Validating credentials..."}
                    {connectionProgress >= 30 && connectionProgress < 60 && "🌐 Connecting to Binance..."}
                    {connectionProgress >= 60 && connectionProgress < 90 && "🔑 Testing API permissions..."}
                    {connectionProgress >= 90 && "✨ Finalizing connection..."}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isConnecting || testingConnection}
              className="flex-1"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!isValid || isConnecting || testingConnection}
              className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              {testingConnection ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Testing Connection...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Connect Exchange
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    );
  }

  // Full page version - original layout
  return (
    <>
      {/* Enhanced Background with Website Theme */}
      <div className="min-h-screen p-6 bg-background">
        <div className="w-full max-w-5xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className={`p-4 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-300 ${iconBg} ${glowShadow}`}>
                <div className="text-4xl text-white">
                  {currentEnv.icon}
                </div>
              </div>
              <div className="text-left">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  Connect to Binance
                </h1>
                <div className="flex items-center gap-2 mt-2">
                  <Badge
                    variant="outline"
                    className={`bg-gradient-to-r ${primaryGradient} text-white border-0 shadow-md`}
                  >
                    <Sparkles className="h-3 w-3 mr-1" />
                    {currentEnv.name}
                  </Badge>
                  {currentEnv.warningLevel === 'high' && (
                    <Badge variant="destructive" className="animate-pulse">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Live Trading
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Set up your API credentials to start trading with advanced features.
            </p>
          </div>

          {/* Main Card with Enhanced Design */}
          <Card className="glass-card shadow-2xl">
            <CardHeader className="space-y-6 pb-8">


              {/* Feature Highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3 p-4 rounded-xl glass-card">
                  <div className={`p-2 rounded-lg ${iconBg} text-white`}>
                    <Rocket className="h-4 w-4" />
                  </div>
                  <div>
                    <p className="font-medium text-foreground">Fast Setup</p>
                    <p className="text-xs text-muted-foreground">Connect in minutes</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 rounded-xl glass-card">
                  <div className={`p-2 rounded-lg ${iconBg} text-white`}>
                    <Shield className="h-4 w-4" />
                  </div>
                  <div>
                    <p className="font-medium text-foreground">Bank-Level Security</p>
                    <p className="text-xs text-muted-foreground">AES-256 encryption</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 rounded-xl glass-card">
                  <div className={`p-2 rounded-lg ${iconBg} text-white`}>
                    <TrendingUp className="h-4 w-4" />
                  </div>
                  <div>
                    <p className="font-medium text-foreground">Advanced Trading</p>
                    <p className="text-xs text-muted-foreground">Professional tools</p>
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-8 px-8 pb-8">
              {/* Enhanced Tabs */}
              <Tabs defaultValue="setup" className="w-full">
                <TabsList className="grid w-full grid-cols-3 p-1 glass-card border-0 shadow-lg">
                  <TabsTrigger
                    value="setup"
                    className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-lg transition-all duration-300"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Setup
                  </TabsTrigger>
                  <TabsTrigger
                    value="instructions"
                    className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-lg transition-all duration-300"
                  >
                    <BookOpen className="h-4 w-4 mr-2" />
                    Guide
                  </TabsTrigger>
                  <TabsTrigger
                    value="permissions"
                    className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-lg transition-all duration-300"
                  >
                    <Key className="h-4 w-4 mr-2" />
                    Permissions
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="setup" className="space-y-8 mt-8">
                  <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-8">
                    {/* Enhanced Environment Toggle */}
                    <div className="p-6 rounded-2xl border-0 shadow-xl glass-card">
                      <div className="flex items-center justify-between">
                        <div className="space-y-3">
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-lg ${iconBg} text-white`}>
                              {currentEnv.icon}
                            </div>
                            <div>
                              <Label className="text-lg font-semibold text-foreground flex items-center gap-2">
                                Environment Selection
                              </Label>
                              {currentEnv.warningLevel === 'high' && (
                                <Badge variant="destructive" className="text-xs animate-pulse mt-1">
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                  Real Funds - High Risk
                                </Badge>
                              )}
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground max-w-md">
                            {testnet
                              ? "Testing environment for development and strategy validation"
                              : "Live trading environment with real funds - Use with extreme caution as losses are permanent"
                            }
                          </p>
                          <div className="flex items-center gap-2 text-xs">
                            <div className="w-2 h-2 rounded-full bg-primary animate-pulse"></div>
                            <span className="font-medium text-foreground">
                              {testnet ? 'Testnet Active' : 'Mainnet Active'}
                            </span>
                          </div>
                        </div>
                        <div className="flex flex-col items-center gap-3">
                          <Switch
                            checked={!testnet}
                            onCheckedChange={(checked) => handleTestnetToggle(!checked)}
                            className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-primary/60"
                          />
                          <span className="text-xs font-medium text-muted-foreground">
                            {testnet ? 'Switch to Live' : 'Switch to Test'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced API Key Field */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${iconBg} text-white`}>
                          <Key className="h-4 w-4" />
                        </div>
                        <div>
                          <Label htmlFor="apiKey" className="text-lg font-semibold text-foreground">
                            API Key
                          </Label>
                          <div className="flex items-center gap-2 mt-1">
                            {testnet && (
                              <Badge variant="outline" className={`text-xs bg-gradient-to-r ${primaryGradient} text-white border-0`}>
                                <TestTube className="h-3 w-3 mr-1" />
                                Testnet
                              </Badge>
                            )}
                            <span className="text-xs text-gray-500">
                              {testnet ? '32-128 characters' : 'Exactly 64 characters'}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="relative group">
                        <Input
                          id="apiKey"
                          type="text"
                          placeholder={testnet
                            ? "Enter your Binance Testnet API Key..."
                            : "Enter your Binance API Key..."
                          }
                          {...register('apiKey')}
                          className={`h-12 text-base transition-all duration-300 border-2 rounded-xl backdrop-blur-sm focus:ring-4 focus:ring-opacity-20 ${
                            errors.apiKey
                              ? 'border-red-300 focus:border-red-500 bg-red-50'
                              : apiKey && !validationMessages.apiKey
                                ? 'border-green-300 focus:border-green-500 bg-green-50'
                                : `border-gray-200 ${isTestnet ? 'focus:border-amber-500' : 'focus:border-emerald-500'} bg-white/70`
                          }`}
                        />

                        {/* Status Icons */}
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                          {apiKey && !validationMessages.apiKey && (
                            <div className="flex items-center gap-1">
                              <CheckCircle2 className="h-5 w-5 text-green-500 animate-pulse" />
                              <span className="text-xs text-green-600 font-medium">Valid</span>
                            </div>
                          )}
                          {validationMessages.apiKey && (
                            <AlertCircle className="h-5 w-5 text-warning" />
                          )}
                        </div>
                      </div>

                      {/* Enhanced validation feedback */}
                      {validationMessages.apiKey && (
                        <div className="p-3 rounded-lg bg-warning/10 border border-warning/30">
                          <p className="text-sm text-warning-foreground flex items-center gap-2">
                            <Info className="h-4 w-4" />
                            {validationMessages.apiKey}
                          </p>
                        </div>
                      )}

                      {errors.apiKey && (
                        <div className="p-3 rounded-lg bg-red-50 border border-red-200">
                          <p className="text-sm text-red-700 flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4" />
                            {errors.apiKey.message}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Enhanced Secret Key Field */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${iconBg} text-white`}>
                          <Lock className="h-4 w-4" />
                        </div>
                        <div>
                          <Label htmlFor="secretKey" className="text-lg font-semibold text-foreground">
                            Secret Key
                          </Label>
                          <div className="flex items-center gap-2 mt-1">
                            {testnet && (
                              <Badge variant="outline" className={`text-xs bg-gradient-to-r ${primaryGradient} text-white border-0`}>
                                <TestTube className="h-3 w-3 mr-1" />
                                Testnet
                              </Badge>
                            )}
                            <span className="text-xs text-gray-500">
                              Keep this secure and private
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="relative group">
                        <Input
                          id="secretKey"
                          type={showSecretKey ? 'text' : 'password'}
                          placeholder={testnet
                            ? "Enter your Binance Testnet Secret Key..."
                            : "Enter your Binance Secret Key..."
                          }
                          {...register('secretKey')}
                          className={`h-12 text-base pr-24 transition-all duration-300 border-2 rounded-xl backdrop-blur-sm focus:ring-4 focus:ring-opacity-20 ${
                            errors.secretKey
                              ? 'border-red-300 focus:border-red-500 bg-red-50'
                              : secretKey && !validationMessages.secretKey
                                ? 'border-green-300 focus:border-green-500 bg-green-50'
                                : `border-gray-200 ${isTestnet ? 'focus:border-amber-500' : 'focus:border-emerald-500'} bg-white/70`
                          }`}
                        />

                        {/* Status and Toggle Icons */}
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                          {secretKey && !validationMessages.secretKey && (
                            <div className="flex items-center gap-1">
                              <CheckCircle2 className="h-5 w-5 text-green-500 animate-pulse" />
                              <span className="text-xs text-green-600 font-medium">Valid</span>
                            </div>
                          )}
                          {validationMessages.secretKey && (
                            <AlertCircle className="h-5 w-5 text-warning" />
                          )}
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-gray-100 rounded-lg transition-colors"
                            onClick={() => setShowSecretKey(!showSecretKey)}
                          >
                            {showSecretKey ? (
                              <EyeOff className="h-4 w-4 text-gray-600" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-600" />
                            )}
                          </Button>
                        </div>
                      </div>

                      {/* Enhanced validation feedback */}
                      {validationMessages.secretKey && (
                        <div className="p-3 rounded-lg bg-warning/10 border border-warning/30">
                          <p className="text-sm text-warning-foreground flex items-center gap-2">
                            <Info className="h-4 w-4" />
                            {validationMessages.secretKey}
                          </p>
                        </div>
                      )}

                      {errors.secretKey && (
                        <div className="p-3 rounded-lg bg-red-50 border border-red-200">
                          <p className="text-sm text-red-700 flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4" />
                            {errors.secretKey.message}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Enhanced Connection Progress */}
                    {testingConnection && (
                      <div className="p-6 rounded-2xl glass-card border-0 shadow-xl">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg ${iconBg} text-white animate-pulse`}>
                                <Wifi className="h-4 w-4" />
                              </div>
                              <div>
                                <p className="font-semibold text-gray-900">Testing Connection</p>
                                <p className="text-sm text-gray-600">{connectionProgress}% Complete</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className={`text-2xl font-bold bg-gradient-to-r ${primaryGradient} bg-clip-text text-transparent`}>
                                {connectionProgress}%
                              </div>
                            </div>
                          </div>

                          <Progress
                            value={connectionProgress}
                            className={`h-3 bg-gray-200 [&>div]:bg-gradient-to-r [&>div]:${primaryGradient} [&>div]:transition-all [&>div]:duration-500`}
                          />

                          <div className="text-center">
                            <p className="text-sm font-medium text-gray-700">
                              {connectionProgress < 30 && "🔍 Validating credentials..."}
                              {connectionProgress >= 30 && connectionProgress < 60 && "🌐 Connecting to Binance..."}
                              {connectionProgress >= 60 && connectionProgress < 90 && "🔑 Testing API permissions..."}
                              {connectionProgress >= 90 && "✨ Finalizing connection..."}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Enhanced Quick Setup Tips */}
                    <div className="p-6 rounded-2xl glass-card border-0 shadow-lg">
                      <div className="flex items-start gap-4">
                        <div className={`p-3 rounded-xl ${iconBg} text-white`}>
                          <Lightbulb className="h-5 w-5" />
                        </div>
                        <div className="space-y-3">
                          <h3 className="font-semibold text-foreground text-lg">Quick Setup Tips</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="flex items-center gap-2 p-3 rounded-lg glass-card">
                              <Star className="h-4 w-4 text-primary" />
                              <span className="text-sm text-muted-foreground">Start with testnet for safety</span>
                            </div>
                            <div className="flex items-center gap-2 p-3 rounded-lg glass-card">
                              <Shield className="h-4 w-4 text-primary" />
                              <span className="text-sm text-muted-foreground">Use read-only permissions initially</span>
                            </div>
                            <div className="flex items-center gap-2 p-3 rounded-lg glass-card">
                              <Lock className="h-4 w-4 text-success" />
                              <span className="text-sm text-muted-foreground">Enable IP restrictions</span>
                            </div>
                            <div className="flex items-center gap-2 p-3 rounded-lg glass-card">
                              <AlertTriangle className="h-4 w-4 text-destructive" />
                              <span className="text-sm text-muted-foreground">Never share your secret key</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Form Actions */}
                    <div className="flex gap-4 pt-6">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={onCancel}
                        disabled={isConnecting || testingConnection}
                        className="flex-1 h-12 text-base border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-300"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        disabled={!isValid || isConnecting || testingConnection}
                        className="flex-1 h-12 text-base bg-primary hover:bg-primary/90 border-0 shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 text-primary-foreground font-semibold"
                      >
                        {testingConnection ? (
                          <>
                            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                            Testing Connection...
                          </>
                        ) : (
                          <>
                            <Zap className="h-5 w-5 mr-2" />
                            Connect to {currentEnv.name}
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </TabsContent>

                {/* Enhanced Instructions Tab */}
                <TabsContent value="instructions" className="space-y-8 mt-8">
                  <div className="space-y-8">
                    {/* Header */}
                    <div className="p-6 rounded-2xl glass-card border-0 shadow-xl">
                      <div className="flex items-center gap-4">
                        <div className={`p-3 rounded-xl ${iconBg} text-white`}>
                          <BookOpen className="h-6 w-6" />
                        </div>
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900">Step-by-Step Setup Guide</h2>
                          <p className="text-gray-600 mt-1">Follow these steps to create your Binance API credentials</p>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Step Cards */}
                    <div className="space-y-6">
                      <div className={`p-6 rounded-2xl bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 group`}>
                        <div className="flex items-start gap-4">
                          <div className={`w-12 h-12 rounded-2xl ${iconBg} text-white flex items-center justify-center text-xl font-bold shadow-lg`}>
                            1
                          </div>
                          <div className="space-y-3 flex-1">
                            <h4 className="text-xl font-semibold text-gray-900">Access Binance API Management</h4>
                            <p className="text-gray-600">
                              Navigate to your Binance account's API management section to create new credentials
                            </p>
                            <Button
                              variant="outline"
                              size="lg"
                              onClick={() => window.open(currentEnv.apiUrl, '_blank')}
                              className="bg-primary hover:bg-primary/90 text-primary-foreground border-0 shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                            >
                              <ExternalLink className="h-4 w-4 mr-2" />
                              Open {currentEnv.name} API Management
                            </Button>
                          </div>
                        </div>
                      </div>

                      <div className={`p-6 rounded-2xl bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 group`}>
                        <div className="flex items-start gap-4">
                          <div className={`w-12 h-12 rounded-2xl ${iconBg} text-white flex items-center justify-center text-xl font-bold shadow-lg`}>
                            2
                          </div>
                          <div className="space-y-3 flex-1">
                            <h4 className="text-xl font-semibold text-gray-900">Create New API Key</h4>
                            <p className="text-gray-600">
                              Click "Create API" and provide a descriptive label for your key (e.g., "FluxTrader")
                            </p>
                            <div className="flex items-center gap-2 p-3 rounded-lg bg-blue-50 border border-blue-200">
                              <Info className="h-4 w-4 text-blue-600" />
                              <span className="text-sm text-blue-700">Use a memorable name to identify this key later</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className={`p-6 rounded-2xl bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 group`}>
                        <div className="flex items-start gap-4">
                          <div className={`w-12 h-12 rounded-2xl ${iconBg} text-white flex items-center justify-center text-xl font-bold shadow-lg`}>
                            3
                          </div>
                          <div className="space-y-3 flex-1">
                            <h4 className="text-xl font-semibold text-gray-900">Configure Permissions</h4>
                            <p className="text-gray-600">
                              Select the appropriate permissions based on your trading needs and security preferences
                            </p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div className="flex items-center gap-2 p-3 rounded-lg bg-green-50 border border-green-200">
                                <CheckCircle className="h-4 w-4 text-green-600" />
                                <span className="text-sm text-green-700">Enable Reading (Required)</span>
                              </div>
                              <div className="flex items-center gap-2 p-3 rounded-lg bg-warning/10 border border-warning/30">
                                <AlertTriangle className="h-4 w-4 text-warning" />
                                <span className="text-sm text-warning-foreground">Spot Trading (Optional)</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className={`p-6 rounded-2xl bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 group`}>
                        <div className="flex items-start gap-4">
                          <div className={`w-12 h-12 rounded-2xl ${iconBg} text-white flex items-center justify-center text-xl font-bold shadow-lg`}>
                            4
                          </div>
                          <div className="space-y-3 flex-1">
                            <h4 className="text-xl font-semibold text-gray-900">Copy Credentials Securely</h4>
                            <p className="text-gray-600">
                              Save your API Key and Secret Key securely. The Secret Key is only shown once!
                            </p>
                            <div className="flex items-center gap-2 p-3 rounded-lg bg-red-50 border border-red-200">
                              <AlertTriangle className="h-4 w-4 text-red-600" />
                              <span className="text-sm text-red-700 font-medium">Important: Store your secret key safely - it cannot be recovered</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Environment-specific note */}
                    {testnet && (
                      <div className="p-6 rounded-2xl glass-card shadow-lg">
                        <div className="flex items-start gap-4">
                          <div className="p-2 rounded-lg bg-primary text-primary-foreground">
                            <TestTube className="h-5 w-5" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-foreground mb-2">Testnet Environment</h4>
                            <p className="text-sm text-muted-foreground">
                              You'll need to create a separate account on Binance Testnet.
                              Testnet credentials won't work on mainnet and vice versa.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </TabsContent>

                {/* Enhanced Permissions Tab */}
                <TabsContent value="permissions" className="space-y-8 mt-8">
                  <div className="space-y-8">
                    {/* Header */}
                    <div className="p-6 rounded-2xl glass-card border-0 shadow-xl">
                      <div className="flex items-center gap-4">
                        <div className={`p-3 rounded-xl ${iconBg} text-white`}>
                          <Settings className="h-6 w-6" />
                        </div>
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900">API Permission Templates</h2>
                          <p className="text-gray-600 mt-1">Choose the right permission level for your trading needs</p>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Permission Cards */}
                    <div className="grid gap-6">
                      {Object.entries(BINANCE_PERMISSIONS).map(([key, permission]) => (
                        <div key={key} className={`p-6 rounded-2xl bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 group`}>
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className={`p-2 rounded-lg ${
                                  key === 'BASIC' ? 'bg-green-500' :
                                  key === 'TRADING' ? 'bg-blue-500' : 'bg-purple-500'
                                } text-white`}>
                                  {key === 'BASIC' ? <Eye className="h-4 w-4" /> :
                                   key === 'TRADING' ? <TrendingUp className="h-4 w-4" /> :
                                   <Settings className="h-4 w-4" />}
                                </div>
                                <h4 className="text-xl font-semibold text-gray-900">{permission.name}</h4>
                              </div>
                              <Badge
                                variant="outline"
                                className={`text-xs ${
                                  key === 'BASIC' ? 'bg-green-50 text-green-700 border-green-200' :
                                  key === 'TRADING' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                                  'bg-purple-50 text-purple-700 border-purple-200'
                                }`}
                              >
                                {key === 'BASIC' ? '🌟 Recommended for beginners' :
                                 key === 'TRADING' ? '⚡ Most common' : '🚀 Advanced users'}
                              </Badge>
                            </div>

                            <p className="text-gray-600">
                              {permission.description}
                            </p>

                            <div className="bg-gray-50 p-4 rounded-xl border border-gray-200">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-gray-700">Permission Template:</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(permission.template)}
                                  className="h-8 text-xs hover:bg-gray-200 transition-colors"
                                >
                                  <Copy className="h-3 w-3 mr-1" />
                                  Copy
                                </Button>
                              </div>
                              <div className="bg-white p-3 rounded-lg border border-gray-200">
                                <p className="text-xs font-mono text-gray-800 whitespace-pre-line">
                                  {permission.template}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Enhanced Security Tip */}
                    <div className="p-6 rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 shadow-lg">
                      <div className="flex items-start gap-4">
                        <div className="p-3 rounded-xl bg-blue-500 text-white">
                          <HelpCircle className="h-5 w-5" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-blue-800 mb-2">💡 Pro Security Tip</h4>
                          <p className="text-sm text-blue-700 mb-3">
                            Start with "Enable Reading" only to test the connection. You can always add trading permissions later when you're ready to execute trades.
                          </p>
                          <div className="flex items-center gap-2 p-3 rounded-lg bg-blue-100 border border-blue-200">
                            <Shield className="h-4 w-4 text-blue-600" />
                            <span className="text-xs text-blue-700 font-medium">This approach minimizes risk while testing</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Enhanced Mainnet Warning Modal */}
      {showMainnetWarning && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="w-full max-w-lg">
            <Card className="border-0 shadow-2xl bg-gradient-to-br from-red-50 via-white to-orange-50">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-2xl bg-gradient-to-br from-red-500 to-orange-600 text-white shadow-lg">
                    <AlertTriangle className="h-8 w-8" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold text-red-800">
                      ⚠️ Mainnet Warning
                    </CardTitle>
                    <p className="text-red-600 mt-1">High Risk Environment</p>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <Alert className="border-red-200 bg-red-50 shadow-lg">
                  <DollarSign className="h-5 w-5 text-red-600" />
                  <AlertDescription className="text-red-800">
                    <strong className="text-lg">You are about to connect to Binance Mainnet with real funds!</strong>
                  </AlertDescription>
                </Alert>

                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-900 text-lg">⚠️ Critical Warnings:</h4>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-4 rounded-xl bg-red-50 border border-red-200">
                      <DollarSign className="h-5 w-5 text-red-600 flex-shrink-0" />
                      <span className="text-red-800 font-medium">This will use real money for trading</span>
                    </div>
                    <div className="flex items-center gap-3 p-4 rounded-xl bg-red-50 border border-red-200">
                      <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0" />
                      <span className="text-red-800 font-medium">Losses are permanent and cannot be recovered</span>
                    </div>
                    <div className="flex items-center gap-3 p-4 rounded-xl bg-red-50 border border-red-200">
                      <Shield className="h-5 w-5 text-red-600 flex-shrink-0" />
                      <span className="text-red-800 font-medium">Only proceed if you understand the risks</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 rounded-xl glass-card">
                  <p className="text-foreground text-sm font-medium text-center">
                    💡 Consider starting with testnet to familiarize yourself with the platform
                  </p>
                </div>

                <div className="flex gap-4 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowMainnetWarning(false)}
                    className="flex-1 h-12 text-base border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleMainnetConfirm}
                    className="flex-1 h-12 text-base bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    I Understand, Proceed
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </>
  );
};

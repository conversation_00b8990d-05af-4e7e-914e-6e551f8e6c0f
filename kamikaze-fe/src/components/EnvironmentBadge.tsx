import React from 'react';
import { Badge } from '@/components/ui/badge';
import { TestTube, DollarSign } from 'lucide-react';
import { useExchange } from '@/contexts/ExchangeContext';
import { cn } from '@/lib/utils';

interface EnvironmentBadgeProps {
  type?: 'bot' | 'portfolio' | 'trade' | 'order' | 'general';
  className?: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const EnvironmentBadge: React.FC<EnvironmentBadgeProps> = ({
  type = 'general',
  className,
  showIcon = true,
  size = 'sm'
}) => {
  const { hasConnectedExchange, isTestnetMode } = useExchange();
  
  if (!hasConnectedExchange()) return null;
  
  const isTestnet = isTestnetMode();
  
  const typeLabels = {
    bot: isTestnet ? 'Testnet Bot' : 'Live Bot',
    portfolio: isTestnet ? 'Testnet Portfolio' : 'Live Portfolio',
    trade: isTestnet ? 'Testnet Trade' : 'Live Trade',
    order: isTestnet ? 'Testnet Order' : 'Live Order',
    general: isTestnet ? 'Testnet' : 'Live'
  };

  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-1.5'
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  return (
    <Badge
      className={cn(
        'font-medium border transition-all duration-200',
        sizeClasses[size],
        isTestnet
          ? 'bg-orange-500/20 text-orange-100 border-orange-400/50 hover:bg-orange-500/30'
          : 'bg-green-500/20 text-green-100 border-green-400/50 hover:bg-green-500/30',
        className
      )}
    >
      <div className="flex items-center gap-1">
        {showIcon && (
          isTestnet ? (
            <TestTube className={iconSizes[size]} />
          ) : (
            <DollarSign className={iconSizes[size]} />
          )
        )}
        <span>{typeLabels[type]}</span>
      </div>
    </Badge>
  );
};

// Specific badge components for different use cases
export const BotEnvironmentBadge: React.FC<Omit<EnvironmentBadgeProps, 'type'>> = (props) => (
  <EnvironmentBadge type="bot" {...props} />
);

export const PortfolioEnvironmentBadge: React.FC<Omit<EnvironmentBadgeProps, 'type'>> = (props) => (
  <EnvironmentBadge type="portfolio" {...props} />
);

export const TradeEnvironmentBadge: React.FC<Omit<EnvironmentBadgeProps, 'type'>> = (props) => (
  <EnvironmentBadge type="trade" {...props} />
);

export const OrderEnvironmentBadge: React.FC<Omit<EnvironmentBadgeProps, 'type'>> = (props) => (
  <EnvironmentBadge type="order" {...props} />
);

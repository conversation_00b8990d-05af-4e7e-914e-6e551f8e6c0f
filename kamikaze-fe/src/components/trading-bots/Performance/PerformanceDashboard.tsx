/**
 * Performance Dashboard Component
 * Comprehensive trading bot performance analytics and metrics
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { TradingBot, PerformanceMetrics, Trade, Position } from '@/types/tradingBot';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  Shield,
  Activity,
  BarChart3,
  PieChart as PieChartIcon,
  Calendar,
  Clock,
  Zap,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';

interface PerformanceDashboardProps {
  bot: TradingBot;
  className?: string;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ReactNode;
  format?: 'currency' | 'percentage' | 'number';
  trend?: 'up' | 'down' | 'neutral';
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon,
  format = 'number',
  trend = 'neutral'
}) => {
  const formatValue = (val: string | number) => {
    if (typeof val === 'string') return val;

    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(val);
      case 'percentage':
        return `${val >= 0 ? '+' : ''}${val.toFixed(2)}%`;
      default:
        return val.toLocaleString();
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-muted-foreground';
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3" />;
      case 'down':
        return <TrendingDown className="h-3 w-3" />;
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {icon}
            <span className="text-sm text-muted-foreground">{title}</span>
          </div>
          {change !== undefined && (
            <div className={`flex items-center gap-1 text-xs ${getTrendColor()}`}>
              {getTrendIcon()}
              {format === 'percentage' ? `${change >= 0 ? '+' : ''}${change.toFixed(2)}%` : change.toFixed(2)}
            </div>
          )}
        </div>
        <div className="mt-2">
          <div className="text-2xl font-bold">{formatValue(value)}</div>
        </div>
      </CardContent>
    </Card>
  );
};

export const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  bot,
  className = ''
}) => {
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedTab, setSelectedTab] = useState('overview');

  const performance = bot.metrics.performance;
  const positions = bot.metrics.current_positions;
  const trades = bot.metrics.recent_trades;

  // Mock data for charts - in real implementation, this would come from API
  const performanceData = [
    { date: '2024-01-01', value: 1000, drawdown: 0 },
    { date: '2024-01-02', value: 1050, drawdown: -2.1 },
    { date: '2024-01-03', value: 1025, drawdown: -3.5 },
    { date: '2024-01-04', value: 1080, drawdown: -1.2 },
    { date: '2024-01-05', value: 1120, drawdown: 0 },
    { date: '2024-01-06', value: 1095, drawdown: -2.8 },
    { date: '2024-01-07', value: 1150, drawdown: 0 }
  ];

  const tradeDistribution = [
    { name: 'Winning Trades', value: performance.winning_trades, color: '#10b981' },
    { name: 'Losing Trades', value: performance.losing_trades, color: '#ef4444' }
  ];

  const riskMetrics = [
    {
      name: 'Portfolio Risk',
      current: 1.8,
      max: bot.config.risk_management.max_portfolio_risk,
      color: '#3b82f6'
    },
    {
      name: 'Position Risk',
      current: 0.9,
      max: bot.config.risk_management.max_position_risk,
      color: '#8b5cf6'
    },
    {
      name: 'Drawdown',
      current: Math.abs(performance.max_drawdown_pct),
      max: bot.config.risk_management.max_drawdown,
      color: '#f59e0b'
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{bot.config.name} Performance</h2>
          <p className="text-muted-foreground">Comprehensive performance analytics and metrics</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1d">1 Day</SelectItem>
              <SelectItem value="7d">7 Days</SelectItem>
              <SelectItem value="30d">30 Days</SelectItem>
              <SelectItem value="90d">90 Days</SelectItem>
              <SelectItem value="1y">1 Year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="risk">Risk Analysis</TabsTrigger>
          <TabsTrigger value="trades">Trade History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <MetricCard
              title="Total Return"
              value={performance.total_return}
              change={performance.total_return_pct}
              icon={<DollarSign className="h-4 w-4" />}
              format="currency"
              trend={performance.total_return >= 0 ? 'up' : 'down'}
            />
            <MetricCard
              title="Win Rate"
              value={performance.win_rate}
              icon={<Target className="h-4 w-4" />}
              format="percentage"
              trend={performance.win_rate >= 60 ? 'up' : performance.win_rate >= 40 ? 'neutral' : 'down'}
            />
            <MetricCard
              title="Sharpe Ratio"
              value={performance.sharpe_ratio.toFixed(2)}
              icon={<BarChart3 className="h-4 w-4" />}
              trend={performance.sharpe_ratio >= 1 ? 'up' : performance.sharpe_ratio >= 0.5 ? 'neutral' : 'down'}
            />
            <MetricCard
              title="Total Trades"
              value={performance.total_trades}
              icon={<Activity className="h-4 w-4" />}
            />
          </div>

          {/* Performance Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Portfolio Value Over Time</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.1}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Trade Distribution */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Trade Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={tradeDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {tradeDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                <div className="flex justify-center gap-4 mt-4">
                  {tradeDistribution.map((entry, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: entry.color }}
                      />
                      <span className="text-sm">{entry.name}: {entry.value}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Current Positions</CardTitle>
              </CardHeader>
              <CardContent>
                {positions.length > 0 ? (
                  <div className="space-y-3">
                    {positions.map((position, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded">
                        <div>
                          <div className="font-medium">{position.symbol}</div>
                          <div className="text-sm text-muted-foreground">
                            {position.side} • Size: {position.size}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`font-medium ${position.unrealized_pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            ${position.unrealized_pnl.toFixed(2)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {position.unrealized_pnl_pct.toFixed(2)}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    No active positions
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          {/* Advanced Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <MetricCard
              title="Annualized Return"
              value={performance.annualized_return}
              icon={<TrendingUp className="h-4 w-4" />}
              format="percentage"
            />
            <MetricCard
              title="Sortino Ratio"
              value={performance.sortino_ratio.toFixed(2)}
              icon={<Shield className="h-4 w-4" />}
            />
            <MetricCard
              title="Profit Factor"
              value={performance.profit_factor.toFixed(2)}
              icon={<Target className="h-4 w-4" />}
            />
            <MetricCard
              title="Max Drawdown"
              value={performance.max_drawdown_pct}
              icon={<TrendingDown className="h-4 w-4" />}
              format="percentage"
              trend="down"
            />
            <MetricCard
              title="Avg Trade Duration"
              value={`${performance.avg_trade_duration.toFixed(1)}h`}
              icon={<Clock className="h-4 w-4" />}
            />
            <MetricCard
              title="Volatility"
              value={performance.volatility}
              icon={<Activity className="h-4 w-4" />}
              format="percentage"
            />
          </div>

          {/* Drawdown Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Drawdown Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Area
                    type="monotone"
                    dataKey="drawdown"
                    stroke="#ef4444"
                    fill="#ef4444"
                    fillOpacity={0.1}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="risk" className="space-y-6">
          {/* Risk Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {riskMetrics.map((metric, index) => (
              <Card key={index}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">{metric.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Current</span>
                      <span className="font-medium">{metric.current.toFixed(1)}%</span>
                    </div>
                    <Progress
                      value={(metric.current / metric.max) * 100}
                      className="h-2"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>0%</span>
                      <span>Max: {metric.max}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Risk Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Risk Analysis</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Value at Risk (95%)</h4>
                  <div className="text-2xl font-bold text-red-600">
                    ${Math.abs(performance.var_95).toFixed(2)}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Maximum expected loss over 1 day with 95% confidence
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Beta</h4>
                  <div className="text-2xl font-bold">
                    {performance.beta.toFixed(2)}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Correlation with market movements
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trades" className="space-y-6">
          {/* Recent Trades */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Trades</CardTitle>
            </CardHeader>
            <CardContent>
              {trades.length > 0 ? (
                <div className="space-y-3">
                  {trades.map((trade, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center gap-3">
                        <Badge variant={trade.side === 'BUY' ? 'default' : 'secondary'}>
                          {trade.side}
                        </Badge>
                        <div>
                          <div className="font-medium">{trade.symbol}</div>
                          <div className="text-sm text-muted-foreground">
                            {new Date(trade.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">
                          {trade.size} @ ${trade.price.toFixed(4)}
                        </div>
                        <div className={`text-sm ${trade.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          P&L: ${trade.pnl.toFixed(2)} ({trade.pnl_pct.toFixed(2)}%)
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  No trades executed yet
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
/**
 * Bot Creation Wizard Component
 * Professional-grade trading bot creation interface
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useTradingBots } from '@/hooks/useTradingBots';
import { useExchange } from '@/contexts/ExchangeContext';
import { STRATEGY_TEMPLATES } from '@/services/tradingBotApi';
import {
  TradingBotConfig,
  StrategyConfig,
  StrategyType,
  RiskLevel,
  TimeFrame,
  PositionSizingMethod,
  SignalType
} from '@/types/tradingBot';
import {
  Bot,
  Settings,
  TrendingUp,
  Shield,
  Zap,
  Target,
  AlertTriangle,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Sparkles
} from 'lucide-react';

interface BotCreationWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onBotCreated?: (bot: any) => void;
}

interface WizardStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

const WIZARD_STEPS: WizardStep[] = [
  {
    id: 'basic',
    title: 'Basic Information',
    description: 'Set up bot name and description',
    icon: <Bot className="h-5 w-5" />
  },
  {
    id: 'strategy',
    title: 'Strategy Selection',
    description: 'Choose your trading strategy',
    icon: <TrendingUp className="h-5 w-5" />
  },
  {
    id: 'risk',
    title: 'Risk Management',
    description: 'Configure risk parameters',
    icon: <Shield className="h-5 w-5" />
  },
  {
    id: 'execution',
    title: 'Execution Settings',
    description: 'Set trading execution parameters',
    icon: <Zap className="h-5 w-5" />
  },
  {
    id: 'review',
    title: 'Review & Create',
    description: 'Review configuration and create bot',
    icon: <CheckCircle className="h-5 w-5" />
  }
];

export const BotCreationWizard: React.FC<BotCreationWizardProps> = ({
  isOpen,
  onClose,
  onBotCreated
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isCreating, setIsCreating] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const { toast } = useToast();
  const { createBot, validateStrategy } = useTradingBots();
  const { hasConnectedExchange } = useExchange();

  // Bot configuration state
  const [config, setConfig] = useState<Partial<TradingBotConfig>>({
    name: '',
    description: '',
    symbols: ['BTCUSDT'],
    initial_capital: 1000,
    strategy: {
      name: '',
      description: '',
      strategy_type: StrategyType.MEAN_REVERSION,
      rules: [],
      indicators: [],
      timeframes: [TimeFrame.H1],
      risk_level: RiskLevel.MEDIUM,
      parameters: {}
    },
    risk_management: {
      max_portfolio_risk: 2.0,
      max_position_risk: 1.0,
      max_drawdown: 10.0,
      position_sizing_method: PositionSizingMethod.VOLATILITY_ADJUSTED,
      risk_level: RiskLevel.MEDIUM,
      enable_stop_loss: true,
      enable_take_profit: true,
      enable_trailing_stop: false,
      correlation_limit: 0.7,
      max_positions: 5,
      daily_loss_limit: 5.0
    },
    trading_engine: {
      max_concurrent_orders: 5,
      order_timeout_seconds: 30,
      position_update_interval: 5,
      signal_generation_interval: 60,
      enable_paper_trading: true,
      enable_order_validation: true,
      enable_risk_checks: true,
      max_daily_trades: 20,
      max_daily_loss_pct: 5.0,
      emergency_stop_loss_pct: 10.0,
      slippage_tolerance_pct: 0.1,
      commission_rate: 0.001
    },
    signal_filter: {
      min_confidence: 0.7,
      min_consensus_score: 0.6,
      min_quality: 'GOOD',
      require_trend_alignment: true,
      min_trend_alignment: 0.6,
      max_signals_per_timeframe: 3,
      enable_volume_filter: true,
      min_volume_ratio: 1.2
    }
  });

  const progress = ((currentStep + 1) / WIZARD_STEPS.length) * 100;

  const handleNext = async () => {
    // Validate current step
    const errors = await validateCurrentStep();
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    setValidationErrors([]);
    if (currentStep < WIZARD_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      setValidationErrors([]);
    }
  };

  const validateCurrentStep = async (): Promise<string[]> => {
    const errors: string[] = [];

    switch (WIZARD_STEPS[currentStep].id) {
      case 'basic':
        if (!config.name?.trim()) errors.push('Bot name is required');
        if (!config.description?.trim()) errors.push('Bot description is required');
        if (!config.symbols?.length) errors.push('At least one trading symbol is required');
        if (!config.initial_capital || config.initial_capital <= 0) errors.push('Initial capital must be greater than 0');
        break;

      case 'strategy':
        if (!config.strategy?.name?.trim()) errors.push('Strategy name is required');
        if (!config.strategy?.strategy_type) errors.push('Strategy type is required');
        if (!config.strategy?.timeframes?.length) errors.push('At least one timeframe is required');

        // Validate strategy with backend
        if (config.strategy && errors.length === 0) {
          try {
            const validation = await validateStrategy(config.strategy as StrategyConfig);
            if (!validation.valid) {
              errors.push(...validation.errors);
            }
          } catch (error) {
            errors.push('Failed to validate strategy configuration');
          }
        }
        break;

      case 'risk':
        if (!config.risk_management?.max_portfolio_risk || config.risk_management.max_portfolio_risk <= 0) {
          errors.push('Max portfolio risk must be greater than 0');
        }
        if (!config.risk_management?.max_position_risk || config.risk_management.max_position_risk <= 0) {
          errors.push('Max position risk must be greater than 0');
        }
        break;

      case 'execution':
        if (!config.trading_engine?.max_concurrent_orders || config.trading_engine.max_concurrent_orders <= 0) {
          errors.push('Max concurrent orders must be greater than 0');
        }
        if (!config.trading_engine?.max_daily_trades || config.trading_engine.max_daily_trades <= 0) {
          errors.push('Max daily trades must be greater than 0');
        }
        break;
    }

    return errors;
  };

  const handleCreateBot = async () => {
    setIsCreating(true);
    setValidationErrors([]);

    try {
      // Final validation
      const errors = await validateCurrentStep();
      if (errors.length > 0) {
        setValidationErrors(errors);
        return;
      }

      // Create the bot
      const bot = await createBot(config as TradingBotConfig);
      if (bot) {
        toast({
          title: 'Success',
          description: `Trading bot "${config.name}" created successfully!`
        });

        onBotCreated?.(bot);
        onClose();

        // Reset wizard
        setCurrentStep(0);
        setConfig({
          name: '',
          description: '',
          symbols: ['BTCUSDT'],
          initial_capital: 1000,
          // ... reset other fields
        });
      }
    } catch (error: any) {
      setValidationErrors([error.message || 'Failed to create trading bot']);
    } finally {
      setIsCreating(false);
    }
  };

  const handleStrategyTemplateSelect = (templateKey: string) => {
    const template = STRATEGY_TEMPLATES[templateKey as keyof typeof STRATEGY_TEMPLATES];
    if (template) {
      setConfig(prev => ({
        ...prev,
        strategy: {
          ...template,
          name: template.name,
          description: template.description
        }
      }));
    }
  };

  const updateConfig = (path: string, value: any) => {
    setConfig(prev => {
      const keys = path.split('.');
      const newConfig = { ...prev };
      let current: any = newConfig;

      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) current[keys[i]] = {};
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-6 w-6 text-primary" />
                Create Trading Bot
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Set up your professional trading bot with advanced strategies and risk management
              </p>
            </div>
            <Button variant="ghost" onClick={onClose}>×</Button>
          </div>

          {/* Progress Bar */}
          <div className="mt-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Step {currentStep + 1} of {WIZARD_STEPS.length}</span>
              <span className="text-sm text-muted-foreground">{Math.round(progress)}% Complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Step Navigation */}
          <div className="flex items-center justify-between mt-4 overflow-x-auto">
            {WIZARD_STEPS.map((step, index) => (
              <div
                key={step.id}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                  index === currentStep
                    ? 'bg-primary text-primary-foreground'
                    : index < currentStep
                    ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                    : 'bg-muted text-muted-foreground'
                }`}
              >
                {step.icon}
                <div className="hidden sm:block">
                  <div className="text-xs font-medium">{step.title}</div>
                  <div className="text-xs opacity-75">{step.description}</div>
                </div>
              </div>
            ))}
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Exchange Connection Warning */}
          {!hasConnectedExchange() && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Exchange Connection Required:</strong> You need to connect an exchange account to start trading bots.
                You can create and configure bots now, but you'll need to connect an exchange before starting them.
                Please go to Settings to connect your exchange.
              </AlertDescription>
            </Alert>
          )}

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Step Content */}
          {WIZARD_STEPS[currentStep].id === 'basic' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bot-name">Bot Name *</Label>
                  <Input
                    id="bot-name"
                    placeholder="e.g., Mean Reversion Pro"
                    value={config.name || ''}
                    onChange={(e) => updateConfig('name', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="initial-capital">Initial Capital (USD) *</Label>
                  <Input
                    id="initial-capital"
                    type="number"
                    placeholder="1000"
                    value={config.initial_capital || ''}
                    onChange={(e) => updateConfig('initial_capital', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bot-description">Description *</Label>
                <Textarea
                  id="bot-description"
                  placeholder="Describe your trading bot strategy and goals..."
                  value={config.description || ''}
                  onChange={(e) => updateConfig('description', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label>Trading Symbols *</Label>
                <div className="flex flex-wrap gap-2">
                  {['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT'].map(symbol => (
                    <Badge
                      key={symbol}
                      variant={config.symbols?.includes(symbol) ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => {
                        const symbols = config.symbols || [];
                        if (symbols.includes(symbol)) {
                          updateConfig('symbols', symbols.filter(s => s !== symbol));
                        } else {
                          updateConfig('symbols', [...symbols, symbol]);
                        }
                      }}
                    >
                      {symbol}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}

          {WIZARD_STEPS[currentStep].id === 'strategy' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Strategy Templates</Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {Object.entries(STRATEGY_TEMPLATES).map(([key, template]) => (
                    <Card
                      key={key}
                      className={`cursor-pointer transition-colors ${
                        config.strategy?.name === template.name
                          ? 'ring-2 ring-primary bg-primary/5'
                          : 'hover:bg-muted/50'
                      }`}
                      onClick={() => handleStrategyTemplateSelect(key)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <TrendingUp className="h-4 w-4" />
                          <h4 className="font-medium">{template.name}</h4>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{template.description}</p>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {template.risk_level.toUpperCase()}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {template.strategy_type.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="strategy-name">Strategy Name *</Label>
                  <Input
                    id="strategy-name"
                    placeholder="Custom Strategy Name"
                    value={config.strategy?.name || ''}
                    onChange={(e) => updateConfig('strategy.name', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="strategy-type">Strategy Type *</Label>
                  <Select
                    value={config.strategy?.strategy_type || ''}
                    onValueChange={(value) => updateConfig('strategy.strategy_type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select strategy type" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(StrategyType).map(type => (
                        <SelectItem key={type} value={type}>
                          {type.replace('_', ' ').toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Timeframes *</Label>
                <div className="flex flex-wrap gap-2">
                  {Object.values(TimeFrame).map(timeframe => (
                    <Badge
                      key={timeframe}
                      variant={config.strategy?.timeframes?.includes(timeframe) ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => {
                        const timeframes = config.strategy?.timeframes || [];
                        if (timeframes.includes(timeframe)) {
                          updateConfig('strategy.timeframes', timeframes.filter(t => t !== timeframe));
                        } else {
                          updateConfig('strategy.timeframes', [...timeframes, timeframe]);
                        }
                      }}
                    >
                      {timeframe}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}

          {WIZARD_STEPS[currentStep].id === 'risk' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="max-portfolio-risk">Max Portfolio Risk (%) *</Label>
                  <Input
                    id="max-portfolio-risk"
                    type="number"
                    step="0.1"
                    placeholder="2.0"
                    value={config.risk_management?.max_portfolio_risk || ''}
                    onChange={(e) => updateConfig('risk_management.max_portfolio_risk', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-position-risk">Max Position Risk (%) *</Label>
                  <Input
                    id="max-position-risk"
                    type="number"
                    step="0.1"
                    placeholder="1.0"
                    value={config.risk_management?.max_position_risk || ''}
                    onChange={(e) => updateConfig('risk_management.max_position_risk', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="max-drawdown">Max Drawdown (%) *</Label>
                  <Input
                    id="max-drawdown"
                    type="number"
                    step="0.1"
                    placeholder="10.0"
                    value={config.risk_management?.max_drawdown || ''}
                    onChange={(e) => updateConfig('risk_management.max_drawdown', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="position-sizing">Position Sizing Method</Label>
                  <Select
                    value={config.risk_management?.position_sizing_method || ''}
                    onValueChange={(value) => updateConfig('risk_management.position_sizing_method', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(PositionSizingMethod).map(method => (
                        <SelectItem key={method} value={method}>
                          {method.replace('_', ' ').toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {WIZARD_STEPS[currentStep].id === 'execution' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="max-concurrent-orders">Max Concurrent Orders *</Label>
                  <Input
                    id="max-concurrent-orders"
                    type="number"
                    placeholder="5"
                    value={config.trading_engine?.max_concurrent_orders || ''}
                    onChange={(e) => updateConfig('trading_engine.max_concurrent_orders', parseInt(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-daily-trades">Max Daily Trades *</Label>
                  <Input
                    id="max-daily-trades"
                    type="number"
                    placeholder="20"
                    value={config.trading_engine?.max_daily_trades || ''}
                    onChange={(e) => updateConfig('trading_engine.max_daily_trades', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="signal-interval">Signal Generation Interval (seconds)</Label>
                  <Input
                    id="signal-interval"
                    type="number"
                    placeholder="60"
                    value={config.trading_engine?.signal_generation_interval || ''}
                    onChange={(e) => updateConfig('trading_engine.signal_generation_interval', parseInt(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="commission-rate">Commission Rate (%)</Label>
                  <Input
                    id="commission-rate"
                    type="number"
                    step="0.001"
                    placeholder="0.001"
                    value={config.trading_engine?.commission_rate || ''}
                    onChange={(e) => updateConfig('trading_engine.commission_rate', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>
          )}

          {WIZARD_STEPS[currentStep].id === 'review' && (
            <div className="space-y-4">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Review your bot configuration below. Once created, you can modify these settings later.
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2 text-sm">
                    <div><strong>Name:</strong> {config.name}</div>
                    <div><strong>Capital:</strong> ${config.initial_capital?.toLocaleString()}</div>
                    <div><strong>Symbols:</strong> {config.symbols?.join(', ')}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">Strategy</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2 text-sm">
                    <div><strong>Type:</strong> {config.strategy?.strategy_type?.replace('_', ' ')}</div>
                    <div><strong>Risk Level:</strong> {config.strategy?.risk_level}</div>
                    <div><strong>Timeframes:</strong> {config.strategy?.timeframes?.join(', ')}</div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex items-center justify-between pt-6 border-t">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <div className="flex items-center gap-2">
              {currentStep < WIZARD_STEPS.length - 1 ? (
                <Button onClick={handleNext}>
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              ) : (
                <Button
                  onClick={handleCreateBot}
                  disabled={isCreating}
                  className="bg-gradient-to-r from-primary to-primary/80"
                >
                  {isCreating ? (
                    <>
                      <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Create Bot
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
/**
 * Bot Control Panel Component
 * Professional trading bot management and control interface
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useTradingBots } from '@/hooks/useTradingBots';
import { useExchange } from '@/contexts/ExchangeContext';
import { TradingBot, BotStatus, RiskLevel } from '@/types/tradingBot';
import {
  Bot,
  Play,
  Pause,
  Square,
  Settings,
  Activity,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Shield,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  Wifi,
  WifiOff,
  MoreVertical,
  Edit,
  Trash2,
  BarChart3,
  Target
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface BotControlPanelProps {
  bot: TradingBot;
  onEdit?: (bot: TradingBot) => void;
  onDelete?: (bot: TradingBot) => void;
  onViewDetails?: (bot: TradingBot) => void;
  className?: string;
}

const getStatusColor = (status: BotStatus) => {
  switch (status) {
    case BotStatus.RUNNING:
      return 'bg-green-500';
    case BotStatus.PAUSED:
      return 'bg-yellow-500';
    case BotStatus.STOPPED:
      return 'bg-gray-500';
    case BotStatus.ERROR:
      return 'bg-red-500';
    case BotStatus.STARTING:
    case BotStatus.STOPPING:
      return 'bg-blue-500';
    default:
      return 'bg-gray-500';
  }
};

const getStatusIcon = (status: BotStatus) => {
  switch (status) {
    case BotStatus.RUNNING:
      return <Activity className="h-4 w-4" />;
    case BotStatus.PAUSED:
      return <Pause className="h-4 w-4" />;
    case BotStatus.STOPPED:
      return <Square className="h-4 w-4" />;
    case BotStatus.ERROR:
      return <AlertTriangle className="h-4 w-4" />;
    case BotStatus.STARTING:
    case BotStatus.STOPPING:
      return <Clock className="h-4 w-4" />;
    default:
      return <Bot className="h-4 w-4" />;
  }
};

const getRiskLevelColor = (riskLevel: RiskLevel) => {
  switch (riskLevel) {
    case RiskLevel.LOW:
      return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900';
    case RiskLevel.MEDIUM:
      return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900';
    case RiskLevel.HIGH:
      return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900';
    default:
      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900';
  }
};

export const BotControlPanel: React.FC<BotControlPanelProps> = ({
  bot,
  onEdit,
  onDelete,
  onViewDetails,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { startBot, stopBot, pauseBot, resumeBot, isConnected } = useTradingBots();
  const { hasConnectedExchange } = useExchange();

  const handleBotAction = async (action: 'start' | 'stop' | 'pause' | 'resume') => {
    // Check exchange connection for actions that require trading
    if ((action === 'start' || action === 'resume') && !hasConnectedExchange()) {
      toast({
        title: 'Exchange Connection Required',
        description: 'You need to connect an exchange account to start trading bots. Please go to Settings to connect your exchange.',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);

    try {
      let success = false;

      switch (action) {
        case 'start':
          success = await startBot(bot.id);
          break;
        case 'stop':
          success = await stopBot(bot.id);
          break;
        case 'pause':
          success = await pauseBot(bot.id);
          break;
        case 'resume':
          success = await resumeBot(bot.id);
          break;
      }

      if (!success) {
        toast({
          title: 'Error',
          description: `Failed to ${action} bot`,
          variant: 'destructive'
        });
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || `Failed to ${action} bot`,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const performance = bot.metrics.performance;
  const isProfit = performance.total_return >= 0;

  return (
    <Card className={`transition-all duration-200 hover:shadow-lg ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className={`w-3 h-3 rounded-full ${getStatusColor(bot.status)} animate-pulse`} />
              {!isConnected && (
                <WifiOff className="absolute -top-1 -right-1 h-3 w-3 text-red-500" />
              )}
            </div>
            <div>
              <CardTitle className="text-lg">{bot.config.name}</CardTitle>
              <p className="text-sm text-muted-foreground">{bot.config.description}</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Badge className={getRiskLevelColor(bot.config.strategy.risk_level)}>
              {bot.config.strategy.risk_level.toUpperCase()}
            </Badge>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onViewDetails?.(bot)}>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onEdit?.(bot)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Configuration
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDelete?.(bot)}
                  className="text-red-600 dark:text-red-400"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Bot
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Status and Strategy Info */}
        <div className="flex items-center gap-4 mt-3">
          <div className="flex items-center gap-2">
            {getStatusIcon(bot.status)}
            <span className="text-sm font-medium capitalize">{bot.status.replace('_', ' ')}</span>
          </div>
          <Separator orientation="vertical" className="h-4" />
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            <span className="text-sm">{bot.config.strategy.strategy_type.replace('_', ' ')}</span>
          </div>
          <Separator orientation="vertical" className="h-4" />
          <div className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            <span className="text-sm">{bot.config.symbols.join(', ')}</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Error Message */}
        {bot.status === BotStatus.ERROR && bot.error_message && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{bot.error_message}</AlertDescription>
          </Alert>
        )}

        {/* Performance Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className={`text-lg font-bold ${isProfit ? 'text-green-600' : 'text-red-600'}`}>
              {formatCurrency(performance.total_return)}
            </div>
            <div className="text-xs text-muted-foreground">Total P&L</div>
          </div>

          <div className="text-center">
            <div className={`text-lg font-bold ${isProfit ? 'text-green-600' : 'text-red-600'}`}>
              {formatPercentage(performance.total_return_pct)}
            </div>
            <div className="text-xs text-muted-foreground">Return %</div>
          </div>

          <div className="text-center">
            <div className="text-lg font-bold">{performance.win_rate.toFixed(1)}%</div>
            <div className="text-xs text-muted-foreground">Win Rate</div>
          </div>

          <div className="text-center">
            <div className="text-lg font-bold">{performance.total_trades}</div>
            <div className="text-xs text-muted-foreground">Trades</div>
          </div>
        </div>

        {/* Risk Metrics */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Max Drawdown</span>
            <span className="font-medium">{formatPercentage(performance.max_drawdown_pct)}</span>
          </div>
          <Progress
            value={Math.abs(performance.max_drawdown_pct)}
            max={bot.config.risk_management.max_drawdown}
            className="h-2"
          />

          <div className="flex items-center justify-between text-sm">
            <span>Sharpe Ratio</span>
            <span className="font-medium">{performance.sharpe_ratio.toFixed(2)}</span>
          </div>
        </div>

        {/* Current Positions */}
        {bot.metrics.current_positions.length > 0 && (
          <div className="space-y-2">
            <div className="text-sm font-medium">Active Positions ({bot.metrics.current_positions.length})</div>
            <div className="space-y-1">
              {bot.metrics.current_positions.slice(0, 3).map((position, index) => (
                <div key={index} className="flex items-center justify-between text-xs bg-muted/50 rounded p-2">
                  <span>{position.symbol} {position.side}</span>
                  <span className={position.unrealized_pnl >= 0 ? 'text-green-600' : 'text-red-600'}>
                    {formatCurrency(position.unrealized_pnl)}
                  </span>
                </div>
              ))}
              {bot.metrics.current_positions.length > 3 && (
                <div className="text-xs text-muted-foreground text-center">
                  +{bot.metrics.current_positions.length - 3} more positions
                </div>
              )}
            </div>
          </div>
        )}

        {/* Exchange Connection Warning for Stopped Bots */}
        {bot.status === BotStatus.STOPPED && !hasConnectedExchange() && (
          <Alert className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              Connect an exchange account to start this bot. Go to Settings → Exchange Connection.
            </AlertDescription>
          </Alert>
        )}

        {/* Control Buttons */}
        <div className="flex items-center gap-2 pt-2 border-t">
          {bot.status === BotStatus.STOPPED && (
            <Button
              onClick={() => handleBotAction('start')}
              disabled={isLoading}
              size="sm"
              className="flex-1"
              variant={!hasConnectedExchange() ? "outline" : "default"}
            >
              <Play className="h-4 w-4 mr-2" />
              Start
            </Button>
          )}

          {bot.status === BotStatus.RUNNING && (
            <>
              <Button
                onClick={() => handleBotAction('pause')}
                disabled={isLoading}
                variant="outline"
                size="sm"
                className="flex-1"
              >
                <Pause className="h-4 w-4 mr-2" />
                Pause
              </Button>
              <Button
                onClick={() => handleBotAction('stop')}
                disabled={isLoading}
                variant="destructive"
                size="sm"
                className="flex-1"
              >
                <Square className="h-4 w-4 mr-2" />
                Stop
              </Button>
            </>
          )}

          {bot.status === BotStatus.PAUSED && (
            <>
              <Button
                onClick={() => handleBotAction('resume')}
                disabled={isLoading}
                size="sm"
                className="flex-1"
                variant={!hasConnectedExchange() ? "outline" : "default"}
              >
                <Play className="h-4 w-4 mr-2" />
                Resume
              </Button>
              <Button
                onClick={() => handleBotAction('stop')}
                disabled={isLoading}
                variant="outline"
                size="sm"
                className="flex-1"
              >
                <Square className="h-4 w-4 mr-2" />
                Stop
              </Button>
            </>
          )}

          {(bot.status === BotStatus.STARTING || bot.status === BotStatus.STOPPING) && (
            <Button disabled size="sm" className="flex-1">
              <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
              {bot.status === BotStatus.STARTING ? 'Starting...' : 'Stopping...'}
            </Button>
          )}

          {bot.status === BotStatus.ERROR && (
            <Button
              onClick={() => handleBotAction('start')}
              disabled={isLoading}
              variant="outline"
              size="sm"
              className="flex-1"
            >
              <Play className="h-4 w-4 mr-2" />
              Restart
            </Button>
          )}
        </div>

        {/* Last Activity */}
        <div className="text-xs text-muted-foreground text-center pt-2 border-t">
          {bot.last_signal_time ? (
            <>Last signal: {new Date(bot.last_signal_time).toLocaleString()}</>
          ) : (
            <>Created: {new Date(bot.created_at).toLocaleString()}</>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
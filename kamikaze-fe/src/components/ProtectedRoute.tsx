import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useExchange } from '@/contexts/ExchangeContext';
import { ExchangeSelectionModal } from '@/components/exchange/ExchangeSelectionModal';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const {
    hasConnectedExchange,
    showConnectionModal,
    setShowConnectionModal
  } = useExchange();
  const location = useLocation();

  // Show exchange connection modal after authentication if no exchange is connected
  useEffect(() => {
    if (isAuthenticated && !isLoading && !hasConnectedExchange()) {
      // Small delay to ensure smooth transition after login
      const timer = setTimeout(() => {
        setShowConnectionModal(true);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, isLoading, hasConnectedExchange, setShowConnectionModal]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect to landing page if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  return (
    <>
      {children}
      <ExchangeSelectionModal
        open={showConnectionModal}
        onOpenChange={setShowConnectionModal}
        onComplete={() => {
          console.log('✅ [PROTECTED_ROUTE] Exchange connection completed successfully');
          // Close the modal and update the state
          setShowConnectionModal(false);
        }}
      />
    </>
  );
};

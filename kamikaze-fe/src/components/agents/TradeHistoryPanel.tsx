import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Target,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';

interface Trade {
  id: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  amount: number;
  price: number;
  total: number;
  profit: number;
  timestamp: string;
  status: 'completed' | 'failed' | 'pending';
  confidence?: number;
  strategy?: string;
}

interface TradeHistoryPanelProps {
  trades: Trade[];
  className?: string;
}

export function TradeHistoryPanel({ trades, className }: TradeHistoryPanelProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(amount);
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getSideColor = (side: Trade['side']) => {
    return side === 'BUY' ?
      'bg-green-400/10 text-green-400 border-green-400/20' :
      'bg-red-400/10 text-red-400 border-red-400/20';
  };

  const getSideIcon = (side: Trade['side']) => {
    return side === 'BUY' ?
      <TrendingUp className="w-3 h-3" /> :
      <TrendingDown className="w-3 h-3" />;
  };

  const getStatusIcon = (status: Trade['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-400" />;
      case 'pending':
        return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      default:
        return null;
    }
  };

  const getProfitColor = (profit: number) => {
    return profit >= 0 ? 'text-green-400' : 'text-red-400';
  };

  const getProfitIcon = (profit: number) => {
    return profit >= 0 ? 
      <TrendingUp className="w-3 h-3" /> : 
      <TrendingDown className="w-3 h-3" />;
  };

  // Calculate summary stats
  const totalTrades = trades.length;
  const completedTrades = trades.filter(t => t.status === 'completed');
  const totalProfit = completedTrades.reduce((sum, trade) => sum + trade.profit, 0);
  const winningTrades = completedTrades.filter(t => t.profit > 0).length;
  const winRate = completedTrades.length > 0 ? (winningTrades / completedTrades.length) * 100 : 0;

  return (
    <Card className={`glass-card ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <Clock className="w-5 h-5 text-primary" />
            Trade History
            {totalTrades > 0 && (
              <Badge variant="secondary">
                {totalTrades}
              </Badge>
            )}
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {/* Summary Stats */}
        {completedTrades.length > 0 && (
          <div className="mb-4 p-4 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-sm text-muted-foreground">Total P&L</div>
                <div className={`text-lg font-semibold flex items-center justify-center gap-1 ${getProfitColor(totalProfit)}`}>
                  {getProfitIcon(totalProfit)}
                  {formatCurrency(totalProfit)}
                </div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Win Rate</div>
                <div className="text-lg font-semibold text-foreground">
                  {winRate.toFixed(1)}%
                </div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Completed</div>
                <div className="text-lg font-semibold text-foreground">
                  {completedTrades.length}
                </div>
              </div>
            </div>
          </div>
        )}

        <ScrollArea className="h-[400px] pr-4">
          {trades.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
              <Clock className="w-8 h-8 mb-2 opacity-50" />
              <p className="text-sm">No trades executed yet</p>
              <p className="text-xs">Trade history will appear when the agent executes trades</p>
            </div>
          ) : (
            <div className="space-y-3">
              {trades.map((trade) => (
                <div
                  key={trade.id}
                  className="p-4 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:shadow-elevated hover:border-primary/30 transition-all duration-300"
                >
                  {/* Trade Header */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className={getSideColor(trade.side)}>
                        {getSideIcon(trade.side)}
                        {trade.side}
                      </Badge>
                      <Badge variant="secondary" className="font-mono">
                        {trade.symbol}
                      </Badge>
                      {trade.strategy && (
                        <Badge variant="outline" className="text-xs">
                          {trade.strategy}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(trade.status)}
                      <span className="text-xs text-muted-foreground">
                        {formatTime(trade.timestamp)}
                      </span>
                    </div>
                  </div>

                  {/* Trade Details */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm mb-3">
                    <div>
                      <div className="text-muted-foreground">Amount</div>
                      <div className="font-medium text-foreground">
                        {trade.amount.toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Price</div>
                      <div className="font-medium text-foreground">
                        {formatCurrency(trade.price)}
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Total</div>
                      <div className="font-medium text-foreground">
                        {formatCurrency(trade.total)}
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">P&L</div>
                      <div className={`font-medium flex items-center gap-1 ${getProfitColor(trade.profit)}`}>
                        {getProfitIcon(trade.profit)}
                        {formatCurrency(Math.abs(trade.profit))}
                      </div>
                    </div>
                  </div>

                  {/* Additional Info */}
                  {trade.confidence && (
                    <div className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-2">
                        <Target className="w-3 h-3 text-muted-foreground" />
                        <span className="text-muted-foreground">
                          Confidence: {(trade.confidence * 100).toFixed(1)}%
                        </span>
                      </div>
                      <div className="text-muted-foreground">
                        Status: <span className="capitalize">{trade.status}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

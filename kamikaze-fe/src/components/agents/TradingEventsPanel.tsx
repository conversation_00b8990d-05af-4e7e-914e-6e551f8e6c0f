import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  TrendingUp, 
  TrendingDown, 
  Target, 
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign
} from 'lucide-react';

interface TradingEvent {
  id: string;
  type: 'trade' | 'order' | 'signal' | 'analysis' | 'error' | 'summary';
  timestamp: string;
  symbol?: string;
  action?: 'BUY' | 'SELL' | 'HOLD';
  amount?: number;
  price?: number;
  profit?: number;
  message: string;
  confidence?: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'running' | 'detected';
  // FluxTrader specific fields
  cycle?: number;
  max_cycles?: number;
  signals_found?: number;
  balance?: number;
  pairs_analyzed?: any[];
  signal_type?: 'PUMP' | 'DUMP';
  momentum?: number;
  change_24h?: number;
  signal_strength?: number;
}

interface TradingEventsPanelProps {
  events: TradingEvent[];
  className?: string;
}

export function TradingEventsPanel({ events, className }: TradingEventsPanelProps) {
  const getEventIcon = (type: TradingEvent['type'], action?: TradingEvent['action'], signal_type?: string) => {
    switch (type) {
      case 'trade':
        return action === 'BUY' ?
          <TrendingUp className="w-4 h-4 text-green-500" /> :
          action === 'SELL' ?
          <TrendingDown className="w-4 h-4 text-red-500" /> :
          <Activity className="w-4 h-4 text-blue-500" />;
      case 'order':
        return <Target className="w-4 h-4 text-orange-500" />;
      case 'signal':
        return signal_type === 'PUMP' ?
          <TrendingUp className="w-4 h-4 text-green-500" /> :
          signal_type === 'DUMP' ?
          <TrendingDown className="w-4 h-4 text-red-500" /> :
          <Activity className="w-4 h-4 text-purple-500" />;
      case 'analysis':
        return <Activity className="w-4 h-4 text-blue-500" />;
      case 'summary':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusIcon = (status: TradingEvent['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-3 h-3 text-green-500" />;
      case 'pending':
        return <Clock className="w-3 h-3 text-yellow-500" />;
      case 'running':
        return <Activity className="w-3 h-3 text-blue-500 animate-pulse" />;
      case 'detected':
        return <Target className="w-3 h-3 text-purple-500" />;
      case 'failed':
        return <AlertTriangle className="w-3 h-3 text-red-500" />;
      case 'cancelled':
        return <AlertTriangle className="w-3 h-3 text-gray-500" />;
      default:
        return null;
    }
  };

  const getActionColor = (action?: TradingEvent['action']) => {
    switch (action) {
      case 'BUY':
        return 'bg-green-500/10 text-green-500 border-green-500/20';
      case 'SELL':
        return 'bg-red-500/10 text-red-500 border-red-500/20';
      case 'HOLD':
        return 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20';
      default:
        return 'bg-gray-500/10 text-gray-500 border-gray-500/20';
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    });
  };

  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) {
      return '$0.00';
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  return (
    <Card className={`glass-card ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
          <Activity className="w-5 h-5 text-primary" />
          Live Trading Events
          {events.length > 0 && (
            <Badge variant="secondary" className="ml-auto">
              {events.length}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          {events.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
              <Activity className="w-8 h-8 mb-2 opacity-50" />
              <p className="text-sm">No trading events yet</p>
              <p className="text-xs">Events will appear when the agent is active</p>
            </div>
          ) : (
            <div className="space-y-3">
              {events.map((event) => (
                <div
                  key={event.id}
                  className="p-3 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:shadow-elevated hover:border-primary/30 transition-all duration-300"
                >
                  {/* Event Header */}
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getEventIcon(event.type, event.action, event.signal_type)}
                      <span className="text-sm font-medium text-foreground capitalize">
                        {event.type}
                      </span>
                      {event.signal_type && (
                        <Badge variant="outline" className={event.signal_type === 'PUMP' ? 'text-green-500 border-green-500' : 'text-red-500 border-red-500'}>
                          {event.signal_type}
                        </Badge>
                      )}
                      {event.action && (
                        <Badge variant="outline" className={getActionColor(event.action)}>
                          {event.action}
                        </Badge>
                      )}
                      {event.symbol && (
                        <Badge variant="secondary" className="text-xs">
                          {event.symbol}
                        </Badge>
                      )}
                      {event.cycle && (
                        <Badge variant="outline" className="text-xs text-blue-500 border-blue-500">
                          Cycle {event.cycle}/{event.max_cycles}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(event.status)}
                      <span className="text-xs text-muted-foreground">
                        {formatTime(event.timestamp)}
                      </span>
                    </div>
                  </div>

                  {/* Event Message */}
                  <p className="text-sm text-foreground mb-2">{event.message}</p>

                  {/* Event Details */}
                  {(event.amount || event.price || event.profit !== undefined || event.confidence ||
                    event.momentum !== undefined || event.change_24h !== undefined || event.signal_strength !== undefined ||
                    event.balance !== undefined || event.signals_found !== undefined) && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                      {event.amount && (
                        <div>
                          <span className="text-muted-foreground">Amount:</span>
                          <span className="ml-1 font-medium text-foreground">
                            {event.amount.toLocaleString()}
                          </span>
                        </div>
                      )}
                      {event.price && (
                        <div>
                          <span className="text-muted-foreground">Price:</span>
                          <span className="ml-1 font-medium text-foreground">
                            {formatCurrency(event.price)}
                          </span>
                        </div>
                      )}
                      {event.profit !== undefined && (
                        <div>
                          <span className="text-muted-foreground">P&L:</span>
                          <span className={`ml-1 font-medium flex items-center gap-1 ${
                            event.profit >= 0 ? 'text-green-500' : 'text-red-500'
                          }`}>
                            {event.profit >= 0 ? (
                              <TrendingUp className="w-3 h-3" />
                            ) : (
                              <TrendingDown className="w-3 h-3" />
                            )}
                            {formatCurrency(Math.abs(event.profit))}
                          </span>
                        </div>
                      )}
                      {event.confidence && (
                        <div>
                          <span className="text-muted-foreground">Confidence:</span>
                          <span className="ml-1 font-medium text-foreground">
                            {(event.confidence * 100).toFixed(1)}%
                          </span>
                        </div>
                      )}
                      {event.momentum !== undefined && event.momentum !== null && (
                        <div>
                          <span className="text-muted-foreground">Momentum:</span>
                          <span className={`ml-1 font-medium ${event.momentum >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                            {event.momentum >= 0 ? '+' : ''}{event.momentum.toFixed(4)}%
                          </span>
                        </div>
                      )}
                      {event.change_24h !== undefined && event.change_24h !== null && (
                        <div>
                          <span className="text-muted-foreground">24h Change:</span>
                          <span className={`ml-1 font-medium ${event.change_24h >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                            {event.change_24h >= 0 ? '+' : ''}{event.change_24h.toFixed(2)}%
                          </span>
                        </div>
                      )}
                      {event.signal_strength !== undefined && event.signal_strength !== null && (
                        <div>
                          <span className="text-muted-foreground">Signal Strength:</span>
                          <span className="ml-1 font-medium text-purple-500">
                            {event.signal_strength.toFixed(2)}
                          </span>
                        </div>
                      )}
                      {event.balance !== undefined && event.balance !== null && (
                        <div>
                          <span className="text-muted-foreground">Balance:</span>
                          <span className="ml-1 font-medium text-foreground">
                            {formatCurrency(event.balance)}
                          </span>
                        </div>
                      )}
                      {event.signals_found !== undefined && (
                        <div>
                          <span className="text-muted-foreground">Signals Found:</span>
                          <span className="ml-1 font-medium text-foreground">
                            {event.signals_found}
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

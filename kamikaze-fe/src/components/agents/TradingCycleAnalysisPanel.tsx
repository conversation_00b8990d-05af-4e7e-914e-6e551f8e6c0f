import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Clock,
  Target,
  Zap,
  PieChart,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface CycleAnalysisData {
  cycle: number;
  max_cycles: number;
  status: 'starting' | 'running' | 'completed' | 'error';
  pairs_analyzed: Array<{
    symbol: string;
    price: number;
    change_pct: number;
    volume: number;
    high_24h: number;
    low_24h: number;
  }>;
  signals_detected: Array<{
    symbol: string;
    signal_type: 'PUMP' | 'DUMP';
    confidence: number;
    momentum: number;
  }>;
  market_conditions: {
    total_pairs_analyzed: number;
    signals_found: number;
    cycle_duration: number;
    average_price_change: number;
    highest_volume_pair?: any;
    most_volatile_pair?: any;
    pairs_to_analyze?: string[];
    balance?: number;
    cycle_start_time?: string;
  };
  performance_metrics: {
    current_balance: number;
    signals_detection_rate: number;
    cycle_efficiency: number;
  };
  analysis_summary: string;
  next_cycle_eta?: string;
  timestamp: string;
}

interface TradingCycleAnalysisPanelProps {
  cycleData: CycleAnalysisData | null;
  className?: string;
}

export function TradingCycleAnalysisPanel({ cycleData, className }: TradingCycleAnalysisPanelProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 8
    }).format(value);
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds.toFixed(0)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'starting':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'running':
        return <Activity className="w-4 h-4 text-green-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'starting':
        return 'text-blue-500 border-blue-500';
      case 'running':
        return 'text-green-500 border-green-500';
      case 'completed':
        return 'text-green-600 border-green-600';
      case 'error':
        return 'text-red-500 border-red-500';
      default:
        return 'text-muted-foreground border-muted-foreground';
    }
  };

  if (!cycleData) {
    return (
      <Card className={cn('glass-card', className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-primary" />
            Trading Cycle Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
            <Activity className="w-8 h-8 mb-2 opacity-50" />
            <p className="text-sm">No cycle data available</p>
            <p className="text-xs">Analysis will appear when trading cycles start</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const cycleProgress = (cycleData.cycle / cycleData.max_cycles) * 100;
  const detectionRate = cycleData.performance_metrics?.signals_detection_rate || 0;

  return (
    <Card className={cn('glass-card', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-primary" />
            Trading Cycle Analysis
          </CardTitle>
          <div className="flex items-center gap-2">
            {getStatusIcon(cycleData.status)}
            <Badge variant="outline" className={getStatusColor(cycleData.status)}>
              Cycle {cycleData.cycle}/{cycleData.max_cycles}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Cycle Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Cycle Progress</span>
            <span className="font-medium">{cycleProgress.toFixed(1)}%</span>
          </div>
          <Progress value={cycleProgress} className="h-2" />
        </div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <div className="p-3 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2 mb-1">
              <Target className="w-4 h-4 text-blue-400" />
              <span className="text-xs text-muted-foreground">Pairs Analyzed</span>
            </div>
            <p className="text-lg font-semibold text-foreground">
              {cycleData.market_conditions?.total_pairs_analyzed || 0}
            </p>
          </div>

          <div className="p-3 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2 mb-1">
              <Zap className="w-4 h-4 text-yellow-400" />
              <span className="text-xs text-muted-foreground">Signals Found</span>
            </div>
            <p className="text-lg font-semibold text-foreground">
              {cycleData.market_conditions?.signals_found || 0}
            </p>
          </div>

          <div className="p-3 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2 mb-1">
              <PieChart className="w-4 h-4 text-green-400" />
              <span className="text-xs text-muted-foreground">Detection Rate</span>
            </div>
            <p className="text-lg font-semibold text-foreground">
              {detectionRate.toFixed(1)}%
            </p>
          </div>

          <div className="p-3 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2 mb-1">
              <Clock className="w-4 h-4 text-purple-400" />
              <span className="text-xs text-muted-foreground">Duration</span>
            </div>
            <p className="text-lg font-semibold text-foreground">
              {formatDuration(cycleData.market_conditions?.cycle_duration || 0)}
            </p>
          </div>
        </div>

        {/* Analysis Summary */}
        <div className="p-3 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
          <h4 className="text-sm font-medium text-foreground mb-2 flex items-center gap-2">
            <Activity className="w-4 h-4 text-primary" />
            Analysis Summary
          </h4>
          <p className="text-sm text-muted-foreground">{cycleData.analysis_summary}</p>
        </div>

        {/* Market Conditions */}
        {cycleData.market_conditions && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-primary" />
              Market Conditions
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
              <div className="p-2 rounded border border-border/20 bg-background/50">
                <span className="text-muted-foreground">Avg Price Change:</span>
                <span className={`ml-2 font-medium ${
                  (cycleData.market_conditions.average_price_change || 0) >= 0 ? 'text-green-500' : 'text-red-500'
                }`}>
                  {(cycleData.market_conditions.average_price_change || 0) >= 0 ? '+' : ''}
                  {(cycleData.market_conditions.average_price_change || 0).toFixed(3)}%
                </span>
              </div>
              
              <div className="p-2 rounded border border-border/20 bg-background/50">
                <span className="text-muted-foreground">Current Balance:</span>
                <span className="ml-2 font-medium text-foreground">
                  {formatCurrency(cycleData.performance_metrics?.current_balance || 0)}
                </span>
              </div>

              {cycleData.market_conditions.highest_volume_pair && (
                <div className="p-2 rounded border border-border/20 bg-background/50">
                  <span className="text-muted-foreground">Highest Volume:</span>
                  <span className="ml-2 font-medium text-foreground">
                    {cycleData.market_conditions.highest_volume_pair.symbol}
                  </span>
                </div>
              )}

              {cycleData.market_conditions.most_volatile_pair && (
                <div className="p-2 rounded border border-border/20 bg-background/50">
                  <span className="text-muted-foreground">Most Volatile:</span>
                  <span className="ml-2 font-medium text-foreground">
                    {cycleData.market_conditions.most_volatile_pair.symbol}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Next Cycle ETA */}
        {cycleData.next_cycle_eta && (
          <div className="flex items-center justify-between p-3 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2">
              <ArrowRight className="w-4 h-4 text-blue-400" />
              <span className="text-sm text-muted-foreground">Next Cycle</span>
            </div>
            <span className="text-sm font-medium text-foreground">
              {formatTime(cycleData.next_cycle_eta)}
            </span>
          </div>
        )}

        {/* Last Updated */}
        <div className="text-xs text-muted-foreground text-center">
          Last updated: {formatTime(cycleData.timestamp)}
        </div>
      </CardContent>
    </Card>
  );
}

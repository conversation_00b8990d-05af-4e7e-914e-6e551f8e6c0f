import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { ExchangeContextType, ExchangeConnection, ExchangeCredentials } from '@/types/exchange';
import { SUPPORTED_EXCHANGES, STORAGE_KEYS, encryptCredentials, decryptCredentials } from '@/lib/exchangeUtils';
import { mcpClient } from '@/services/mcpClient';
import { useToast } from '@/hooks/use-toast';

export type TradingEnvironment = 'testnet' | 'mainnet';

export interface EnvironmentState {
  currentEnvironment: TradingEnvironment;
  isTestnetMode: boolean;
  environmentHistory: Array<{
    environment: TradingEnvironment;
    timestamp: Date;
    exchangeId: string;
  }>;
}

const ExchangeContext = createContext<ExchangeContextType | undefined>(undefined);

interface ExchangeProviderProps {
  children: ReactNode;
}

export const ExchangeProvider: React.FC<ExchangeProviderProps> = ({ children }) => {
  const [exchanges, setExchanges] = useState<Record<string, ExchangeConnection>>({});
  const [selectedExchange, setSelectedExchange] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [showConnectionModal, setShowConnectionModal] = useState(false);
  const [environmentState, setEnvironmentState] = useState<EnvironmentState>({
    currentEnvironment: 'testnet',
    isTestnetMode: true,
    environmentHistory: []
  });
  const { toast } = useToast();

  // Initialize exchanges from storage on mount
  useEffect(() => {
    initializeExchanges();
    loadSelectedExchange();
  }, []);

  // Initialize MCP client connection
  useEffect(() => {
    const initMCP = async () => {
      try {
        console.log('🔧 Initializing MCP client connection...');
        const connected = await mcpClient.connect();
        console.log(`🔗 MCP client initialization: ${connected ? 'success' : 'failed'}`);
      } catch (error) {
        console.error('❌ Failed to connect to MCP server:', error);
      }
    };
    initMCP();

    return () => {
      console.log('🔌 Disconnecting MCP client...');
      mcpClient.disconnect();
    };
  }, []);

  const initializeExchanges = useCallback(() => {
    try {
      const storedConnections = localStorage.getItem(STORAGE_KEYS.EXCHANGE_CONNECTIONS);
      let savedExchanges: Record<string, ExchangeConnection> = {};

      if (storedConnections) {
        const decrypted = decryptCredentials(storedConnections);
        savedExchanges = JSON.parse(decrypted);
      }

      // Initialize all supported exchanges
      const initialExchanges: Record<string, ExchangeConnection> = {};
      
      SUPPORTED_EXCHANGES.forEach(exchangeInfo => {
        const saved = savedExchanges[exchangeInfo.id];
        initialExchanges[exchangeInfo.id] = {
          id: exchangeInfo.id,
          name: exchangeInfo.name,
          status: saved?.status || 'disconnected',
          credentials: saved?.credentials,
          lastConnected: saved?.lastConnected,
          error: saved?.error,
          isAvailable: exchangeInfo.isAvailable,
          comingSoon: exchangeInfo.comingSoon
        };
      });

      setExchanges(initialExchanges);
    } catch (error) {
      console.error('Failed to initialize exchanges:', error);
      // Initialize with default state if loading fails
      const defaultExchanges: Record<string, ExchangeConnection> = {};
      SUPPORTED_EXCHANGES.forEach(exchangeInfo => {
        defaultExchanges[exchangeInfo.id] = {
          id: exchangeInfo.id,
          name: exchangeInfo.name,
          status: 'disconnected',
          isAvailable: exchangeInfo.isAvailable,
          comingSoon: exchangeInfo.comingSoon
        };
      });
      setExchanges(defaultExchanges);
    }
  }, []);

  const loadSelectedExchange = useCallback(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.SELECTED_EXCHANGE);
      if (stored) {
        setSelectedExchange(stored);
      }
    } catch (error) {
      console.error('Failed to load selected exchange:', error);
    }
  }, []);

  const saveExchangesToStorage = useCallback((exchangesToSave: Record<string, ExchangeConnection>) => {
    try {
      const encrypted = encryptCredentials(JSON.stringify(exchangesToSave));
      localStorage.setItem(STORAGE_KEYS.EXCHANGE_CONNECTIONS, encrypted);
    } catch (error) {
      console.error('Failed to save exchanges to storage:', error);
    }
  }, []);

  const connectExchange = useCallback(async (exchangeId: string, credentials: ExchangeCredentials): Promise<boolean> => {
    if (!exchanges[exchangeId]?.isAvailable) {
      toast({
        title: "Exchange Not Available",
        description: "This exchange is not yet supported.",
        variant: "destructive",
      });
      return false;
    }

    setIsConnecting(true);
    
    try {
      // Update status to connecting
      setExchanges(prev => ({
        ...prev,
        [exchangeId]: {
          ...prev[exchangeId],
          status: 'connecting',
          error: undefined
        }
      }));

      // Test connection based on exchange type
      console.log(`🚀 Starting connection test for ${exchangeId}...`);
      let connectionResult;
      if (exchangeId === 'binance') {
        console.log('🔧 Testing Binance connection with credentials:', {
          apiKey: credentials.apiKey?.substring(0, 8) + '...',
          testnet: credentials.testnet,
          timestamp: new Date().toISOString()
        });

        try {
          // Use the new exchange service that handles both testing and saving
          const { exchangeService } = await import('@/services/exchangeService');
          connectionResult = await exchangeService.testBinanceConnection({
            apiKey: credentials.apiKey,
            secretKey: credentials.secretKey,
            testnet: credentials.testnet
          });
          console.log('📊 Binance connection result:', connectionResult);
        } catch (error) {
          console.error('❌ Exchange service failed:', error);
          connectionResult = {
            success: false,
            error: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
          };
        }
      } else {
        throw new Error('Exchange not supported yet');
      }

      if (connectionResult.success) {
        // Connection successful
        const updatedExchanges = {
          ...exchanges,
          [exchangeId]: {
            ...exchanges[exchangeId],
            status: 'connected' as const,
            credentials,
            lastConnected: Date.now(),
            error: undefined
          }
        };

        setExchanges(updatedExchanges);
        setSelectedExchange(exchangeId);
        
        // Save to storage
        saveExchangesToStorage(updatedExchanges);
        localStorage.setItem(STORAGE_KEYS.SELECTED_EXCHANGE, exchangeId);

        toast({
          title: "Exchange Connected",
          description: `Successfully connected to ${exchanges[exchangeId].name}`,
        });

        return true;
      } else {
        // Connection failed
        const errorMessage = connectionResult.error || 'Connection failed';
        setExchanges(prev => ({
          ...prev,
          [exchangeId]: {
            ...prev[exchangeId],
            status: 'error',
            error: errorMessage
          }
        }));

        toast({
          title: "Connection Failed",
          description: errorMessage,
          variant: "destructive",
        });

        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      setExchanges(prev => ({
        ...prev,
        [exchangeId]: {
          ...prev[exchangeId],
          status: 'error',
          error: errorMessage
        }
      }));

      toast({
        title: "Connection Error",
        description: errorMessage,
        variant: "destructive",
      });

      return false;
    } finally {
      setIsConnecting(false);
    }
  }, [exchanges, toast, saveExchangesToStorage]);

  const disconnectExchange = useCallback((exchangeId: string) => {
    const updatedExchanges = {
      ...exchanges,
      [exchangeId]: {
        ...exchanges[exchangeId],
        status: 'disconnected' as const,
        credentials: undefined,
        error: undefined
      }
    };

    setExchanges(updatedExchanges);
    
    if (selectedExchange === exchangeId) {
      setSelectedExchange(null);
      localStorage.removeItem(STORAGE_KEYS.SELECTED_EXCHANGE);
    }

    saveExchangesToStorage(updatedExchanges);

    toast({
      title: "Exchange Disconnected",
      description: `Disconnected from ${exchanges[exchangeId].name}`,
    });
  }, [exchanges, selectedExchange, toast, saveExchangesToStorage]);

  const testConnection = useCallback(async (exchangeId: string, credentials: ExchangeCredentials): Promise<boolean> => {
    try {
      if (exchangeId === 'binance') {
        const result = await mcpClient.testBinanceConnection(credentials);
        return result.success;
      }
      return false;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }, []);

  const getConnectionStatus = useCallback((exchangeId: string): ExchangeConnection['status'] => {
    return exchanges[exchangeId]?.status || 'disconnected';
  }, [exchanges]);

  const hasConnectedExchange = useCallback((): boolean => {
    return Object.values(exchanges).some(exchange => exchange.status === 'connected');
  }, [exchanges]);

  // Environment management functions
  const setEnvironment = useCallback((environment: TradingEnvironment, exchangeId?: string) => {
    setEnvironmentState(prev => ({
      currentEnvironment: environment,
      isTestnetMode: environment === 'testnet',
      environmentHistory: [
        ...prev.environmentHistory,
        {
          environment,
          timestamp: new Date(),
          exchangeId: exchangeId || 'unknown'
        }
      ].slice(-10) // Keep last 10 environment changes
    }));

    // Store environment preference
    localStorage.setItem('kamikaze_trading_environment', environment);

    // Show environment change notification
    toast({
      title: `Switched to ${environment.toUpperCase()} mode`,
      description: environment === 'testnet'
        ? '🧪 You are now in simulation mode with fake funds'
        : '💰 You are now in LIVE trading mode with real money',
      variant: environment === 'testnet' ? 'default' : 'destructive'
    });
  }, [toast]);

  const getEnvironmentForExchange = useCallback((exchangeId: string): TradingEnvironment | null => {
    const exchange = exchanges[exchangeId];
    if (!exchange?.credentials) return null;

    // Always return mainnet (testnet removed)
    return 'mainnet';
  }, [exchanges]);

  const isTestnetMode = useCallback((): boolean => {
    return false; // Always false (testnet removed)
  }, []);

  // Load environment preference on mount
  useEffect(() => {
    const savedEnvironment = localStorage.getItem('kamikaze_trading_environment') as TradingEnvironment;
    if (savedEnvironment && (savedEnvironment === 'testnet' || savedEnvironment === 'mainnet')) {
      setEnvironmentState(prev => ({
        ...prev,
        currentEnvironment: savedEnvironment,
        isTestnetMode: savedEnvironment === 'testnet'
      }));
    }
  }, []);

  const value: ExchangeContextType = {
    exchanges,
    selectedExchange,
    isConnecting,
    connectExchange,
    disconnectExchange,
    testConnection,
    getConnectionStatus,
    hasConnectedExchange,
    showConnectionModal,
    setShowConnectionModal,
    environmentState,
    setEnvironment,
    getEnvironmentForExchange,
    isTestnetMode
  };

  return (
    <ExchangeContext.Provider value={value}>
      {children}
    </ExchangeContext.Provider>
  );
};

export const useExchange = (): ExchangeContextType => {
  const context = useContext(ExchangeContext);
  if (context === undefined) {
    throw new Error('useExchange must be used within an ExchangeProvider');
  }
  return context;
};

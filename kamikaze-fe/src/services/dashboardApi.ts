/**
 * Dashboard API Service
 * Handles all dashboard-related API calls with real Binance data
 */

import { apiClient } from './api';

// Types for dashboard data
export interface AssetBalance {
  asset: string;
  balance: number;
  usd_value: number;
  btc_value: number;
  percentage: number;
}

export interface PortfolioMetrics {
  total_value_usd: number;
  total_value_btc: number;
  daily_pnl: number;
  daily_pnl_percent: number;
  asset_allocation: AssetBalance[];
  btc_price_usd: number;
  timestamp: number;
}

export interface TradingBotMetrics {
  id: string;
  name: string;
  status: 'active' | 'paused' | 'stopped' | 'error';
  strategy: string;
  profit: number;
  profit_percentage: number;
  trades: number;
  win_rate: number;
  last_trade?: string;
  risk_level: 'low' | 'medium' | 'high';
}

export interface RecentTrade {
  id: string;
  symbol: string;
  side: string;
  quantity: number;
  price: number;
  total: number;
  timestamp: number;
  pnl: number;
}

export interface TopAsset {
  symbol: string;
  name: string;
  price: number;
  change_percent: number;
}

export interface RiskMetrics {
  max_drawdown: number;
  sharpe_ratio: number;
  volatility: number;
  beta: number;
  var_95: number;
}

export interface AIInsight {
  id: string;
  type: 'opportunity' | 'warning' | 'info';
  title: string;
  description: string;
  confidence: number;
  timestamp: string;
}

export interface DashboardOverview {
  portfolio: PortfolioMetrics;
  trading_bots: TradingBotMetrics[];
  recent_trades: RecentTrade[];
  top_assets: TopAsset[];
  risk_metrics: RiskMetrics;
  ai_insights: AIInsight[];
  timestamp: number;
  environment: string;
}

export interface QuickStats {
  daily_pnl: number;
  active_bots: number;
  total_bots: number;
  win_rate: number;
  trades_today: number;
}

export interface QuickStatsResponse {
  stats: QuickStats;
  timestamp: number;
}

export interface PortfolioPerformancePoint {
  timestamp: number;
  value_usd: number;
  value_btc: number;
  pnl: number;
  pnl_percent: number;
}

export interface PortfolioPerformanceResponse {
  period: string;
  data_points: PortfolioPerformancePoint[];
  total_return: number;
  total_return_percent: number;
  timestamp: number;
}

class DashboardApiService {
  /**
   * Get complete dashboard overview with real data
   */
  async getDashboardOverview(): Promise<DashboardOverview> {
    try {
      console.log('🔄 [DashboardAPI] Fetching dashboard overview...');
      // Use real dashboard endpoint with authentication
      const response = await apiClient.get<DashboardOverview>('/api/v1/dashboard/overview');
      
      if (response.data) {
        console.log('✅ [DashboardAPI] Dashboard overview loaded successfully', {
          portfolioValue: response.data.portfolio.total_value_usd,
          environment: response.data.environment,
          botsCount: response.data.trading_bots.length,
          tradesCount: response.data.recent_trades.length
        });
        return response.data;
      } else {
        throw new Error('No data received from dashboard API');
      }
    } catch (error: any) {
      console.error('💥 [DashboardAPI] Failed to fetch dashboard overview:', error);
      throw new Error(error.response?.data?.detail || error.message || 'Failed to fetch dashboard overview');
    }
  }

  /**
   * Get quick stats for dashboard header
   */
  async getQuickStats(): Promise<QuickStats> {
    try {
      console.log('🔄 [DashboardAPI] Fetching quick stats...');
      const response = await apiClient.get<QuickStatsResponse>('/api/v1/dashboard/quick-stats');
      
      if (response.data) {
        console.log('✅ [DashboardAPI] Quick stats loaded successfully');
        return response.data.stats;
      } else {
        throw new Error('No data received from quick stats API');
      }
    } catch (error: any) {
      console.error('💥 [DashboardAPI] Failed to fetch quick stats:', error);
      throw new Error(error.response?.data?.detail || error.message || 'Failed to fetch quick stats');
    }
  }

  /**
   * Get portfolio performance over time
   */
  async getPortfolioPerformance(period: string = '1M'): Promise<PortfolioPerformanceResponse> {
    try {
      console.log('🔄 [DashboardAPI] Fetching portfolio performance...', { period });
      const response = await apiClient.get<PortfolioPerformanceResponse>(
        `/api/v1/dashboard/performance?period=${period}`
      );
      
      if (response.data) {
        console.log('✅ [DashboardAPI] Portfolio performance loaded successfully');
        return response.data;
      } else {
        throw new Error('No data received from portfolio performance API');
      }
    } catch (error: any) {
      console.error('💥 [DashboardAPI] Failed to fetch portfolio performance:', error);
      throw new Error(error.response?.data?.detail || error.message || 'Failed to fetch portfolio performance');
    }
  }

  /**
   * Refresh dashboard data (force cache invalidation)
   */
  async refreshDashboard(): Promise<DashboardOverview> {
    try {
      console.log('🔄 [DashboardAPI] Refreshing dashboard data...');
      // Add cache-busting parameter
      const timestamp = Date.now();
      const response = await apiClient.get<DashboardOverview>(
        `/api/v1/dashboard/overview?refresh=${timestamp}`
      );
      
      if (response.data) {
        console.log('✅ [DashboardAPI] Dashboard refreshed successfully');
        return response.data;
      } else {
        throw new Error('No data received from dashboard API');
      }
    } catch (error: any) {
      console.error('💥 [DashboardAPI] Failed to refresh dashboard:', error);
      throw new Error(error.response?.data?.detail || error.message || 'Failed to refresh dashboard');
    }
  }
}

// Export singleton instance
export const dashboardApi = new DashboardApiService();

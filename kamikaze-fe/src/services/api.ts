/**
 * Enhanced API client for FluxTrader backend
 * Handles authentication, user management, and exchange operations
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';

// Types
export interface User {
  id: number;
  email: string;
  name: string;
  role: 'admin' | 'trader' | 'trial';
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  last_login?: string;
  avatar_url?: string;
  bio?: string;
  timezone: string;
}

export interface UserStats {
  total_trades: number;
  successful_trades: number;
  total_pnl: string;
  win_rate: number;
  active_exchanges: number;
}

export interface UserProfile extends User {
  stats?: UserStats;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  name: string;
  password: string;
  confirm_password: string;
  role?: 'trial' | 'trader';
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export interface ExchangeCredentials {
  id: number;
  user_id: number;
  exchange_type: 'binance' | 'coinbase' | 'kraken' | 'bybit';
  exchange_name: string;
  is_testnet: boolean;
  is_sandbox: boolean;
  status: 'active' | 'inactive' | 'expired' | 'error';
  last_validated?: string;
  last_connected?: string;
  connection_count: number;
  environment_name: string;
  created_at: string;
  updated_at: string;
}

export interface CreateExchangeCredentials {
  exchange_type: 'binance' | 'coinbase' | 'kraken' | 'bybit';
  exchange_name: string;
  api_key: string;
  secret_key: string;
  is_testnet?: boolean;
  is_sandbox?: boolean;
  is_read_only?: boolean;
  notes?: string;
}

export interface ConnectionTestRequest {
  api_key: string;
  secret_key: string;
  is_testnet?: boolean;
}

export interface ConnectionTestResult {
  success: boolean;
  message: string;
  account_info?: Record<string, unknown>;
  permissions?: string[];
  error_code?: string;
}

export interface SupportedExchange {
  id: string;
  name: string;
  display_name: string;
  description: string;
  is_available: boolean;
  coming_soon: boolean;
  features: string[];
  testnet_available: boolean;
  sandbox_available: boolean;
}

import { appConfig } from '../config/appConfig';

class ApiClient {
  private client: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = appConfig.API_BASE_URL;
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getAccessToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            await this.refreshToken();
            const token = this.getAccessToken();
            if (token) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.client(originalRequest);
            }
          } catch (refreshError) {
            this.logout();
            window.location.href = '/login';
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // Token management
  private getAccessToken(): string | null {
    return localStorage.getItem('kamikaze_access_token');
  }

  private getRefreshToken(): string | null {
    return localStorage.getItem('kamikaze_refresh_token');
  }

  private setTokens(tokens: TokenResponse): void {
    localStorage.setItem('kamikaze_access_token', tokens.access_token);
    localStorage.setItem('kamikaze_refresh_token', tokens.refresh_token);
  }

  private clearTokens(): void {
    localStorage.removeItem('kamikaze_access_token');
    localStorage.removeItem('kamikaze_refresh_token');
  }

  // Authentication methods
  async login(credentials: LoginRequest): Promise<TokenResponse> {
    const response = await this.client.post('/api/v1/auth/signin', credentials);

    if (response.data.success) {
      const tokens = {
        access_token: response.data.access_token,
        refresh_token: response.data.refresh_token,
        token_type: response.data.token_type || 'bearer',
        expires_in: 1800 // 30 minutes
      };
      this.setTokens(tokens);
      return tokens;
    } else {
      throw new Error(response.data.message || 'Login failed');
    }
  }

  async register(userData: RegisterRequest): Promise<User> {
    const response = await this.client.post('/api/v1/auth/signup', {
      name: userData.name,
      email: userData.email,
      password: userData.password,
      username: userData.email.split('@')[0] // Generate username from email
    });

    if (response.data.success) {
      return response.data.user;
    } else {
      throw new Error(response.data.message || 'Registration failed');
    }
  }

  async refreshToken(): Promise<TokenResponse> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.client.post<TokenResponse>('/api/v1/auth/refresh', {
      refresh_token: refreshToken,
    });
    
    this.setTokens(response.data);
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.client.post('/api/v1/auth/logout');
    } catch (error) {
      // Ignore logout errors
    } finally {
      this.clearTokens();
    }
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.client.get('/api/v1/auth/me');

    if (response.data.success) {
      return response.data.user;
    } else {
      throw new Error(response.data.message || 'Failed to get user info');
    }
  }

  // Exchange methods
  async getSupportedExchanges(): Promise<SupportedExchange[]> {
    const response = await this.client.get<SupportedExchange[]>('/api/v1/exchanges/supported');
    return response.data;
  }

  async getMyExchanges(): Promise<ExchangeCredentials[]> {
    const response = await this.client.get<ExchangeCredentials[]>('/api/v1/exchanges/');
    return response.data;
  }

  async createExchangeCredentials(credentials: CreateExchangeCredentials): Promise<ExchangeCredentials> {
    const response = await this.client.post<ExchangeCredentials>('/api/v1/exchanges/', credentials);
    return response.data;
  }

  async getExchangeCredentials(credentialsId: number): Promise<ExchangeCredentials> {
    const response = await this.client.get<ExchangeCredentials>(`/api/v1/exchanges/${credentialsId}`);
    return response.data;
  }

  async updateExchangeCredentials(
    credentialsId: number, 
    updates: Partial<CreateExchangeCredentials>
  ): Promise<ExchangeCredentials> {
    const response = await this.client.put<ExchangeCredentials>(`/api/v1/exchanges/${credentialsId}`, updates);
    return response.data;
  }

  async deleteExchangeCredentials(credentialsId: number): Promise<void> {
    await this.client.delete(`/api/v1/exchanges/${credentialsId}`);
  }

  // Credentials methods
  async saveTestnetCredentials(credentials: { exchange: string; api_key: string; secret_key: string }): Promise<any> {
    const response = await this.client.post('/api/v1/credentials/testnet', credentials);
    return response.data;
  }

  async saveBinanceCredentials(credentials: { api_key: string; secret_key: string; is_mainnet: boolean }): Promise<any> {
    const response = await this.client.post('/api/v1/credentials/binance', credentials);
    return response.data;
  }

  async testConnection(connectionData: ConnectionTestRequest): Promise<ConnectionTestResult> {
    const response = await this.client.post<ConnectionTestResult>('/api/v1/credentials/test-connection', connectionData);
    return response.data;
  }

  async validateCredentials(credentialType: string, isMainnet: boolean = true): Promise<ConnectionTestResult> {
    const response = await this.client.post<ConnectionTestResult>(`/api/v1/credentials/validate/${credentialType}?is_mainnet=${isMainnet}`);
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    const response = await this.client.get('/health');
    return response.data;
  }

  // HTTP methods for direct API calls
  async get<T = any>(url: string, config?: any): Promise<AxiosResponse<T>> {
    return this.client.get<T>(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> {
    return this.client.post<T>(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> {
    return this.client.put<T>(url, data, config);
  }

  async delete<T = any>(url: string, config?: any): Promise<AxiosResponse<T>> {
    return this.client.delete<T>(url, config);
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }

  getAuthHeaders(): Record<string, string> {
    const token = this.getAccessToken();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }


}

// Create singleton instance
export const apiClient = new ApiClient();

// Export default
export default apiClient;
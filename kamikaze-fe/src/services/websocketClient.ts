/**
 * WebSocket Client for Real-Time Dashboard Updates
 * <PERSON><PERSON> authenticated WebSocket connections for portfolio data
 */

import { apiClient } from './api';
import { appConfig } from '../config/appConfig';

export interface WebSocketMessage {
  type: string;
  data?: any;
  user_id?: number;
  client_id?: string;
  timestamp?: number;
  error?: string;
  message?: string;
}

export interface PortfolioUpdateData {
  total_value_usd: number;
  total_value_btc: number;
  daily_pnl: number;
  daily_pnl_percent: number;
  asset_allocation: Array<{
    asset: string;
    balance: number;
    usd_value: number;
    btc_value: number;
    percentage: number;
  }>;
  btc_price_usd: number;
  timestamp: number;
}

export type WebSocketEventHandler = (message: WebSocketMessage) => void;

class WebSocketClient {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();
  private clientId: string;
  private baseUrl: string;

  constructor() {
    this.clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.baseUrl = appConfig.WS_URL;
  }

  /**
   * Connect to general WebSocket with authentication
   */
  async connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      console.log('🔌 [WebSocket] Already connecting or connected');
      return;
    }

    this.isConnecting = true;

    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No authentication token available');
      }

      const wsUrl = `${this.baseUrl}/ws/${this.clientId}?token=${encodeURIComponent(token)}`;
      console.log('🔌 [WebSocket] Connecting to general WebSocket...', wsUrl);

      this.ws = new WebSocket(wsUrl);
      this.setupWebSocketHandlers();

      // Wait for connection to be established
      await new Promise<void>((resolve, reject) => {
        if (!this.ws) {
          reject(new Error('WebSocket not initialized'));
          return;
        }

        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 10000);

        this.ws.onopen = () => {
          clearTimeout(timeout);
          console.log('✅ [WebSocket] General connection established');
          resolve();
        };

        this.ws.onerror = (error) => {
          clearTimeout(timeout);
          console.error('❌ [WebSocket] Connection error:', error);
          reject(error);
        };
      });
    } catch (error) {
      console.error('❌ [WebSocket] General connection failed:', error);
      this.isConnecting = false;
      throw error;
    }
  }

  /**
   * Connect to portfolio WebSocket with authentication
   */
  async connectPortfolio(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.isConnecting = true;

    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No authentication token available');
      }

      const wsUrl = `${this.baseUrl}/ws/portfolio/${this.clientId}?token=${encodeURIComponent(token)}`;
      console.log('🔌 [WebSocket] Connecting to portfolio updates...', wsUrl);

      this.ws = new WebSocket(wsUrl);
      this.setupWebSocketHandlers();
    } catch (error) {
      console.error('❌ [WebSocket] Portfolio connection failed:', error);
      this.isConnecting = false;
      throw error;
    }
  }

  /**
   * Connect to agent WebSocket for real-time trading events
   */
  async connectAgent(agentId: string): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      console.log('🔌 [WebSocket] Already connecting or connected');
      return;
    }

    this.isConnecting = true;

    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Use the general WebSocket endpoint for agent events
      const wsUrl = `${this.baseUrl}/ws/${this.clientId}?token=${encodeURIComponent(token)}`;
      console.log('🔌 [WebSocket] Connecting to agent events...', wsUrl);

      this.ws = new WebSocket(wsUrl);
      this.setupWebSocketHandlers();

      // Wait for connection to be established
      await new Promise<void>((resolve, reject) => {
        if (!this.ws) {
          reject(new Error('WebSocket not initialized'));
          return;
        }

        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 10000);

        this.ws.onopen = () => {
          clearTimeout(timeout);
          console.log('✅ [WebSocket] Agent connection established');
          resolve();
        };

        this.ws.onerror = (error) => {
          clearTimeout(timeout);
          console.error('❌ [WebSocket] Connection error:', error);
          reject(error);
        };
      });
    } catch (error) {
      console.error('❌ [WebSocket] Agent connection failed:', error);
      this.isConnecting = false;
      throw error;
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupWebSocketHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('✅ [WebSocket] Connection established');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.emit('connected', { type: 'connected' });
    };

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        console.log('📨 [WebSocket] Received message:', message.type);

        // Emit the specific message type
        this.emit(message.type, message);

        // Handle FluxTrader specific events
        if (message.type === 'cycle_analysis') {
          console.log('📊 [WebSocket] Cycle analysis received');
          this.emit('cycle_analysis', message);
        } else if (message.type === 'trade_execution') {
          console.log('💰 [WebSocket] Trade execution received');
          this.emit('trade_execution', message);
        } else if (message.type === 'agent_update') {
          console.log('🤖 [WebSocket] Agent update received');
          this.emit('agent_update', message);
        }

        // Handle Trading Bot specific events
        else if (message.type === 'bot_status') {
          console.log('🤖 [WebSocket] Bot status update received');
          this.emit('bot_status', message);
        } else if (message.type === 'signal') {
          console.log('📈 [WebSocket] Trading signal received');
          this.emit('signal', message);
        } else if (message.type === 'trade') {
          console.log('💱 [WebSocket] Trade update received');
          this.emit('trade', message);
        } else if (message.type === 'performance') {
          console.log('📊 [WebSocket] Performance update received');
          this.emit('performance', message);
        }
      } catch (error) {
        console.error('💥 [WebSocket] Failed to parse message:', error);
      }
    };

    this.ws.onclose = (event) => {
      console.log('🔌 [WebSocket] Connection closed:', event.code, event.reason);
      this.isConnecting = false;
      this.ws = null;
      this.emit('disconnected', { type: 'disconnected' });

      // Attempt to reconnect if not a normal closure
      if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('💥 [WebSocket] Connection error:', error);
      this.isConnecting = false;
      this.emit('error', { type: 'error', error: 'WebSocket connection error' });
    };
  }

  /**
   * Disconnect from WebSocket
   */
  disconnect(): void {
    if (this.ws) {
      console.log('🔌 [WebSocket] Disconnecting...');
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    this.reconnectAttempts = this.maxReconnectAttempts; // Prevent reconnection
  }

  /**
   * Subscribe to portfolio updates
   */
  subscribeToPortfolio(): void {
    this.sendMessage({
      type: 'subscribe_portfolio'
    });
  }

  /**
   * Request current portfolio data
   */
  requestPortfolioData(): void {
    this.sendMessage({
      type: 'get_portfolio'
    });
  }

  /**
   * Send a message through WebSocket
   */
  sendMessage(message: any): void {
    console.log(`🔌 [WebSocket] Attempting to send message:`, message);
    console.log(`🔌 [WebSocket] WebSocket state:`, {
      ws: !!this.ws,
      readyState: this.ws?.readyState,
      expectedState: WebSocket.OPEN,
      url: this.ws?.url
    });

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log(`🔌 [WebSocket] Sending message to backend:`, JSON.stringify(message));
      this.ws.send(JSON.stringify(message));
      console.log(`🔌 [WebSocket] Message sent successfully!`);
    } else {
      console.error('❌ [WebSocket] Cannot send message - not connected', {
        ws: !!this.ws,
        readyState: this.ws?.readyState,
        expectedState: WebSocket.OPEN
      });
    }
  }

  /**
   * Add event listener
   */
  on(event: string, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  /**
   * Remove event listener
   */
  off(event: string, handler: WebSocketEventHandler): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to all listeners
   */
  private emit(event: string, data: WebSocketMessage): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`💥 [WebSocket] Error in event handler for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`🔄 [WebSocket] Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (this.reconnectAttempts <= this.maxReconnectAttempts) {
        console.log(`🔄 [WebSocket] Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
        this.connectPortfolio().catch(error => {
          console.error('💥 [WebSocket] Reconnection failed:', error);
        });
      }
    }, delay);
  }

  /**
   * Get connection status
   */
  get isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Get connection state
   */
  get connectionState(): string {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }

  /**
   * Subscribe to agent updates
   */
  async subscribeToAgent(agentId: string): Promise<void> {
    console.log(`🔌 [WebSocket] Subscribing to agent ${agentId}`);

    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error(`🔌 [WebSocket] Cannot subscribe - WebSocket not connected`);
      throw new Error('WebSocket not connected');
    }

    const subscribeMessage = {
      type: 'subscribe',
      agent_id: agentId
    };

    console.log(`🔌 [WebSocket] Sending subscription message:`, subscribeMessage);
    this.sendMessage(subscribeMessage);
    console.log(`🔌 [WebSocket] Subscription message sent for agent ${agentId}`);
  }

  /**
   * Unsubscribe from agent updates
   */
  async unsubscribeFromAgent(agentId: string): Promise<void> {
    console.log(`🔌 [WebSocket] Unsubscribing from agent ${agentId}`);
    this.sendMessage({
      type: 'unsubscribe',
      agent_id: agentId
    });
  }

  /**
   * Add message handler (compatibility method)
   */
  addMessageHandler(handler: WebSocketEventHandler): void {
    this.on('trading_event', handler);
    this.on('agent_event', handler);
    this.on('portfolio_update', handler);
    this.on('cycle_analysis', handler);
    this.on('trade_execution', handler);
    this.on('agent_update', handler);
  }

  /**
   * Remove message handler (compatibility method)
   */
  removeMessageHandler(handler: WebSocketEventHandler): void {
    this.off('trading_event', handler);
    this.off('agent_event', handler);
    this.off('portfolio_update', handler);
  }

  /**
   * Subscribe to trading bot updates
   */
  async subscribeToBot(botId: string): Promise<void> {
    console.log(`🤖 [WebSocket] Subscribing to bot ${botId}`);

    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error(`🤖 [WebSocket] Cannot subscribe - WebSocket not connected`);
      throw new Error('WebSocket not connected');
    }

    const subscribeMessage = {
      type: 'subscribe',
      channel: `bot_${botId}`,
      data: { bot_id: botId }
    };

    console.log(`🤖 [WebSocket] Sending bot subscription message:`, subscribeMessage);
    this.sendMessage(subscribeMessage);
    console.log(`🤖 [WebSocket] Subscription message sent for bot ${botId}`);
  }

  /**
   * Unsubscribe from trading bot updates
   */
  async unsubscribeFromBot(botId: string): Promise<void> {
    console.log(`🤖 [WebSocket] Unsubscribing from bot ${botId}`);

    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn(`🤖 [WebSocket] Cannot unsubscribe - WebSocket not connected`);
      return;
    }

    const unsubscribeMessage = {
      type: 'unsubscribe',
      channel: `bot_${botId}`,
      data: { bot_id: botId }
    };

    this.sendMessage(unsubscribeMessage);
  }

  /**
   * Add trading bot message handler
   */
  onMessage(handler: (message: any) => void): void {
    this.on('bot_status', handler);
    this.on('signal', handler);
    this.on('trade', handler);
    this.on('performance', handler);
  }

  /**
   * Send message (alias for compatibility)
   */
  async send(message: any): Promise<void> {
    this.sendMessage(message);
  }
}

// Export singleton instance
export const websocketClient = new WebSocketClient();
/**
 * Trading Bot API Service
 * Professional-grade trading bot API integration
 */

import { apiClient } from './api';
import {
  TradingBot,
  TradingBotConfig,
  CreateBotRequest,
  CreateBotResponse,
  BotControlRequest,
  BotControlResponse,
  BotListResponse,
  BotMetricsResponse,
  TradingBotMetrics,
  StrategyConfig,
  StrategyType,
  RiskLevel,
  TimeFrame,
  PositionSizingMethod,
  SignalType
} from '@/types/tradingBot';

export class TradingBotApiService {
  private baseUrl = '/api/v1/technical-indicators';

  /**
   * Create a new trading bot
   */
  async createBot(config: TradingBotConfig): Promise<CreateBotResponse> {
    try {
      const response = await apiClient.post<CreateBotResponse>(`${this.baseUrl}/bots`, {
        config
      });
      return response.data;
    } catch (error: any) {
      console.error('Failed to create trading bot:', error);
      throw new Error(error.response?.data?.message || 'Failed to create trading bot');
    }
  }

  /**
   * Get all trading bots for the current user
   */
  async getBots(): Promise<BotListResponse> {
    try {
      const response = await apiClient.get<BotListResponse>(`${this.baseUrl}/bots`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to fetch trading bots:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch trading bots');
    }
  }

  /**
   * Get a specific trading bot by ID
   */
  async getBot(botId: string): Promise<TradingBot> {
    try {
      const response = await apiClient.get<{ success: boolean; bot: TradingBot }>(`${this.baseUrl}/bots/${botId}`);
      if (!response.data.success) {
        throw new Error('Failed to fetch bot');
      }
      return response.data.bot;
    } catch (error: any) {
      console.error(`Failed to fetch bot ${botId}:`, error);
      throw new Error(error.response?.data?.message || 'Failed to fetch bot');
    }
  }

  /**
   * Update trading bot configuration
   */
  async updateBot(botId: string, config: Partial<TradingBotConfig>): Promise<TradingBot> {
    try {
      const response = await apiClient.put<{ success: boolean; bot: TradingBot }>(`${this.baseUrl}/bots/${botId}`, {
        config
      });
      if (!response.data.success) {
        throw new Error('Failed to update bot');
      }
      return response.data.bot;
    } catch (error: any) {
      console.error(`Failed to update bot ${botId}:`, error);
      throw new Error(error.response?.data?.message || 'Failed to update bot');
    }
  }

  /**
   * Delete a trading bot
   */
  async deleteBot(botId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/bots/${botId}`);
    } catch (error: any) {
      console.error(`Failed to delete bot ${botId}:`, error);
      throw new Error(error.response?.data?.message || 'Failed to delete bot');
    }
  }

  /**
   * Start a trading bot
   */
  async startBot(botId: string): Promise<BotControlResponse> {
    try {
      const response = await apiClient.post<BotControlResponse>(`${this.baseUrl}/bots/${botId}/start`);
      return response.data;
    } catch (error: any) {
      console.error(`Failed to start bot ${botId}:`, error);
      throw new Error(error.response?.data?.message || 'Failed to start bot');
    }
  }

  /**
   * Stop a trading bot
   */
  async stopBot(botId: string): Promise<BotControlResponse> {
    try {
      const response = await apiClient.post<BotControlResponse>(`${this.baseUrl}/bots/${botId}/stop`);
      return response.data;
    } catch (error: any) {
      console.error(`Failed to stop bot ${botId}:`, error);
      throw new Error(error.response?.data?.message || 'Failed to stop bot');
    }
  }

  /**
   * Pause a trading bot
   */
  async pauseBot(botId: string): Promise<BotControlResponse> {
    try {
      const response = await apiClient.post<BotControlResponse>(`${this.baseUrl}/bots/${botId}/pause`);
      return response.data;
    } catch (error: any) {
      console.error(`Failed to pause bot ${botId}:`, error);
      throw new Error(error.response?.data?.message || 'Failed to pause bot');
    }
  }

  /**
   * Resume a trading bot
   */
  async resumeBot(botId: string): Promise<BotControlResponse> {
    try {
      const response = await apiClient.post<BotControlResponse>(`${this.baseUrl}/bots/${botId}/resume`);
      return response.data;
    } catch (error: any) {
      console.error(`Failed to resume bot ${botId}:`, error);
      throw new Error(error.response?.data?.message || 'Failed to resume bot');
    }
  }

  /**
   * Get bot performance metrics
   */
  async getBotMetrics(botId: string): Promise<BotMetricsResponse> {
    try {
      const response = await apiClient.get<BotMetricsResponse>(`${this.baseUrl}/bots/${botId}/metrics`);
      return response.data;
    } catch (error: any) {
      console.error(`Failed to fetch bot metrics ${botId}:`, error);
      throw new Error(error.response?.data?.message || 'Failed to fetch bot metrics');
    }
  }

  /**
   * Get available strategy templates
   */
  async getStrategyTemplates(): Promise<StrategyConfig[]> {
    try {
      const response = await apiClient.get<{ success: boolean; strategies: StrategyConfig[] }>(`${this.baseUrl}/strategies/templates`);
      if (!response.data.success) {
        throw new Error('Failed to fetch strategy templates');
      }
      return response.data.strategies;
    } catch (error: any) {
      console.error('Failed to fetch strategy templates:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch strategy templates');
    }
  }

  /**
   * Create a custom strategy
   */
  async createStrategy(strategy: StrategyConfig): Promise<StrategyConfig> {
    try {
      const response = await apiClient.post<{ success: boolean; strategy: StrategyConfig }>(`${this.baseUrl}/strategies`, strategy);
      if (!response.data.success) {
        throw new Error('Failed to create strategy');
      }
      return response.data.strategy;
    } catch (error: any) {
      console.error('Failed to create strategy:', error);
      throw new Error(error.response?.data?.message || 'Failed to create strategy');
    }
  }

  /**
   * Validate strategy configuration
   */
  async validateStrategy(strategy: StrategyConfig): Promise<{ valid: boolean; errors: string[] }> {
    try {
      const response = await apiClient.post<{ success: boolean; valid: boolean; errors: string[] }>(`${this.baseUrl}/strategies/validate`, strategy);
      return {
        valid: response.data.valid,
        errors: response.data.errors || []
      };
    } catch (error: any) {
      console.error('Failed to validate strategy:', error);
      throw new Error(error.response?.data?.message || 'Failed to validate strategy');
    }
  }

  /**
   * Backtest a strategy
   */
  async backtestStrategy(strategy: StrategyConfig, config: {
    symbols: string[];
    start_date: string;
    end_date: string;
    initial_capital: number;
    timeframe: TimeFrame;
  }): Promise<any> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/strategies/backtest`, {
        strategy,
        ...config
      });
      return response.data;
    } catch (error: any) {
      console.error('Failed to backtest strategy:', error);
      throw new Error(error.response?.data?.message || 'Failed to backtest strategy');
    }
  }
}

// Create singleton instance
export const tradingBotApi = new TradingBotApiService();

// Strategy Templates for quick bot creation
export const STRATEGY_TEMPLATES = {
  MEAN_REVERSION: {
    name: 'Mean Reversion Pro',
    description: 'Professional mean reversion strategy using Bollinger Bands, RSI, and volume confirmation',
    strategy_type: StrategyType.MEAN_REVERSION,
    risk_level: RiskLevel.MEDIUM,
    timeframes: [TimeFrame.M15, TimeFrame.H1],
    indicators: [
      { name: 'BBANDS', parameters: { period: 20, std: 2 }, weight: 0.3, enabled: true },
      { name: 'RSI', parameters: { period: 14 }, weight: 0.4, enabled: true },
      { name: 'STOCHRSI', parameters: { period: 14, k: 3, d: 3 }, weight: 0.3, enabled: true }
    ],
    rules: [{
      name: 'Mean Reversion Entry',
      entry_conditions: {
        conditions: [
          { indicator: 'BBANDS_LOWER', operator: 'LT', value: 'close' },
          { indicator: 'RSI', operator: 'LT', value: 30 },
          { indicator: 'STOCHRSI_K', operator: 'LT', value: 20 }
        ],
        operator: 'AND'
      },
      signal_type: SignalType.BUY,
      min_confidence: 0.7,
      stop_loss_pct: 1.5,
      take_profit_pct: 2.5
    }],
    parameters: {
      max_holding_period: 24,
      volume_confirmation: true,
      trend_filter: false
    }
  },

  MOMENTUM: {
    name: 'Momentum Breakout',
    description: 'Aggressive momentum strategy with EMA crossover, MACD, and ADX confirmation',
    strategy_type: StrategyType.MOMENTUM,
    risk_level: RiskLevel.HIGH,
    timeframes: [TimeFrame.M15, TimeFrame.H1, TimeFrame.H4],
    indicators: [
      { name: 'EMA', parameters: { period: 12 }, weight: 0.25, enabled: true },
      { name: 'EMA', parameters: { period: 26 }, weight: 0.25, enabled: true },
      { name: 'MACD', parameters: { fast: 12, slow: 26, signal: 9 }, weight: 0.3, enabled: true },
      { name: 'ADX', parameters: { period: 14 }, weight: 0.2, enabled: true }
    ],
    rules: [{
      name: 'Momentum Entry',
      entry_conditions: {
        conditions: [
          { indicator: 'EMA_12', operator: 'CROSS_ABOVE', value: 'EMA_26' },
          { indicator: 'MACD', operator: 'CROSS_ABOVE', value: 'MACD_SIGNAL' },
          { indicator: 'ADX', operator: 'GT', value: 25 }
        ],
        operator: 'AND'
      },
      signal_type: SignalType.BUY,
      min_confidence: 0.75,
      stop_loss_pct: 3.0,
      take_profit_pct: 8.0
    }],
    parameters: {
      max_holding_period: 48,
      trailing_stop: true,
      trend_confirmation: true
    }
  },

  MULTI_TIMEFRAME: {
    name: 'Multi-Timeframe Analysis',
    description: 'Professional multi-timeframe strategy with Ichimoku, EMA, and RSI convergence',
    strategy_type: StrategyType.MULTI_TIMEFRAME,
    risk_level: RiskLevel.MEDIUM,
    timeframes: [TimeFrame.M15, TimeFrame.H1, TimeFrame.H4],
    indicators: [
      { name: 'ICHIMOKU', parameters: { tenkan: 9, kijun: 26, senkou: 52 }, weight: 0.4, enabled: true },
      { name: 'EMA', parameters: { period: 20 }, weight: 0.2, enabled: true },
      { name: 'EMA', parameters: { period: 50 }, weight: 0.2, enabled: true },
      { name: 'RSI', parameters: { period: 14 }, weight: 0.2, enabled: true }
    ],
    rules: [{
      name: 'Multi-Timeframe Entry',
      entry_conditions: {
        conditions: [
          { indicator: 'ICHIMOKU_ABOVE_CLOUD', operator: 'EQ', value: true },
          { indicator: 'EMA_20', operator: 'GT', value: 'EMA_50' },
          { indicator: 'RSI', operator: 'GT', value: 30 },
          { indicator: 'RSI', operator: 'LT', value: 70 }
        ],
        operator: 'AND'
      },
      signal_type: SignalType.BUY,
      min_confidence: 0.8,
      stop_loss_pct: 2.0,
      take_profit_pct: 6.0
    }],
    parameters: {
      max_holding_period: 72,
      multi_timeframe_confirmation: true,
      consensus_threshold: 0.7
    }
  }
};
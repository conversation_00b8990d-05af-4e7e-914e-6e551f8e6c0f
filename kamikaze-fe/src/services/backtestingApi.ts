/**
 * Backtesting API Service
 * Handles communication with the backend backtesting system
 */

import { apiClient } from './apiClient';
import { BacktestingConfig, BacktestingResults, BacktestingStrategy } from '@/types/backtesting';

export interface BacktestingConfigRequest {
  strategy: {
    id: string;
    name: string;
    description?: string;
    category: string;
    parameters: Record<string, any>;
  };
  trading_pairs: string[];
  timeframe: string;
  start_date: string;
  end_date: string;
  initial_capital: number;
  commission: number;
  slippage: number;
  risk_management?: {
    stop_loss?: number;
    take_profit?: number;
    position_sizing: string;
    max_position_size: number;
    max_drawdown: number;
    max_portfolio_risk?: number;
    enable_dynamic_sizing?: boolean;
  };
  benchmark?: string;
  name?: string;
}

export interface BacktestingResponse {
  success: boolean;
  message: string;
  backtest_id?: string;
  status?: string;
}

export interface BacktestingProgressResponse {
  backtest_id: string;
  status: string;
  progress: number;
  error_message?: string;
  estimated_completion?: string;
}

export interface BacktestingResultsResponse {
  id: string;
  config: any;
  start_time: string;
  end_time?: string;
  status: string;
  progress: number;
  trades: Array<{
    id: string;
    symbol: string;
    side: string;
    entry_time: string;
    exit_time?: string;
    entry_price: number;
    exit_price?: number;
    quantity: number;
    pnl: number;
    pnl_percent: number;
    duration_minutes?: number;
    entry_reason?: string;
    exit_reason?: string;
  }>;
  metrics: {
    total_return: number;
    total_return_percent: number;
    annualized_return: number;
    sharpe_ratio: number;
    sortino_ratio: number;
    max_drawdown: number;
    max_drawdown_percent: number;
    win_rate: number;
    profit_factor: number;
    total_trades: number;
    winning_trades: number;
    losing_trades: number;
    average_win: number;
    average_loss: number;
    largest_win: number;
    largest_loss: number;
    average_trade_duration: number;
    volatility: number;
    calmar_ratio: number;
    recovery_factor: number;
    payoff_ratio: number;
    expected_value: number;
  };
  equity: Array<{
    timestamp: string;
    value: number;
    drawdown: number;
    drawdown_percent: number;
  }>;
  monthly_returns: Array<{
    month: string;
    return: number;
  }>;
  benchmark_comparison?: {
    benchmark: string;
    benchmark_return: number;
    alpha: number;
    beta: number;
    correlation: number;
  };
}

export interface BacktestingHistoryResponse {
  backtests: Array<{
    id: string;
    name: string;
    strategy_name: string;
    trading_pairs: string[];
    timeframe: string;
    start_date: string;
    end_date: string;
    initial_capital: number;
    status: string;
    progress: number;
    created_at: string;
    completed_at?: string;
    total_return?: number;
    total_return_percent?: number;
    sharpe_ratio?: number;
    max_drawdown_percent?: number;
    total_trades?: number;
  }>;
  total: number;
  limit: number;
  offset: number;
}

export interface AvailableStrategy {
  id: string;
  name: string;
  description: string;
  category: string;
  parameters: Record<string, {
    type: string;
    default: any;
    min?: number;
    max?: number;
    options?: string[];
    description?: string;
  }>;
}

export interface StrategiesResponse {
  success: boolean;
  strategies: AvailableStrategy[];
}

class BacktestingApiService {
  private baseUrl = '/api/v1/backtesting';

  /**
   * Start a new backtest
   */
  async startBacktest(config: BacktestingConfigRequest): Promise<BacktestingResponse> {
    try {
      const response = await apiClient.post<BacktestingResponse>(`${this.baseUrl}/start`, config);
      return response.data;
    } catch (error: any) {
      console.error('Failed to start backtest:', error);
      throw new Error(error.response?.data?.detail || 'Failed to start backtest');
    }
  }

  /**
   * Get backtest status and progress
   */
  async getBacktestStatus(backtestId: string): Promise<BacktestingProgressResponse> {
    try {
      const response = await apiClient.get<BacktestingProgressResponse>(`${this.baseUrl}/status/${backtestId}`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get backtest status:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get backtest status');
    }
  }

  /**
   * Get backtest results
   */
  async getBacktestResults(backtestId: string): Promise<BacktestingResultsResponse> {
    try {
      const response = await apiClient.get<BacktestingResultsResponse>(`${this.baseUrl}/results/${backtestId}`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get backtest results:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get backtest results');
    }
  }

  /**
   * Get backtest history
   */
  async getBacktestHistory(limit = 20, offset = 0): Promise<BacktestingHistoryResponse> {
    try {
      const response = await apiClient.get<BacktestingHistoryResponse>(
        `${this.baseUrl}/history?limit=${limit}&offset=${offset}`
      );
      return response.data;
    } catch (error: any) {
      console.error('Failed to get backtest history:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get backtest history');
    }
  }

  /**
   * Delete a backtest
   */
  async deleteBacktest(backtestId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.delete(`${this.baseUrl}/${backtestId}`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to delete backtest:', error);
      throw new Error(error.response?.data?.detail || 'Failed to delete backtest');
    }
  }

  /**
   * Get available trading strategies
   */
  async getAvailableStrategies(): Promise<AvailableStrategy[]> {
    try {
      const response = await apiClient.get<StrategiesResponse>(`${this.baseUrl}/strategies`);
      return response.data.strategies;
    } catch (error: any) {
      console.error('Failed to get available strategies:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get available strategies');
    }
  }

  /**
   * Connect to WebSocket for real-time progress updates
   */
  connectToProgressUpdates(backtestId: string, onUpdate: (data: any) => void): WebSocket | null {
    try {
      const wsUrl = `ws://localhost:8000/api/v1/backtesting/ws/${backtestId}`;
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('Connected to backtest progress WebSocket');
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          onUpdate(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

      ws.onclose = () => {
        console.log('WebSocket connection closed');
      };

      return ws;
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
      return null;
    }
  }

  /**
   * Convert frontend config to backend format
   */
  convertConfigToBackendFormat(config: BacktestingConfig): BacktestingConfigRequest {
    return {
      strategy: {
        id: config.strategy.id,
        name: config.strategy.name,
        description: config.strategy.description,
        category: config.strategy.category,
        parameters: config.strategy.parameters
      },
      trading_pairs: config.tradingPairs,
      timeframe: config.timeframe,
      start_date: config.startDate.toISOString(),
      end_date: config.endDate.toISOString(),
      initial_capital: config.initialCapital,
      commission: config.commission,
      slippage: config.slippage,
      risk_management: {
        stop_loss: config.riskManagement?.stopLoss,
        take_profit: config.riskManagement?.takeProfit,
        position_sizing: config.riskManagement?.positionSizing || 'percentage',
        max_position_size: config.riskManagement?.maxPositionSize || 10,
        max_drawdown: config.riskManagement?.maxDrawdown || 20,
        max_portfolio_risk: config.riskManagement?.maxDrawdown,
        enable_dynamic_sizing: true
      },
      benchmark: config.benchmark
    };
  }

  /**
   * Convert backend results to frontend format
   */
  convertResultsToFrontendFormat(backendResults: BacktestingResultsResponse): BacktestingResults {
    return {
      id: backendResults.id,
      config: backendResults.config as BacktestingConfig, // Type assertion for now
      startTime: new Date(backendResults.start_time),
      endTime: backendResults.end_time ? new Date(backendResults.end_time) : new Date(),
      status: backendResults.status as 'running' | 'completed' | 'failed' | 'cancelled',
      progress: backendResults.progress,
      trades: backendResults.trades.map(trade => ({
        id: trade.id,
        symbol: trade.symbol,
        side: trade.side as 'BUY' | 'SELL',
        entryTime: new Date(trade.entry_time),
        exitTime: trade.exit_time ? new Date(trade.exit_time) : undefined,
        entryPrice: trade.entry_price,
        exitPrice: trade.exit_price,
        quantity: trade.quantity,
        pnl: trade.pnl,
        pnlPercent: trade.pnl_percent,
        durationMinutes: trade.duration_minutes,
        entryReason: trade.entry_reason,
        exitReason: trade.exit_reason
      })),
      metrics: {
        totalReturn: backendResults.metrics.total_return,
        totalReturnPercent: backendResults.metrics.total_return_percent,
        annualizedReturn: backendResults.metrics.annualized_return,
        sharpeRatio: backendResults.metrics.sharpe_ratio,
        sortinoRatio: backendResults.metrics.sortino_ratio,
        maxDrawdown: backendResults.metrics.max_drawdown,
        maxDrawdownPercent: backendResults.metrics.max_drawdown_percent,
        winRate: backendResults.metrics.win_rate,
        profitFactor: backendResults.metrics.profit_factor,
        totalTrades: backendResults.metrics.total_trades,
        winningTrades: backendResults.metrics.winning_trades,
        losingTrades: backendResults.metrics.losing_trades,
        averageWin: backendResults.metrics.average_win,
        averageLoss: backendResults.metrics.average_loss,
        largestWin: backendResults.metrics.largest_win,
        largestLoss: backendResults.metrics.largest_loss,
        averageTradeDuration: backendResults.metrics.average_trade_duration,
        volatility: backendResults.metrics.volatility,
        calmarRatio: backendResults.metrics.calmar_ratio,
        recoveryFactor: backendResults.metrics.recovery_factor,
        payoffRatio: backendResults.metrics.payoff_ratio,
        expectedValue: backendResults.metrics.expected_value
      },
      equity: backendResults.equity.map(point => ({
        timestamp: new Date(point.timestamp),
        value: point.value,
        drawdown: point.drawdown
      })),
      monthlyReturns: backendResults.monthly_returns.map(month => ({
        month: month.month,
        return: month.return
      })),
      benchmarkComparison: backendResults.benchmark_comparison ? {
        benchmark: backendResults.benchmark_comparison.benchmark,
        benchmarkReturn: backendResults.benchmark_comparison.benchmark_return,
        alpha: backendResults.benchmark_comparison.alpha,
        beta: backendResults.benchmark_comparison.beta,
        correlation: backendResults.benchmark_comparison.correlation
      } : undefined
    };
  }
}

export const backtestingApi = new BacktestingApiService();

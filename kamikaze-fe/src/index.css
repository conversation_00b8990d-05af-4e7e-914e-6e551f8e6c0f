@tailwind base;
@tailwind components;
@tailwind utilities;

/* Premium AI Trading Dashboard Design System
Sophisticated glassmorphism with eye-friendly color palette
Enhanced for premium, expensive appearance
*/

@layer base {
  :root {
    /* Light Theme Variables */
    --background: 0 0% 100%;           /* Pure white background */
    --foreground: 222 84% 4.9%;        /* Very dark text */

    /* Glassmorphism Card System - Light Theme */
    --card: 0 0% 100%;                 /* White cards */
    --card-foreground: 222 84% 4.9%;   /* Dark text on cards */
    --card-glass: 0 0% 100% / 0.8;     /* Semi-transparent white background */
    --card-border: 214 32% 91% / 0.8;  /* Light gray glass border */

    --popover: 0 0% 100%;              /* White popover */
    --popover-foreground: 222 84% 4.9%;

    /* Premium Color Palette - Light Theme */
    --primary: 217 91% 60%;            /* #3b82f6 - vibrant blue */
    --primary-foreground: 210 40% 98%;
    --primary-muted: 217 91% 60% / 0.1; /* Subtle primary overlay */

    --secondary: 210 40% 98%;          /* Light secondary */
    --secondary-foreground: 222 84% 4.9%; /* Dark text on light secondary */

    --muted: 210 40% 98%;              /* Light muted */
    --muted-foreground: 215 16% 46.9%; /* Medium gray text */

    /* Premium Accent Colors */
    --accent-teal: 178 60% 48%;        /* #14b8a6 - warm teal */
    --accent-purple: 262 83% 58%;      /* #7c3aed - rich purple */
    --accent-orange: 24 95% 53%;       /* #ea580c - warm orange */
    --accent-foreground: 210 40% 98%;

    /* Additional accent colors for sidebar */
    --accent: 210 40% 98%;             /* Light accent */
    --accent-foreground: 222 84% 4.9%;

    /* Trading Status Colors */
    --success: 160 84% 39%;            /* #059669 - green */
    --success-foreground: 210 40% 98%;
    --success-muted: 160 84% 39% / 0.1;

    --warning: 32 95% 44%;             /* #d97706 - amber */
    --warning-foreground: 210 40% 98%;
    --warning-muted: 32 95% 44% / 0.1;

    --destructive: 0 84% 60%;          /* #dc2626 - red */
    --destructive-foreground: 210 40% 98%;
    --destructive-muted: 0 84% 60% / 0.1;

    /* Interface Elements - Light Theme */
    --border: 214 32% 91%;             /* Light gray borders */
    --input: 214 32% 91%;              /* Light input backgrounds */
    --ring: 217 91% 60%;               /* Focus ring color */

    --radius: 0.875rem;                /* Slightly larger radius for premium feel */

    /* Enhanced Chart Colors */
    --chart-1: 217 91% 60%;            /* Primary blue */
    --chart-2: 178 60% 48%;            /* Teal */
    --chart-3: 262 83% 58%;            /* Purple */
    --chart-4: 160 84% 39%;            /* Green */
    --chart-5: 24 95% 53%;             /* Orange */
    --chart-6: 340 82% 52%;            /* Pink */

    /* Premium Gradients - Light Theme */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(217 91% 70%) 50%, hsl(217 91% 55%) 100%);
    --gradient-glass: linear-gradient(135deg, hsl(0 0% 100% / 0.9) 0%, hsl(0 0% 100% / 0.85) 50%, hsl(0 0% 100% / 0.9) 100%);
    --gradient-glass-border: linear-gradient(135deg, hsl(214 32% 91% / 0.5) 0%, hsl(214 32% 91% / 0.3) 50%, hsl(214 32% 91% / 0.5) 100%);
    --gradient-success: linear-gradient(135deg, hsl(160 84% 39%) 0%, hsl(160 84% 49%) 50%, hsl(160 84% 35%) 100%);
    --gradient-warning: linear-gradient(135deg, hsl(32 95% 44%) 0%, hsl(32 95% 54%) 50%, hsl(32 95% 40%) 100%);
    --gradient-destructive: linear-gradient(135deg, hsl(0 84% 60%) 0%, hsl(0 84% 70%) 50%, hsl(0 84% 55%) 100%);
    --gradient-button: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(217 91% 65%) 50%, hsl(217 91% 55%) 100%);
    --gradient-button-hover: linear-gradient(135deg, hsl(217 91% 65%) 0%, hsl(217 91% 75%) 50%, hsl(217 91% 60%) 100%);
    --gradient-shine: linear-gradient(135deg, hsl(217 91% 60% / 0.1) 0%, transparent 50%, hsl(217 91% 60% / 0.05) 100%);

    /* Glossy Rich Yellow Gradients */
    --gradient-golden: linear-gradient(135deg, hsl(45 100% 55%) 0%, hsl(43 100% 65%) 25%, hsl(41 100% 70%) 50%, hsl(43 100% 60%) 75%, hsl(45 100% 50%) 100%);
    --gradient-golden-hover: linear-gradient(135deg, hsl(45 100% 60%) 0%, hsl(43 100% 70%) 25%, hsl(41 100% 75%) 50%, hsl(43 100% 65%) 75%, hsl(45 100% 55%) 100%);
    --gradient-golden-shine: linear-gradient(135deg, hsl(45 100% 85% / 0.3) 0%, transparent 50%, hsl(45 100% 90% / 0.2) 100%);

    /* Premium Shadows & Effects - Light Theme */
    --shadow-glass: 0 8px 32px hsl(222 84% 4.9% / 0.1), 0 4px 16px hsl(222 84% 4.9% / 0.05), inset 0 1px 0 hsl(0 0% 100% / 0.15);
    --shadow-elevated: 0 20px 64px hsl(222 84% 4.9% / 0.15), 0 12px 32px hsl(222 84% 4.9% / 0.1);
    --shadow-glow-primary: 0 0 32px hsl(217 91% 60% / 0.2), 0 0 64px hsl(217 91% 60% / 0.05);
    --shadow-glow-success: 0 0 24px hsl(160 84% 39% / 0.2), 0 0 48px hsl(160 84% 39% / 0.05);
    --shadow-glow-warning: 0 0 24px hsl(32 95% 44% / 0.2), 0 0 48px hsl(32 95% 44% / 0.05);
    --shadow-button: 0 8px 24px hsl(217 91% 60% / 0.2), 0 4px 12px hsl(217 91% 60% / 0.1);
    --shadow-button-hover: 0 12px 32px hsl(217 91% 60% / 0.25), 0 6px 16px hsl(217 91% 60% / 0.15);
    --shadow-inner: inset 0 2px 8px hsl(222 84% 4.9% / 0.1);

    /* Golden Button Shadows */
    --shadow-golden: 0 8px 24px hsl(45 100% 50% / 0.4), 0 4px 12px hsl(45 100% 50% / 0.3), inset 0 1px 0 hsl(45 100% 80% / 0.3);
    --shadow-golden-hover: 0 12px 32px hsl(45 100% 50% / 0.5), 0 6px 16px hsl(45 100% 50% / 0.4), inset 0 1px 0 hsl(45 100% 85% / 0.4);

    /* Backdrop Blur Effects */
    --blur-glass: blur(16px);
    --blur-heavy: blur(24px);

    /* Premium Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Premium Sidebar Colors - Light Theme */
    --sidebar-background: 0 0% 100%;       /* White background */
    --sidebar-foreground: 222 84% 4.9%;    /* Dark text */
    --sidebar-primary: 217 91% 60%;        /* Primary blue for active states */
    --sidebar-primary-foreground: 210 40% 98%; /* White text on primary */
    --sidebar-accent: 210 40% 98%;         /* Light accent */
    --sidebar-accent-foreground: 222 84% 4.9%; /* Dark text */
    --sidebar-border: 214 32% 91% / 0.5;   /* Light border */
    --sidebar-ring: 217 91% 60%;           /* Focus ring */
  }

  .dark {
    /* Premium Dark Theme - Enhanced brightness for better readability */
    --background: 0 0% 6%;             /* Slightly brighter dark background for better contrast */
    --foreground: 210 40% 98%;         /* #f8fafc - high-contrast white */

    /* Glassmorphism Card System - Enhanced brightness for better readability */
    --card: 220 13% 28%;               /* #424b5a - even brighter warm slate for cards */
    --card-foreground: 210 40% 98%;    /* #f8fafc - crisp white text */
    --card-glass: 220 13% 28% / 0.9;   /* Brighter semi-transparent card background */
    --card-border: 220 13% 40% / 0.5;  /* More visible glass border */

    --popover: 220 13% 22%;            /* Slightly darker than cards for layering */
    --popover-foreground: 210 40% 98%;

    /* Premium Color Palette */
    --primary: 217 91% 60%;            /* #3b82f6 - vibrant blue */
    --primary-foreground: 210 40% 98%;
    --primary-muted: 217 91% 60% / 0.1; /* Subtle primary overlay */

    --secondary: 215 28% 22%;          /* #2a3441 - brighter warm slate */
    --secondary-foreground: 210 20% 88%; /* #d1d9e1 - brighter soft gray text */

    --muted: 215 28% 18%;              /* #1a2332 - brighter muted */
    --muted-foreground: 215 16% 70%;   /* #7a8a9a - brighter muted text */

    /* Premium Accent Colors */
    --accent-teal: 178 60% 48%;        /* #14b8a6 - warm teal */
    --accent-purple: 262 83% 58%;      /* #7c3aed - rich purple */
    --accent-orange: 24 95% 53%;       /* #ea580c - warm orange */
    --accent-foreground: 210 40% 98%;

    /* Additional accent colors for sidebar */
    --accent: 220 13% 25%;             /* Default accent */
    --accent-foreground: 210 20% 88%;

    /* Trading Status Colors - Muted for eye comfort */
    --success: 160 84% 39%;            /* #059669 - muted green */
    --success-foreground: 210 40% 98%;
    --success-muted: 160 84% 39% / 0.1;

    --warning: 32 95% 44%;             /* #d97706 - warm amber */
    --warning-foreground: 210 40% 98%;
    --warning-muted: 32 95% 44% / 0.1;

    --destructive: 0 84% 60%;          /* #dc2626 - soft red */
    --destructive-foreground: 210 40% 98%;
    --destructive-muted: 0 84% 60% / 0.1;

    /* Interface Elements - Brighter */
    --border: 220 13% 35% / 0.4;       /* More visible glass borders */
    --input: 220 13% 25% / 0.85;       /* Brighter glass input backgrounds */
    --ring: 217 91% 60%;               /* Focus ring color */

    --radius: 0.875rem;                /* Slightly larger radius for premium feel */

    /* Enhanced Chart Colors */
    --chart-1: 217 91% 60%;            /* Primary blue */
    --chart-2: 178 60% 48%;            /* Teal */
    --chart-3: 262 83% 58%;            /* Purple */
    --chart-4: 160 84% 39%;            /* Green */
    --chart-5: 24 95% 53%;             /* Orange */
    --chart-6: 340 82% 52%;            /* Pink */

    /* Premium Sidebar Colors - Enhanced contrast and accessibility */
    --sidebar-background: 220 13% 18%;     /* Darker background for better depth */
    --sidebar-foreground: 210 40% 98%;     /* High contrast white text */
    --sidebar-primary: 217 91% 60%;        /* Primary blue for active states */
    --sidebar-primary-foreground: 210 40% 98%; /* White text on primary */
    --sidebar-accent: 220 13% 22%;         /* Slightly lighter than background */
    --sidebar-accent-foreground: 210 20% 85%; /* Light gray text */
    --sidebar-border: 220 13% 30% / 0.3;   /* Subtle border */
    --sidebar-ring: 217 91% 60%;           /* Focus ring */
  }
}

/* Premium Component Styles */
@layer components {
  /* Glass Card Effect - Enhanced for unified appearance */
  .glass-card {
    @apply relative overflow-hidden rounded-xl transition-all duration-300;
    background: hsl(var(--card-glass));
    border: 1px solid hsl(var(--card-border));
    box-shadow: var(--shadow-glass);
    backdrop-filter: var(--blur-glass);
    -webkit-backdrop-filter: var(--blur-glass);
    position: relative;
  }

  .glass-card::before {
    content: '';
    @apply absolute inset-0 opacity-50 pointer-events-none;
    background: var(--gradient-shine);
    border-radius: inherit;
  }

  /* Enhanced hover effects for all glass cards */
  .glass-card:hover {
    border-color: hsl(var(--primary) / 0.2);
    box-shadow: var(--shadow-elevated);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glass-card:hover::before {
    @apply opacity-70;
  }

  /* Premium Button Styles */
  .btn-premium {
    @apply relative overflow-hidden rounded-lg font-medium transition-all duration-300;
    background: var(--gradient-button);
    box-shadow: var(--shadow-button);
    border: 1px solid hsl(var(--primary) / 0.3);
  }

  .btn-premium:hover {
    background: var(--gradient-button-hover);
    box-shadow: var(--shadow-button-hover);
    transform: translateY(-1px);
  }

  .btn-premium:active {
    transform: translateY(0);
    box-shadow: var(--shadow-button);
  }

  .btn-premium::before {
    content: '';
    @apply absolute inset-0 opacity-0 transition-opacity duration-300;
    background: var(--gradient-shine);
  }

  .btn-premium:hover::before {
    @apply opacity-100;
  }

  /* Golden Glossy Button Styles */
  .btn-golden {
    @apply relative overflow-hidden rounded-lg font-medium transition-all duration-300;
    background: var(--gradient-golden);
    box-shadow: var(--shadow-golden);
    border: 1px solid hsl(45 100% 60% / 0.4);
    color: hsl(45 15% 15%); /* Dark text for contrast on yellow */
  }

  .btn-golden:hover {
    background: var(--gradient-golden-hover);
    box-shadow: var(--shadow-golden-hover);
    transform: translateY(-2px);
  }

  .btn-golden:active {
    transform: translateY(0);
    box-shadow: var(--shadow-golden);
  }

  .btn-golden::before {
    content: '';
    @apply absolute inset-0 opacity-0 transition-opacity duration-300;
    background: var(--gradient-golden-shine);
  }

  .btn-golden:hover::before {
    @apply opacity-100;
  }

  /* Glass Input Styles */
  .input-glass {
    @apply relative rounded-lg border transition-all duration-300;
    background: hsl(var(--input));
    border-color: hsl(var(--border));
    backdrop-filter: var(--blur-glass);
    -webkit-backdrop-filter: var(--blur-glass);
  }

  .input-glass:focus {
    @apply outline-none ring-2;
    ring-color: hsl(var(--ring) / 0.3);
    border-color: hsl(var(--ring));
    box-shadow: var(--shadow-glow-primary);
  }

  /* Animated Gradient Text */
  .gradient-text {
    @apply bg-clip-text text-transparent;
    background: var(--gradient-primary);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  @keyframes shimmer {
    100% { transform: translateX(100%); }
  }

  @keyframes color-pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  @keyframes rainbow-glow {
    0% { box-shadow: 0 0 20px hsl(var(--primary) / 0.3); }
    25% { box-shadow: 0 0 20px hsl(var(--accent-teal) / 0.3); }
    50% { box-shadow: 0 0 20px hsl(var(--accent-purple) / 0.3); }
    75% { box-shadow: 0 0 20px hsl(var(--success) / 0.3); }
    100% { box-shadow: 0 0 20px hsl(var(--primary) / 0.3); }
  }

  /* Animation classes */
  .animate-color-pulse {
    animation: color-pulse 2s ease-in-out infinite;
  }

  .animate-rainbow-glow {
    animation: rainbow-glow 4s ease-in-out infinite;
  }

  .animate-gradient-shift {
    background-size: 200% 200%;
    animation: gradient-shift 8s ease-in-out infinite;
  }

  /* Enhanced background overlays */
  .bg-overlay-primary {
    background: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, transparent 50%, hsl(var(--primary) / 0.05) 100%);
  }

  .bg-overlay-colorful {
    background:
      radial-gradient(circle at 25% 25%, hsl(var(--primary) / 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, hsl(var(--accent-teal) / 0.08) 0%, transparent 50%),
      radial-gradient(circle at 50% 50%, hsl(var(--accent-purple) / 0.06) 0%, transparent 50%);
  }

  /* Enhanced Glow Effects */
  .glow-primary {
    box-shadow: var(--shadow-glow-primary);
  }

  .glow-success {
    box-shadow: var(--shadow-glow-success);
  }

  .glow-warning {
    box-shadow: var(--shadow-glow-warning);
  }

  .glow-teal {
    box-shadow: 0 0 24px hsl(var(--accent-teal) / 0.3), 0 0 48px hsl(var(--accent-teal) / 0.1);
  }

  .glow-purple {
    box-shadow: 0 0 24px hsl(var(--accent-purple) / 0.3), 0 0 48px hsl(var(--accent-purple) / 0.1);
  }

  .glow-orange {
    box-shadow: 0 0 24px hsl(var(--accent-orange) / 0.3), 0 0 48px hsl(var(--accent-orange) / 0.1);
  }

  /* Enhanced sidebar with primary blue accents */
  .sidebar-icon-container {
    @apply transition-all duration-300 hover:scale-110;
  }

  .sidebar-icon-container:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 0 16px hsl(var(--primary) / 0.3);
  }

  /* Primary blue enhanced glassmorphism for sidebar */
  .glass-unified {
    background:
      radial-gradient(circle at 30% 20%, hsl(var(--primary) / 0.06) 0%, transparent 40%),
      radial-gradient(circle at 70% 80%, hsl(var(--primary) / 0.04) 0%, transparent 40%);
  }

  /* Enhanced navigation hover effects with primary blue */
  .glass-interactive:hover {
    background: linear-gradient(135deg, hsl(var(--primary) / 0.12) 0%, hsl(var(--primary) / 0.08) 100%);
    border-color: hsl(var(--primary) / 0.3);
    box-shadow:
      0 4px 12px hsl(var(--primary) / 0.15),
      0 0 20px hsl(var(--primary) / 0.1);
  }

  /* Smooth Transitions */
  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-fast {
    transition: var(--transition-fast);
  }

  .transition-bounce {
    transition: var(--transition-bounce);
  }

  /* Dashboard Grid Alignment Utilities */
  .dashboard-grid {
    display: grid;
    gap: 1.5rem;
    align-items: start;
  }

  .dashboard-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .dashboard-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent card heights in grid rows */
  .grid-row-equal-height {
    display: grid;
    align-items: stretch;
  }

  .grid-row-equal-height > * {
    height: 100%;
  }

  /* Dashboard component consistency */
  .dashboard-component {
    @apply glass-card hover:shadow-elevated transition-all duration-300;
  }

  /* Nested glass cards within dashboard components - Brighter */
  .glass-card .glass-card {
    background: hsl(var(--card-glass) / 0.7);
    border: 1px solid hsl(var(--card-border) / 0.5);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  .glass-card .glass-card:hover {
    background: hsl(var(--card-glass) / 0.9);
    border-color: hsl(var(--primary) / 0.4);
  }

  /* Navigation glassmorphism - Enhanced for unified appearance */
  .glass-nav {
    @apply glass-card hover:shadow-elevated transition-all duration-300;
    border-radius: 0;
    border-left: none;
    border-right: none;
    background: hsl(var(--card-glass));
    backdrop-filter: var(--blur-glass);
    -webkit-backdrop-filter: var(--blur-glass);
  }

  .glass-sidebar {
    @apply glass-card hover:shadow-elevated transition-all duration-300;
    border-radius: 0;
    border-top: none;
    border-bottom: none;
    border-left: none;
    background:
      linear-gradient(180deg, hsl(var(--primary) / 0.08) 0%, hsl(var(--primary) / 0.04) 30%, hsl(var(--primary) / 0.06) 70%, hsl(var(--primary) / 0.08) 100%),
      linear-gradient(135deg, hsl(var(--card-glass)) 0%, hsl(var(--card-glass)) 100%);
    backdrop-filter: var(--blur-glass);
    -webkit-backdrop-filter: var(--blur-glass);
    position: fixed;
    height: 100vh;
    overflow: hidden;
    border-right: 1px solid hsl(var(--primary) / 0.2);
  }

  /* Navigation item glassmorphism */
  .glass-nav-item {
    @apply glass-card hover:shadow-elevated hover:border-primary/20 transition-all duration-300;
    background: hsl(var(--card-glass) / 0.7);
    border: 1px solid hsl(var(--card-border) / 0.5);
  }

  .glass-nav-item:hover {
    background: hsl(var(--card-glass) / 0.9);
    border-color: hsl(var(--primary) / 0.3);
  }

  .glass-nav-item.active {
    background: var(--gradient-primary);
    border-color: hsl(var(--primary) / 0.5);
    box-shadow: var(--shadow-glow-primary);
  }

  /* Form glassmorphism - Brighter */
  .glass-form {
    @apply glass-card hover:shadow-elevated transition-all duration-300;
  }

  .glass-input {
    @apply input-glass;
    background: hsl(var(--card-glass) / 0.75);
    border: 1px solid hsl(var(--card-border) / 0.5);
  }

  .glass-input:focus {
    background: hsl(var(--card-glass) / 0.9);
    border-color: hsl(var(--primary) / 0.6);
  }

  /* Modal and popup glassmorphism */
  .glass-modal {
    @apply glass-card;
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
  }

  .glass-overlay {
    background: hsl(var(--background) / 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Page-level glassmorphism utilities */
  .glass-page-header {
    @apply glass-container p-6 mb-8;
  }

  .glass-content-section {
    @apply glass-card hover:shadow-elevated transition-all duration-300 p-6 mb-6;
  }

  /* Enhanced interactive elements */
  .glass-interactive {
    @apply glass-card hover:shadow-elevated hover:border-primary/30 transition-all duration-300 cursor-pointer;
  }

  .glass-interactive:active {
    transform: translateY(1px);
    box-shadow: var(--shadow-glass);
  }

  /* Glassmorphism for specific component types */
  .glass-metric {
    @apply glass-card hover:shadow-elevated transition-all duration-300 p-4 text-center;
  }

  .glass-list-item {
    @apply glass-card hover:shadow-elevated hover:border-primary/20 transition-all duration-300 p-3 mb-2;
  }

  /* Global glassmorphism consistency - Brighter */
  .glass-unified {
    background: hsl(var(--card-glass)) !important;
    border: 1px solid hsl(var(--card-border)) !important;
    backdrop-filter: var(--blur-glass) !important;
    -webkit-backdrop-filter: var(--blur-glass) !important;
    box-shadow: var(--shadow-glass) !important;
  }

  /* Additional brightness utilities */
  .glass-bright {
    background: hsl(220 13% 30% / 0.9) !important;
    border: 1px solid hsl(220 13% 40% / 0.6) !important;
  }

  .glass-extra-bright {
    background: hsl(220 13% 35% / 0.95) !important;
    border: 1px solid hsl(220 13% 45% / 0.7) !important;
  }

  /* Legacy gradient-glass support for dark theme */
  .bg-gradient-glass {
    background: hsl(var(--card) / 0.5) !important;
    border: 1px solid hsl(var(--border) / 0.3) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
  }

  /* Sidebar specific glass effects */
  .sidebar-glass {
    background: hsl(var(--sidebar-background)) !important;
    border: 1px solid hsl(var(--sidebar-border)) !important;
    backdrop-filter: var(--blur-glass) !important;
    -webkit-backdrop-filter: var(--blur-glass) !important;
  }

  .sidebar-glass-card {
    background: hsl(var(--sidebar-accent) / 0.3) !important;
    border: 1px solid hsl(var(--sidebar-border)) !important;
    backdrop-filter: var(--blur-glass) !important;
    -webkit-backdrop-filter: var(--blur-glass) !important;
  }

  /* Enhanced logo section styling */
  .sidebar-logo-enhanced {
    background: hsl(var(--sidebar-accent) / 0.35) !important;
    border: 1px solid hsl(var(--sidebar-border)) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar-logo-enhanced:hover {
    background: hsl(var(--sidebar-accent) / 0.45) !important;
    border-color: hsl(var(--sidebar-primary) / 0.5) !important;
    box-shadow: 0 8px 25px hsl(var(--sidebar-primary) / 0.15) !important;
  }

  /* Icon ring enhancement */
  .sidebar-icon-ring {
    box-shadow:
      0 0 0 2px hsl(var(--sidebar-primary) / 0.2),
      0 4px 12px hsl(var(--sidebar-primary) / 0.15);
  }

  .sidebar-icon-ring:hover {
    box-shadow:
      0 0 0 2px hsl(var(--sidebar-primary) / 0.4),
      0 6px 16px hsl(var(--sidebar-primary) / 0.25);
  }

  /* Live status indicator enhancements */
  .live-indicator-collapsed {
    background: hsl(var(--success));
    border: 2px solid hsl(var(--sidebar-background));
    box-shadow:
      0 0 0 1px hsl(var(--success) / 0.3),
      0 2px 4px hsl(var(--success) / 0.2);
  }

  .live-indicator-collapsed:before {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: inherit;
    background: hsl(var(--success) / 0.2);
    animation: pulse 2s infinite;
  }

  /* Text shadow for better readability */
  .brand-text-shadow {
    text-shadow: 0 1px 2px hsl(var(--sidebar-background) / 0.5);
  }

  /* Enhanced live status text */
  .live-status-text {
    text-shadow: 0 1px 2px hsl(var(--success) / 0.3);
    letter-spacing: 0.05em;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground transition-colors duration-300;
    background-image:
      radial-gradient(circle at 20% 80%, hsl(217 91% 60% / 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, hsl(178 60% 48% / 0.03) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, hsl(262 83% 58% / 0.02) 0%, transparent 50%);
    min-height: 100vh;
  }

  .dark body {
    background-image:
      radial-gradient(circle at 20% 80%, hsl(217 91% 60% / 0.06) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, hsl(178 60% 48% / 0.06) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, hsl(262 83% 58% / 0.04) 0%, transparent 50%);
  }

  /* Global glassmorphism application with enhanced backgrounds */
  .glass-page {
    @apply glass-card;
    height: 100vh;
    border: none;
    border-radius: 0;
    overflow: hidden;
    background:
      radial-gradient(circle at 20% 80%, hsl(217 91% 60% / 0.08) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, hsl(178 60% 48% / 0.08) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, hsl(262 83% 58% / 0.06) 0%, transparent 50%),
      linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--background)) 100%);
  }

  /* Enhanced Authentication Form Styling */
  .auth-modal {
    @apply glass-card;
    background: hsl(var(--card-glass) / 0.95);
    border: 1px solid hsl(var(--card-border) / 0.8);
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    box-shadow:
      0 25px 50px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .auth-modal::before {
    content: '';
    @apply absolute inset-0 opacity-30 pointer-events-none;
    background: radial-gradient(
      circle at 50% 0%,
      rgba(59, 130, 246, 0.15) 0%,
      transparent 50%
    );
    border-radius: inherit;
  }

  /* Enhanced Auth Form Input Styling */
  .auth-input {
    @apply relative transition-all duration-300;
    background: hsl(var(--card-glass) / 0.8);
    border: 1px solid hsl(var(--card-border) / 0.6);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    color: hsl(var(--foreground) / 0.95);
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .auth-input:focus {
    background: hsl(var(--card-glass) / 0.95);
    border-color: hsl(217 91% 60% / 0.8);
    box-shadow:
      0 0 0 3px hsl(217 91% 60% / 0.15),
      0 4px 12px rgba(59, 130, 246, 0.15);
    outline: none;
  }

  .auth-input:hover {
    background: hsl(var(--card-glass) / 0.9);
    border-color: hsl(var(--card-border) / 0.8);
  }

  .auth-input::placeholder {
    color: hsl(var(--muted-foreground) / 0.85);
    font-weight: 400;
  }

  /* Enhanced Auth Button Styling */
  .auth-button {
    @apply relative overflow-hidden font-semibold transition-all duration-300;
    background: linear-gradient(135deg,
      hsl(217 91% 60%) 0%,
      hsl(178 60% 48%) 100%);
    border: 1px solid hsl(217 91% 60% / 0.3);
    color: white;
    box-shadow:
      0 4px 16px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .auth-button:hover {
    background: linear-gradient(135deg,
      hsl(217 91% 65%) 0%,
      hsl(178 60% 53%) 100%);
    box-shadow:
      0 6px 20px rgba(59, 130, 246, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }

  .auth-button:active {
    transform: translateY(0);
    box-shadow:
      0 2px 8px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .auth-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .auth-button::before {
    content: '';
    @apply absolute inset-0 opacity-0 transition-opacity duration-300;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.2) 0%,
      transparent 50%,
      rgba(255, 255, 255, 0.1) 100%);
  }

  .auth-button:hover::before {
    @apply opacity-100;
  }

  /* Enhanced Auth Label Styling */
  .auth-label {
    @apply font-semibold text-sm;
    color: hsl(var(--foreground) / 0.95);
    margin-bottom: 0.5rem;
    display: block;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* Enhanced Auth Error Styling */
  .auth-error {
    @apply text-sm font-medium;
    color: hsl(0 84% 65%);
    margin-top: 0.375rem;
    display: flex;
    align-items: center;
    gap: 0.375rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* Enhanced Auth Title Styling */
  .auth-title {
    @apply text-2xl font-bold text-center mb-2;
    background: linear-gradient(135deg,
      hsl(217 91% 60%) 0%,
      hsl(178 60% 48%) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
  }

  /* Enhanced Auth Subtitle Styling */
  .auth-subtitle {
    @apply text-center mb-6;
    color: hsl(var(--muted-foreground) / 0.9);
    font-weight: 500;
    font-size: 0.875rem;
    line-height: 1.5;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  /* Enhanced Auth Footer Link Styling */
  .auth-footer-link {
    @apply font-medium transition-colors duration-200;
    color: hsl(217 91% 60%);
    text-decoration: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .auth-footer-link:hover {
    color: hsl(178 60% 48%);
    text-decoration: underline;
  }

  /* Enhanced Auth Footer Text */
  .auth-footer-text {
    color: hsl(var(--muted-foreground) / 0.85);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  /* Enhanced Auth Icon Styling */
  .auth-icon {
    color: hsl(var(--muted-foreground) / 0.75);
    transition: color 0.2s ease;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
  }

  .auth-input:focus + .auth-icon,
  .auth-input:focus ~ .auth-icon {
    color: hsl(217 91% 60%);
  }

  .auth-input:hover + .auth-icon,
  .auth-input:hover ~ .auth-icon {
    color: hsl(var(--muted-foreground) / 0.85);
  }

  /* Enhanced Auth Toggle Button */
  .auth-toggle-button {
    @apply absolute right-0 top-0 h-full px-3 py-2;
    @apply flex items-center justify-center;
    @apply cursor-pointer outline-none;
    @apply transition-colors duration-200;
    background: transparent;
    border: none;
    color: hsl(var(--muted-foreground) / 0.75);
    border-radius: 0 0.375rem 0.375rem 0;
  }

  .auth-toggle-button:hover {
    color: hsl(var(--muted-foreground) / 0.9);
    background: hsl(var(--card-glass) / 0.3);
  }

  .auth-toggle-button:focus {
    color: hsl(217 91% 60%);
    outline: 2px solid hsl(217 91% 60% / 0.3);
    outline-offset: -2px;
  }

  .auth-toggle-button:active {
    color: hsl(217 91% 60%);
    background: hsl(var(--card-glass) / 0.5);
  }

  /* Ensure icons in toggle button don't inherit conflicting styles */
  .auth-toggle-button svg {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
    transition: none;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
  }

  /* Responsive toggle button enhancements */
  @media (max-width: 640px) {
    .auth-toggle-button {
      padding: 0.75rem;
    }

    .auth-toggle-button svg {
      width: 1.125rem;
      height: 1.125rem;
    }
  }

  /* Enhanced Auth Alert Styling */
  .auth-alert {
    @apply rounded-lg p-3 mb-4;
    background: hsl(0 84% 60% / 0.15);
    border: 1px solid hsl(0 84% 60% / 0.3);
    color: hsl(0 84% 65%);
    font-weight: 500;
    font-size: 0.875rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* Enhanced Auth Loading State */
  .auth-loading {
    @apply inline-flex items-center gap-2;
  }

  .auth-loading .spinner {
    @apply animate-spin;
    color: currentColor;
  }

  /* Responsive Auth Modal Enhancements */
  @media (max-width: 640px) {
    .auth-modal {
      margin: 1rem;
      max-width: calc(100vw - 2rem);
      max-height: calc(100vh - 2rem);
    }

    .auth-title {
      @apply text-xl;
    }

    .auth-subtitle {
      @apply text-xs;
      margin-bottom: 1.5rem;
    }

    .auth-input {
      @apply text-base; /* Prevent zoom on iOS */
      padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    }

    .auth-button {
      @apply text-base;
      padding: 0.875rem 1rem;
    }

    .auth-label {
      @apply text-xs;
    }

    .auth-error {
      @apply text-xs;
    }
  }

  /* Light theme specific auth enhancements */
  .auth-input::placeholder {
    color: hsl(var(--muted-foreground) / 0.75);
  }

  .auth-label {
    color: hsl(var(--foreground) / 0.9);
  }

  .auth-subtitle {
    color: hsl(var(--muted-foreground) / 0.85);
  }

  .auth-icon {
    color: hsl(var(--muted-foreground) / 0.7);
  }

  /* Dark mode specific auth enhancements */
  .dark .auth-modal {
    box-shadow:
      0 25px 50px -12px rgba(0, 0, 0, 0.5),
      0 0 0 1px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  .dark .auth-input {
    background: hsl(var(--card-glass) / 0.9);
    border-color: hsl(var(--card-border) / 0.8);
  }

  .dark .auth-input:focus {
    background: hsl(var(--card-glass) / 0.95);
    border-color: hsl(217 91% 60% / 0.9);
    box-shadow:
      0 0 0 3px hsl(217 91% 60% / 0.2),
      0 4px 12px rgba(59, 130, 246, 0.2);
  }

  .dark .auth-input::placeholder {
    color: hsl(var(--muted-foreground) / 0.85);
  }

  .dark .auth-label {
    color: hsl(var(--foreground) / 0.95);
  }

  .dark .auth-subtitle {
    color: hsl(var(--muted-foreground) / 0.9);
  }

  .dark .auth-icon {
    color: hsl(var(--muted-foreground) / 0.75);
  }

  .dark .auth-error {
    color: hsl(0 84% 70%);
  }

  .dark .auth-alert {
    background: hsl(0 84% 60% / 0.2);
    border-color: hsl(0 84% 60% / 0.4);
    color: hsl(0 84% 70%);
  }

  .dark .auth-toggle-button {
    color: hsl(var(--muted-foreground) / 0.75);
  }

  .dark .auth-toggle-button:hover {
    color: hsl(var(--muted-foreground) / 0.9);
    background: hsl(var(--card-glass) / 0.4);
  }

  .dark .auth-toggle-button:focus {
    color: hsl(217 91% 60%);
    outline-color: hsl(217 91% 60% / 0.4);
  }

  /* Ensure proper layout behavior */
  .sidebar-layout {
    height: 100vh;
    overflow: hidden;
  }

  .main-content-area {
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    /* Enhanced background for better readability */
    background:
      linear-gradient(135deg, hsl(var(--card)) / 0.08 0%, transparent 50%),
      linear-gradient(225deg, hsl(var(--primary)) / 0.04 0%, transparent 50%),
      linear-gradient(315deg, hsl(var(--accent-teal)) / 0.04 0%, transparent 50%),
      hsl(var(--background));
  }

  .glass-container {
    @apply glass-card hover:shadow-elevated transition-all duration-300;
  }

  /* Enhanced main content styling for better readability */
  .main-content-enhanced {
    background:
      radial-gradient(circle at 25% 25%, hsl(var(--primary)) / 0.06 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, hsl(var(--accent-teal)) / 0.06 0%, transparent 50%),
      radial-gradient(circle at 50% 50%, hsl(var(--card)) / 0.12 0%, transparent 70%),
      linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) / 0.1 100%);
    backdrop-filter: blur(0.5px);
    -webkit-backdrop-filter: blur(0.5px);
  }

  .glass-section {
    @apply glass-card hover:shadow-elevated transition-all duration-300;
    margin-bottom: 1.5rem;
  }

  /* Scrollbar Styling */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground;
  }
}
import React from 'react';
import { EnvironmentBanner } from './components/EnvironmentBanner';
import { ExchangeSelectionModal } from './components/exchange/ExchangeSelectionModal';
import { BinanceConnectionFormModal } from './components/exchange/BinanceConnectionFormModal';

const App: React.FC = () => {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-8 space-y-8">
        <h1 className="text-3xl font-bold text-center mb-8">Exchange Components Preview</h1>
        
        {/* Environment Banner */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Environment Banner</h2>
          <EnvironmentBanner />
        </div>

        {/* Exchange Selection Modal */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Exchange Selection Modal</h2>
          <ExchangeSelectionModal 
            open={true} 
            onOpenChange={() => {}} 
            onComplete={() => {}}
          />
        </div>

        {/* Binance Connection Form */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Binance Connection Form</h2>
          <BinanceConnectionFormModal
            onSubmit={async () => true}
            onCancel={() => {}}
            isConnecting={false}
            environment="mainnet"
          />
        </div>
      </div>
    </div>
  );
};

export default App;
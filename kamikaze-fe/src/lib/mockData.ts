// Mock data for the premium AI trading dashboard

export interface TradingBot {
  id: string;
  name: string;
  status: 'active' | 'paused' | 'stopped' | 'error';
  strategy: string;
  profit: number;
  profitPercentage: number;
  trades: number;
  winRate: number;
  lastTrade?: string;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface ChartDataPoint {
  name: string;
  value: number;
  profit?: number;
  loss?: number;
  volume?: number;
  [key: string]: string | number | undefined;
}

export interface Trade {
  id: string;
  symbol: string;
  type: 'buy' | 'sell';
  amount: number;
  price: number;
  profit: number;
  timestamp: string;
  status: 'completed' | 'pending' | 'failed';
}

export interface Asset {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
}

// Mock Trading Bots
export const mockTradingBots: TradingBot[] = [
  {
    id: '1',
    name: 'Alpha Momentum',
    status: 'active',
    strategy: 'Momentum Trading',
    profit: 12450,
    profitPercentage: 24.8,
    trades: 156,
    winRate: 78,
    lastTrade: '2 minutes ago',
    riskLevel: 'medium',
  },
  {
    id: '2',
    name: 'Beta Arbitrage',
    status: 'active',
    strategy: 'Arbitrage',
    profit: 8920,
    profitPercentage: 15.2,
    trades: 89,
    winRate: 85,
    lastTrade: '5 minutes ago',
    riskLevel: 'low',
  },
  {
    id: '3',
    name: 'Gamma Scalper',
    status: 'paused',
    strategy: 'Scalping',
    profit: -1250,
    profitPercentage: -3.1,
    trades: 234,
    winRate: 65,
    lastTrade: '1 hour ago',
    riskLevel: 'high',
  },
  {
    id: '4',
    name: 'Delta Grid',
    status: 'active',
    strategy: 'Grid Trading',
    profit: 5680,
    profitPercentage: 11.4,
    trades: 67,
    winRate: 72,
    lastTrade: '8 minutes ago',
    riskLevel: 'medium',
  },
];

// Mock Portfolio Performance Data
export const mockPortfolioData: ChartDataPoint[] = [
  { name: 'Jan', value: 50000, profit: 2000, loss: 500 },
  { name: 'Feb', value: 52500, profit: 3200, loss: 700 },
  { name: 'Mar', value: 55800, profit: 4100, loss: 900 },
  { name: 'Apr', value: 59200, profit: 3800, loss: 600 },
  { name: 'May', value: 63500, profit: 4500, loss: 800 },
  { name: 'Jun', value: 68200, profit: 5200, loss: 1000 },
  { name: 'Jul', value: 72800, profit: 4800, loss: 700 },
  { name: 'Aug', value: 76900, profit: 5500, loss: 1200 },
  { name: 'Sep', value: 81200, profit: 4900, loss: 600 },
  { name: 'Oct', value: 85800, profit: 5800, loss: 1100 },
  { name: 'Nov', value: 90500, profit: 6200, loss: 900 },
  { name: 'Dec', value: 95200, profit: 5900, loss: 800 },
];

// Mock Market Data
export const mockMarketData: ChartDataPoint[] = [
  { name: '00:00', value: 42000, volume: 1200000 },
  { name: '04:00', value: 42150, volume: 980000 },
  { name: '08:00', value: 41980, volume: 1450000 },
  { name: '12:00', value: 42300, volume: 1680000 },
  { name: '16:00', value: 42180, volume: 1320000 },
  { name: '20:00', value: 42450, volume: 1100000 },
];

// Mock Asset Allocation Data
export const mockAssetAllocation: ChartDataPoint[] = [
  { name: 'Bitcoin', value: 45, amount: 42750, changePercent: 2.34 },
  { name: 'Ethereum', value: 25, amount: 23750, changePercent: 1.87 },
  { name: 'Altcoins', value: 20, amount: 19000, changePercent: -0.45 },
  { name: 'Stablecoins', value: 10, amount: 9500, changePercent: 0.02 },
];

// Mock Recent Trades
export const mockRecentTrades: Trade[] = [
  {
    id: '1',
    symbol: 'BTC/USDT',
    type: 'buy',
    amount: 0.5,
    price: 42150,
    profit: 125.50,
    timestamp: '2 minutes ago',
    status: 'completed',
  },
  {
    id: '2',
    symbol: 'ETH/USDT',
    type: 'sell',
    amount: 2.3,
    price: 2580,
    profit: -45.20,
    timestamp: '5 minutes ago',
    status: 'completed',
  },
  {
    id: '3',
    symbol: 'ADA/USDT',
    type: 'buy',
    amount: 1000,
    price: 0.45,
    profit: 23.80,
    timestamp: '12 minutes ago',
    status: 'completed',
  },
  {
    id: '4',
    symbol: 'SOL/USDT',
    type: 'sell',
    amount: 5,
    price: 98.50,
    profit: 67.30,
    timestamp: '18 minutes ago',
    status: 'completed',
  },
  {
    id: '5',
    symbol: 'DOT/USDT',
    type: 'buy',
    amount: 50,
    price: 6.75,
    profit: 0,
    timestamp: '25 minutes ago',
    status: 'pending',
  },
];

// Mock Top Performing Assets
export const mockTopAssets: Asset[] = [
  {
    symbol: 'BTC',
    name: 'Bitcoin',
    price: 42150,
    change: 1250,
    changePercent: 3.05,
    volume: 28500000000,
    marketCap: 825000000000,
  },
  {
    symbol: 'ETH',
    name: 'Ethereum',
    price: 2580,
    change: -45,
    changePercent: -1.71,
    volume: 15200000000,
    marketCap: 310000000000,
  },
  {
    symbol: 'SOL',
    name: 'Solana',
    price: 98.50,
    change: 5.20,
    changePercent: 5.57,
    volume: 2100000000,
    marketCap: 42000000000,
  },
  {
    symbol: 'ADA',
    name: 'Cardano',
    price: 0.45,
    change: 0.02,
    changePercent: 4.65,
    volume: 890000000,
    marketCap: 15800000000,
  },
  {
    symbol: 'AVAX',
    name: 'Avalanche',
    price: 24.80,
    change: -1.20,
    changePercent: -4.62,
    volume: 650000000,
    marketCap: 9200000000,
  },
];

// Mock Risk Metrics
export const mockRiskMetrics = {
  portfolioValue: 95200,
  dailyPnL: 1250,
  dailyPnLPercent: 1.33,
  maxDrawdown: -5.2,
  sharpeRatio: 1.85,
  volatility: 12.4,
  var95: -2850,
  beta: 0.92,
};

// Mock AI Insights
export const mockAIInsights = [
  {
    id: '1',
    type: 'opportunity',
    title: 'BTC Momentum Signal',
    description: 'Strong bullish momentum detected in Bitcoin. Consider increasing position size.',
    confidence: 85,
    timestamp: '5 minutes ago',
  },
  {
    id: '2',
    type: 'warning',
    title: 'High Volatility Alert',
    description: 'Market volatility has increased by 15% in the last hour. Consider reducing risk exposure.',
    confidence: 92,
    timestamp: '12 minutes ago',
  },
  {
    id: '3',
    type: 'info',
    title: 'Arbitrage Opportunity',
    description: 'Price discrepancy detected between exchanges for ETH/USDT pair.',
    confidence: 78,
    timestamp: '18 minutes ago',
  },
];

// Utility functions for generating dynamic data
export const generateRandomPrice = (basePrice: number, volatility: number = 0.02) => {
  const change = (Math.random() - 0.5) * 2 * volatility;
  return basePrice * (1 + change);
};

export const generateTimeSeriesData = (points: number, baseValue: number, trend: number = 0) => {
  const data: ChartDataPoint[] = [];
  let currentValue = baseValue;
  
  for (let i = 0; i < points; i++) {
    const randomChange = (Math.random() - 0.5) * 0.1;
    currentValue = currentValue * (1 + trend + randomChange);
    
    data.push({
      name: `Point ${i + 1}`,
      value: Math.round(currentValue),
    });
  }
  
  return data;
};

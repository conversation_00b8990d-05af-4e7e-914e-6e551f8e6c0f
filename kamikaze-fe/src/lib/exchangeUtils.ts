import { ExchangeInfo } from '@/types/exchange';
import { Binance<PERSON>ogo, CoinbaseLogo, KrakenLogo, BybitLogo } from '@/components/logos';

export const SUPPORTED_EXCHANGES: ExchangeInfo[] = [
  {
    id: 'binance',
    name: 'binance',
    displayName: 'Binance',
    logo: 'BinanceLogo',
    description: 'World\'s largest cryptocurrency exchange by trading volume',
    isAvailable: true,
    features: ['Spot Trading', 'Futures Trading', 'Real-time Data', 'Advanced Orders']
  },
  {
    id: 'coinbase',
    name: 'coinbase',
    displayName: 'Coinbase Pro',
    logo: 'CoinbaseLogo',
    description: 'Professional trading platform by Coinbase',
    isAvailable: false,
    comingSoon: true,
    features: ['Spot Trading', 'Advanced Charts', 'API Access']
  },
  {
    id: 'kraken',
    name: 'kraken',
    displayName: 'Kraken',
    logo: 'KrakenLogo',
    description: 'Secure and reliable cryptocurrency exchange',
    isAvailable: false,
    comingSoon: true,
    features: ['Spot Trading', 'Margin Trading', 'Futures Trading']
  },
  {
    id: 'bybit',
    name: 'bybit',
    displayName: 'Bybit',
    logo: 'BybitLogo',
    description: 'Cryptocurrency derivatives exchange',
    isAvailable: false,
    comingSoon: true,
    features: ['Derivatives Trading', 'Spot Trading', 'Copy Trading']
  }
];

export const getExchangeInfo = (exchangeId: string): ExchangeInfo | undefined => {
  return SUPPORTED_EXCHANGES.find(exchange => exchange.id === exchangeId);
};

export const getAvailableExchanges = (): ExchangeInfo[] => {
  return SUPPORTED_EXCHANGES.filter(exchange => exchange.isAvailable);
};

export const getComingSoonExchanges = (): ExchangeInfo[] => {
  return SUPPORTED_EXCHANGES.filter(exchange => exchange.comingSoon);
};

export const getExchangeLogoComponent = (logoName: string) => {
  const logoComponents = {
    'BinanceLogo': BinanceLogo,
    'CoinbaseLogo': CoinbaseLogo,
    'KrakenLogo': KrakenLogo,
    'BybitLogo': BybitLogo,
  };

  return logoComponents[logoName as keyof typeof logoComponents] || null;
};

// Simple encryption/decryption for storing credentials
// Note: In production, consider using crypto-js or secure-ls for better encryption
export const encryptCredentials = (credentials: string): string => {
  // Simple base64 encoding for demo - replace with proper encryption in production
  return btoa(credentials);
};

export const decryptCredentials = (encryptedCredentials: string): string => {
  try {
    return atob(encryptedCredentials);
  } catch {
    return '';
  }
};

// Enhanced validation for Binance API keys with environment-specific rules
export const validateApiKey = (apiKey: string, isTestnet: boolean = false): boolean => {
  if (!apiKey || apiKey.length === 0) return false;

  // More lenient validation - just check basic format and length
  // Let the backend do the real validation with Binance API
  if (isTestnet) {
    // Testnet keys can vary in length, just check basic format
    return apiKey.length >= 20 && apiKey.length <= 128 && /^[A-Za-z0-9]+$/.test(apiKey);
  }

  // Mainnet API keys - more lenient length check
  return apiKey.length >= 40 && apiKey.length <= 128 && /^[A-Za-z0-9]+$/.test(apiKey);
};

export const validateSecretKey = (secretKey: string, isTestnet: boolean = false): boolean => {
  if (!secretKey || secretKey.length === 0) return false;

  // More lenient validation - just check basic format and length
  // Let the backend do the real validation with Binance API
  if (isTestnet) {
    return secretKey.length >= 20 && secretKey.length <= 128 && /^[A-Za-z0-9+/=]+$/.test(secretKey);
  }

  // Mainnet secret keys - more lenient length check
  return secretKey.length >= 40 && secretKey.length <= 128 && /^[A-Za-z0-9+/=]+$/.test(secretKey);
};

// Real-time validation feedback
export const getApiKeyValidationMessage = (apiKey: string, isTestnet: boolean = false): string | null => {
  if (!apiKey) return null;

  if (isTestnet) {
    if (apiKey.length < 20) return "API key too short (minimum 20 characters)";
    if (apiKey.length > 128) return "API key too long (maximum 128 characters)";
    if (!/^[A-Za-z0-9]+$/.test(apiKey)) return "API key contains invalid characters (only alphanumeric allowed)";
  } else {
    if (apiKey.length < 40) return "API key too short (minimum 40 characters)";
    if (apiKey.length > 128) return "API key too long (maximum 128 characters)";
    if (!/^[A-Za-z0-9]+$/.test(apiKey)) return "API key contains invalid characters (only alphanumeric allowed)";
  }

  return null;
};

export const getSecretKeyValidationMessage = (secretKey: string, isTestnet: boolean = false): string | null => {
  if (!secretKey) return null;

  if (isTestnet) {
    if (secretKey.length < 20) return "Secret key too short (minimum 20 characters)";
    if (secretKey.length > 128) return "Secret key too long (maximum 128 characters)";
    if (!/^[A-Za-z0-9+/=]+$/.test(secretKey)) return "Secret key contains invalid characters";
  } else {
    if (secretKey.length < 40) return "Secret key too short (minimum 40 characters)";
    if (secretKey.length > 128) return "Secret key too long (maximum 128 characters)";
    if (!/^[A-Za-z0-9+/=]+$/.test(secretKey)) return "Secret key contains invalid characters";
  }

  return null;
};

export const formatConnectionStatus = (status: string): { text: string; color: string } => {
  switch (status) {
    case 'connected':
      return { text: 'Connected', color: 'text-success' };
    case 'connecting':
      return { text: 'Connecting...', color: 'text-warning' };
    case 'error':
      return { text: 'Error', color: 'text-destructive' };
    default:
      return { text: 'Disconnected', color: 'text-muted-foreground' };
  }
};

// Environment-specific configuration (mainnet only)
export const BINANCE_ENVIRONMENT = {
  name: 'Mainnet',
  description: 'Live trading environment',
  color: 'primary',
  apiUrl: 'https://www.binance.com/en/my/settings/api-management',
  docsUrl: 'https://binance-docs.github.io/apidocs/spot/en/',
  icon: '◆',
  warningLevel: 'high'
} as const;

// Required API permissions for different trading modes
export const BINANCE_PERMISSIONS = {
  BASIC: {
    name: 'Basic (Read-Only)',
    permissions: ['Enable Reading'],
    description: 'View account information and balances',
    template: 'Enable Reading: ✓'
  },
  TRADING: {
    name: 'Trading',
    permissions: ['Enable Reading', 'Enable Spot & Margin Trading'],
    description: 'Full trading capabilities including spot and margin',
    template: 'Enable Reading: ✓\nEnable Spot & Margin Trading: ✓'
  },
  FUTURES: {
    name: 'Futures Trading',
    permissions: ['Enable Reading', 'Enable Spot & Margin Trading', 'Enable Futures'],
    description: 'Complete trading access including futures',
    template: 'Enable Reading: ✓\nEnable Spot & Margin Trading: ✓\nEnable Futures: ✓'
  }
} as const;

// Environment-specific styling classes - using website theme colors
export const getEnvironmentStyles = (isTestnet: boolean) => {
  // Use consistent website theme colors for both environments
  return {
    primary: 'text-primary',
    background: 'bg-primary/10',
    border: 'border-primary/30',
    badge: 'bg-primary/20 text-primary-foreground border-primary/40',
    button: 'bg-primary hover:bg-primary/90 text-primary-foreground',
    glow: 'shadow-primary/25'
  };
};

// Storage keys
export const STORAGE_KEYS = {
  EXCHANGE_CONNECTIONS: 'kamikaze_exchange_connections',
  SELECTED_EXCHANGE: 'kamikaze_selected_exchange',
} as const;

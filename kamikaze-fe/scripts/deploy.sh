#!/bin/bash

# Deployment script for Kamikaze Frontend
# This script handles local deployment testing and validation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GRE<PERSON>}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    
    log_info "Dependencies check passed"
}

# Install dependencies
install_deps() {
    log_info "Installing dependencies..."
    npm ci
}

# Run quality checks
run_quality_checks() {
    log_info "Running quality checks..."
    
    log_info "Type checking..."
    npm run type-check
    
    log_info "Linting..."
    npm run lint
    
    log_info "Running tests..."
    npm run test:run
    
    log_info "Quality checks passed"
}

# Build the application
build_app() {
    log_info "Building application..."
    npm run build
    log_info "Build completed"
}

# Deploy to Vercel (if token is available)
deploy_vercel() {
    if [ -z "$VERCEL_TOKEN" ]; then
        log_warn "VERCEL_TOKEN not set, skipping Vercel deployment"
        return
    fi
    
    log_info "Deploying to Vercel..."
    
    if ! command -v vercel &> /dev/null; then
        log_info "Installing Vercel CLI..."
        npm install -g vercel@latest
    fi
    
    vercel --prod --token="$VERCEL_TOKEN"
    log_info "Deployment completed"
}

# Main execution
main() {
    log_info "Starting deployment process..."
    
    check_dependencies
    install_deps
    run_quality_checks
    build_app
    deploy_vercel
    
    log_info "Deployment process completed successfully!"
}

# Run main function
main "$@"

#!/bin/bash

# Test Deployment Script
# This script tests the CI/CD pipeline components locally

set -e

echo "🧪 Testing CI/CD Pipeline Components..."

# Test 1: Type checking
echo "✅ Testing TypeScript compilation..."
npm run type-check
echo "✅ Type checking passed!"

# Test 2: Linting
echo "✅ Testing code linting..."
npm run lint
echo "✅ Linting passed!"

# Test 3: Testing
echo "✅ Running tests..."
npm run test:run
echo "✅ Tests passed!"

# Test 4: Build
echo "✅ Testing build process..."
npm run build
echo "✅ Build passed!"

# Test 5: Check Vercel configuration
echo "✅ Checking Vercel configuration..."
if [ -f ".vercel/project.json" ]; then
    echo "✅ Vercel project config found"
else
    echo "❌ Vercel project config missing"
    exit 1
fi

if [ -f "vercel.json" ]; then
    echo "✅ Vercel.json found"
else
    echo "❌ Vercel.json missing"
    exit 1
fi

# Test 6: Check GitHub Actions
if [ -f ".github/workflows/ci-cd.yml" ]; then
    echo "✅ GitHub Actions workflow found"
else
    echo "❌ GitHub Actions workflow missing"
    exit 1
fi

echo ""
echo "🎉 All CI/CD pipeline tests passed!"
echo ""
echo "Next steps:"
echo "1. Add VERCEL_TOKEN secret to GitHub repository"
echo "2. Update .vercel/project.json with your project details"
echo "3. Push to main branch to trigger deployment"
echo ""
echo "Your CI/CD pipeline is ready! 🚀"

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
.next/
out/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Test files
coverage/
*.test.ts
*.test.tsx
*.spec.ts
*.spec.tsx

# Documentation
README.md
*.md

# CI/CD
.github/
CI-CD-SETUP.md

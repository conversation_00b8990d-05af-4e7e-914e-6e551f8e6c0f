# Kamikaze AI - Frontend Application (CI/CD Branch)

<div align="center">
  <img src="public/favicon.svg" alt="Kamikaze AI Logo" width="80" height="80">

  **Advanced AI-Powered Trading Platform Frontend with CI/CD Pipeline**

  [![React](https://img.shields.io/badge/React-18.x-blue.svg)](https://reactjs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue.svg)](https://www.typescriptlang.org/)
  [![Vite](https://img.shields.io/badge/Vite-5.x-646CFF.svg)](https://vitejs.dev/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.x-38B2AC.svg)](https://tailwindcss.com/)
  [![Vercel](https://img.shields.io/badge/Vercel-000000.svg?logo=vercel)](https://vercel.com/)
  [![GitHub Actions](https://img.shields.io/badge/GitHub_Actions-2088FF.svg?logo=github-actions&logoColor=white)](https://github.com/features/actions)
</div>

## 🚀 About Kamikaze AI

Kamikaze AI is a cutting-edge trading platform that leverages artificial intelligence to provide advanced trading insights and portfolio management. This repository contains the frontend React application featuring a modern glassmorphism design, interactive charts, and comprehensive authentication system.

**This is the CI/CD branch** featuring automated deployment pipelines, testing workflows, and production-ready configurations.

### ✨ Key Features

- **🎨 Premium Glassmorphism UI** - Modern, translucent design with backdrop blur effects
- **🔐 Advanced Authentication** - Secure sign-in/sign-up with enhanced UX
- **📊 Interactive Portfolio Charts** - Real-time portfolio performance visualization
- **🌓 Dark/Light Theme Support** - Seamless theme switching
- **📱 Responsive Design** - Optimized for desktop, tablet, and mobile
- **♿ Accessibility Focused** - WCAG compliant with proper contrast ratios
- **⚡ High Performance** - Optimized with Vite and modern React patterns
- **🔄 CI/CD Pipeline** - Automated testing, building, and deployment
- **🚀 Vercel Integration** - Seamless deployment with preview environments
- **🧪 Automated Testing** - Comprehensive test suite with CI integration

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow
- **Automated Testing** on every push and pull request
- **Build Verification** to ensure production readiness
- **Code Quality Checks** with ESLint and TypeScript
- **Deployment Automation** to Vercel on successful builds
- **Preview Deployments** for pull requests

### Vercel Integration
- **Automatic Deployments** from the cicd-fe branch
- **Preview URLs** for every pull request
- **Environment Variables** management
- **Performance Monitoring** and analytics
- **Edge Network** global distribution

## 🛠️ Development Setup

### Prerequisites

- **Node.js** (v18 or higher) - [Install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- **npm** or **yarn** package manager
- **Git** for version control
- **Vercel CLI** (optional) - `npm i -g vercel`

### Quick Start

```bash
# Clone the repository
git clone https://github.com/Anki246/kamikaze-fe.git

# Navigate to project directory
cd kamikaze-fe

# Switch to CI/CD branch
git checkout cicd-fe

# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at `http://localhost:3000`

### Available Scripts

```bash
# Development server with hot reload
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run type checking
npm run type-check

# Lint code
npm run lint

# Run tests
npm test

# Run CI/CD tests
npm run test:ci

# Deploy to Vercel
npm run deploy
```

## 🏗️ Technology Stack

### Core Technologies
- **[React 18](https://reactjs.org/)** - Modern React with hooks and concurrent features
- **[TypeScript](https://www.typescriptlang.org/)** - Type-safe JavaScript development
- **[Vite](https://vitejs.dev/)** - Lightning-fast build tool and dev server
- **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework

### UI Components & Styling
- **[shadcn/ui](https://ui.shadcn.com/)** - High-quality, accessible React components
- **[Radix UI](https://www.radix-ui.com/)** - Unstyled, accessible UI primitives
- **[Lucide React](https://lucide.dev/)** - Beautiful & consistent icon library
- **[Class Variance Authority](https://cva.style/)** - Component variant management

### Form Management & Validation
- **[React Hook Form](https://react-hook-form.com/)** - Performant forms with easy validation
- **[Zod](https://zod.dev/)** - TypeScript-first schema validation

### CI/CD & Deployment
- **[GitHub Actions](https://github.com/features/actions)** - Automated workflows and CI/CD
- **[Vercel](https://vercel.com/)** - Deployment platform with edge network
- **[Vercel CLI](https://vercel.com/cli)** - Command-line deployment tools
- **[ESLint](https://eslint.org/)** - Code linting and quality enforcement
- **[Prettier](https://prettier.io/)** - Code formatting
- **[Vitest](https://vitest.dev/)** - Fast unit testing framework

## 🚀 Deployment & CI/CD

### Automated Deployment Pipeline

#### GitHub Actions Workflow (`.github/workflows/ci-cd.yml`)
```yaml
# Triggers on push to cicd-fe branch and pull requests
- Checkout code
- Setup Node.js environment
- Install dependencies
- Run type checking
- Run linting
- Run tests
- Build application
- Deploy to Vercel (on success)
```

#### Vercel Configuration
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "installCommand": "npm install",
  "framework": "vite"
}
```

### Manual Deployment Options

#### Option 1: Vercel CLI
```bash
# Install Vercel CLI globally
npm i -g vercel

# Login to Vercel
vercel login

# Deploy to preview
vercel

# Deploy to production
vercel --prod
```

#### Option 2: GitHub Integration
1. Connect repository to Vercel dashboard
2. Configure build settings
3. Set environment variables
4. Enable automatic deployments

#### Option 3: Manual Build
```bash
# Build for production
npm run build

# The dist/ folder contains the built application
# Upload contents to your hosting provider
```

### Environment Variables

#### Development (`.env.local`)
```env
# API Configuration
VITE_API_URL=http://localhost:8000
VITE_APP_ENV=development

# Authentication
VITE_AUTH_DOMAIN=dev.kamikaze-ai.com
VITE_ENABLE_ANALYTICS=false
```

#### Production (Vercel Dashboard)
```env
# API Configuration
VITE_API_URL=https://api.kamikaze-ai.com
VITE_APP_ENV=production

# Authentication
VITE_AUTH_DOMAIN=kamikaze-ai.com
VITE_ENABLE_ANALYTICS=true

# Vercel Analytics
VERCEL_ANALYTICS_ID=your_analytics_id
```

## 📁 Project Structure (CI/CD Branch)

```
kamikaze-fe/
├── .github/
│   └── workflows/
│       └── ci-cd.yml          # GitHub Actions CI/CD pipeline
├── .vercel/
│   └── project.json           # Vercel project configuration
├── scripts/
│   ├── deploy-vercel.sh       # Deployment automation script
│   └── test-deployment.sh     # Deployment testing script
├── public/                    # Static assets
│   ├── favicon.svg           # Kamikaze AI favicon
│   └── ...
├── src/
│   ├── components/           # Reusable React components
│   │   ├── auth/            # Authentication components
│   │   ├── ui/              # shadcn/ui components
│   │   └── ...
│   ├── pages/               # Page components
│   ├── lib/                 # Utility functions
│   ├── hooks/               # Custom React hooks
│   ├── types/               # TypeScript type definitions
│   ├── test/                # Test files
│   │   ├── ci-cd.test.ts    # CI/CD specific tests
│   │   └── ...
│   ├── index.css            # Global styles and Tailwind
│   └── main.tsx             # Application entry point
├── .vercelignore            # Vercel deployment ignore rules
├── vercel.json              # Vercel configuration
├── DEPLOYMENT_READY.md      # Deployment readiness checklist
├── README_CI_CD.md          # Detailed CI/CD documentation
├── VERCEL_CI_CD_SETUP.md    # Vercel setup guide
├── package.json             # Dependencies and scripts
├── tailwind.config.js       # Tailwind CSS configuration
├── tsconfig.json            # TypeScript configuration
└── vite.config.ts           # Vite configuration
```

## 🧪 Testing & Quality Assurance

### Test Suite
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run CI/CD specific tests
npm run test:ci

# Run type checking
npm run type-check

# Run linting
npm run lint

# Run formatting check
npm run format:check
```

### Automated Testing Pipeline
- **Unit Tests** - Component and utility function testing
- **Integration Tests** - Feature workflow testing
- **Type Checking** - TypeScript compilation verification
- **Linting** - Code quality and style enforcement
- **Build Testing** - Production build verification
- **Deployment Testing** - Post-deployment smoke tests

### Quality Gates
- ✅ All tests must pass
- ✅ TypeScript compilation must succeed
- ✅ ESLint rules must pass
- ✅ Build must complete successfully
- ✅ No critical security vulnerabilities
- ✅ Performance budgets must be met

## 🔍 Monitoring & Analytics

### Vercel Analytics
- **Performance Metrics** - Core Web Vitals tracking
- **User Analytics** - Page views and user behavior
- **Error Tracking** - Runtime error monitoring
- **Deployment Metrics** - Build and deployment statistics

### GitHub Insights
- **Action Workflows** - CI/CD pipeline monitoring
- **Code Quality** - Pull request checks and reviews
- **Security Alerts** - Dependency vulnerability scanning
- **Performance** - Build time and success rate tracking

## 🎨 Design System

### Color Palette
- **Primary Blue**: `#3b82f6` (hsl(217 91% 60%))
- **Accent Teal**: `#14b8a6` (hsl(178 60% 48%))
- **Background**: Dynamic based on theme
- **Glassmorphism**: Semi-transparent overlays with backdrop blur

### Typography
- **Font Family**: System font stack for optimal performance
- **Font Weights**: 400 (normal), 500 (medium), 600 (semibold), 700 (bold)
- **Responsive Scaling**: Fluid typography across device sizes

### Components
- **Glassmorphism Cards**: Translucent backgrounds with blur effects
- **Interactive Elements**: Smooth hover and focus transitions
- **Form Controls**: Enhanced accessibility and visual feedback

## 🤝 Contributing (CI/CD Branch)

### CI/CD Development Workflow
1. **Fork** the repository
2. **Create** a feature branch from `cicd-fe`: `git checkout -b feature/amazing-feature`
3. **Develop** with automated testing: `npm run test:watch`
4. **Test** CI/CD locally: `npm run test:ci`
5. **Commit** with conventional commits: `git commit -m 'feat: add amazing feature'`
6. **Push** to your fork: `git push origin feature/amazing-feature`
7. **Open** a Pull Request to `cicd-fe` branch
8. **Wait** for automated checks to pass
9. **Review** and merge after approval

### CI/CD Specific Guidelines
- **All PRs** must pass automated CI/CD pipeline
- **Tests** must be added for new features
- **Build** must succeed in production mode
- **Performance** budgets must not be exceeded
- **Security** scans must pass
- **Documentation** must be updated for CI/CD changes

### Commit Convention
We use [Conventional Commits](https://www.conventionalcommits.org/):
- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation updates
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Adding tests
- `chore:` - Maintenance tasks
- `ci:` - CI/CD pipeline changes
- `deploy:` - Deployment related changes

## 🚨 Troubleshooting

### Common CI/CD Issues

#### Build Failures
```bash
# Check build locally
npm run build

# Check TypeScript errors
npm run type-check

# Check linting issues
npm run lint
```

#### Deployment Issues
```bash
# Test deployment script
./scripts/test-deployment.sh

# Check Vercel logs
vercel logs

# Verify environment variables
vercel env ls
```

#### Test Failures
```bash
# Run tests with verbose output
npm run test -- --verbose

# Run specific test file
npm run test -- src/test/ci-cd.test.ts

# Check test coverage
npm run test:coverage
```

## 📚 Documentation

### CI/CD Specific Documentation
- **[DEPLOYMENT_READY.md](./DEPLOYMENT_READY.md)** - Deployment readiness checklist
- **[README_CI_CD.md](./README_CI_CD.md)** - Detailed CI/CD pipeline documentation
- **[VERCEL_CI_CD_SETUP.md](./VERCEL_CI_CD_SETUP.md)** - Vercel setup and configuration guide

### General Documentation
- **[GitHub Actions Workflow](./.github/workflows/ci-cd.yml)** - CI/CD pipeline configuration
- **[Vercel Configuration](./vercel.json)** - Deployment settings
- **[Package Scripts](./package.json)** - Available npm scripts

## 🐛 Bug Reports & Feature Requests

### Reporting CI/CD Issues
Please use [GitHub Issues](https://github.com/Anki246/kamikaze-fe/issues) with the `ci-cd` label. Include:
- **Pipeline Step**: Which CI/CD step failed
- **Error Logs**: Complete error messages and logs
- **Environment**: Branch, commit hash, and environment details
- **Reproduction**: Steps to reproduce the issue
- **Expected Behavior**: What should have happened

### CI/CD Feature Requests
Submit enhancement requests for the CI/CD pipeline with:
- **Use Case**: Describe the CI/CD improvement needed
- **Proposed Solution**: Your suggested implementation
- **Impact**: How it improves the development workflow
- **Alternatives**: Any alternative approaches considered

## 📄 License

This project is proprietary software. All rights reserved.

## 🔗 Links

- **Repository**: [https://github.com/Anki246/kamikaze-fe](https://github.com/Anki246/kamikaze-fe)
- **CI/CD Branch**: [https://github.com/Anki246/kamikaze-fe/tree/cicd-fe](https://github.com/Anki246/kamikaze-fe/tree/cicd-fe)
- **Issues**: [https://github.com/Anki246/kamikaze-fe/issues](https://github.com/Anki246/kamikaze-fe/issues)
- **Actions**: [https://github.com/Anki246/kamikaze-fe/actions](https://github.com/Anki246/kamikaze-fe/actions)
- **Vercel Dashboard**: [Kamikaze AI Vercel Project](https://vercel.com/dashboard)

## 📞 Support

For CI/CD and deployment support:
- **GitHub Issues**: Technical issues and pipeline problems
- **GitHub Discussions**: CI/CD best practices and questions
- **Vercel Support**: Deployment and hosting issues

---

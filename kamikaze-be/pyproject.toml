[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | env
  | \.env
  | _build
  | buck-out
  | build
  | dist
  | node_modules
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["venv/*", "env/*", ".venv/*", ".env/*", "node_modules/*"]
skip = ["src/agents/fluxtrader/fastmcp_client.py", "src/mcp_servers/binance_fastmcp_server.py"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "venv",
    "env",
    ".venv",
    ".env",
    "node_modules",
    "build",
    "dist"
]

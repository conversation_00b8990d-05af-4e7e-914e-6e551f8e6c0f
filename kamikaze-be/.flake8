[flake8]
# Flake8 configuration for Kamikaze AI

# Maximum line length
max-line-length = 88

# Ignore specific error codes
ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # E501: line too long (handled by black)
    E501,
    # F401: imported but unused (handled by isort/IDE)
    F401

# Exclude directories and files
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    env,
    .env,
    node_modules,
    build,
    dist,
    .tox,
    .pytest_cache,
    htmlcov,
    .coverage,
    *.egg-info,
    migrations,
    # Specific files to exclude
    src/agents/fluxtrader/fastmcp_client.py,
    src/mcp_servers/binance_fastmcp_server.py

# Maximum complexity
max-complexity = 10

# Enable specific checks
select = 
    E,  # pycodestyle errors
    W,  # pycodestyle warnings
    F,  # pyflakes
    C,  # mccabe complexity

# Per-file ignores
per-file-ignores = 
    # Tests can have longer lines and unused imports
    tests/*:E501,F401,F811
    # Configuration files can have longer lines
    */config.py:E501
    # Init files can have unused imports
    __init__.py:F401

# Show source code for each error
show-source = True

# Show pep8 error codes
show-pep8 = True

# Count errors
count = True

# Statistics
statistics = True

# Format
format = %(path)s:%(row)d:%(col)d: %(code)s %(text)s

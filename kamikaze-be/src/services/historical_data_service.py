"""
Historical Data Service for Backtesting
Fetches, processes, and stores historical market data from Binance API
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import aiohttp
import asyncpg
from decimal import Decimal

from ..infrastructure.database_config import DatabaseConfig
from ..shared.logging_config import setup_logging

logger = setup_logging("historical_data_service")


class HistoricalDataService:
    """
    Service for managing historical market data for backtesting.
    
    Features:
    - Fetch historical klines from Binance API
    - Store data in PostgreSQL with deduplication
    - Efficient data retrieval for backtesting
    - Data validation and normalization
    - Support for multiple timeframes
    """
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.binance_api_url = "https://api.binance.com/api/v3"
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Timeframe mapping for Binance API
        self.timeframe_mapping = {
            '1m': '1m',
            '5m': '5m', 
            '15m': '15m',
            '1h': '1h',
            '4h': '4h',
            '1d': '1d',
            '1w': '1w'
        }
        
        # Maximum klines per request (Binance limit)
        self.max_klines_per_request = 1000
    
    async def connect(self) -> bool:
        """Connect to database and initialize HTTP session."""
        try:
            # Connect to database
            config = DatabaseConfig()
            self.pool = await asyncpg.create_pool(**config.connection_params)
            
            # Test connection
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            
            # Initialize HTTP session
            self.session = aiohttp.ClientSession()
            
            logger.info("✅ Historical Data Service connected successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect Historical Data Service: {e}")
            return False
    
    async def disconnect(self):
        """Close database and HTTP connections."""
        if self.pool:
            await self.pool.close()
        if self.session:
            await self.session.close()
        logger.info("🔌 Historical Data Service disconnected")
    
    async def fetch_historical_data(
        self, 
        symbol: str, 
        timeframe: str, 
        start_date: datetime, 
        end_date: datetime,
        force_refresh: bool = False
    ) -> List[Dict]:
        """
        Fetch historical klines data for a symbol and timeframe.
        
        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe (e.g., '1h', '1d')
            start_date: Start date for data
            end_date: End date for data
            force_refresh: Force refresh from API even if data exists
            
        Returns:
            List of klines data dictionaries
        """
        if not self.pool or not self.session:
            raise Exception("Service not connected")
        
        # Check if data already exists (unless force refresh)
        if not force_refresh:
            existing_data = await self._get_existing_data(symbol, timeframe, start_date, end_date)
            if existing_data:
                logger.info(f"📊 Using existing data for {symbol} {timeframe}")
                return existing_data
        
        logger.info(f"🔄 Fetching historical data for {symbol} {timeframe} from {start_date} to {end_date}")
        
        # Fetch data from Binance API
        klines_data = await self._fetch_from_binance(symbol, timeframe, start_date, end_date)
        
        if klines_data:
            # Store in database
            await self._store_klines_data(symbol, timeframe, klines_data)
            logger.info(f"✅ Stored {len(klines_data)} klines for {symbol} {timeframe}")
        
        return klines_data
    
    async def _get_existing_data(
        self, 
        symbol: str, 
        timeframe: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> List[Dict]:
        """Check if historical data already exists in database."""
        try:
            async with self.pool.acquire() as conn:
                query = """
                    SELECT open_time, close_time, open_price, high_price, low_price, 
                           close_price, volume, quote_volume, trades_count,
                           taker_buy_base_volume, taker_buy_quote_volume
                    FROM historical_klines 
                    WHERE symbol = $1 AND timeframe = $2 
                    AND open_time >= $3 AND close_time <= $4
                    ORDER BY open_time ASC
                """
                
                start_timestamp = int(start_date.timestamp() * 1000)
                end_timestamp = int(end_date.timestamp() * 1000)
                
                rows = await conn.fetch(query, symbol, timeframe, start_timestamp, end_timestamp)
                
                if not rows:
                    return []
                
                # Convert to expected format
                klines_data = []
                for row in rows:
                    klines_data.append({
                        'open_time': row['open_time'],
                        'close_time': row['close_time'],
                        'open': float(row['open_price']),
                        'high': float(row['high_price']),
                        'low': float(row['low_price']),
                        'close': float(row['close_price']),
                        'volume': float(row['volume']),
                        'quote_volume': float(row['quote_volume']),
                        'trades_count': row['trades_count'],
                        'taker_buy_base_volume': float(row['taker_buy_base_volume']),
                        'taker_buy_quote_volume': float(row['taker_buy_quote_volume'])
                    })
                
                return klines_data
                
        except Exception as e:
            logger.error(f"❌ Error checking existing data: {e}")
            return []
    
    async def _fetch_from_binance(
        self, 
        symbol: str, 
        timeframe: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> List[Dict]:
        """Fetch klines data from Binance API."""
        try:
            binance_interval = self.timeframe_mapping.get(timeframe)
            if not binance_interval:
                raise ValueError(f"Unsupported timeframe: {timeframe}")
            
            start_timestamp = int(start_date.timestamp() * 1000)
            end_timestamp = int(end_date.timestamp() * 1000)
            
            all_klines = []
            current_start = start_timestamp
            
            while current_start < end_timestamp:
                # Prepare request parameters
                params = {
                    'symbol': symbol.upper(),
                    'interval': binance_interval,
                    'startTime': current_start,
                    'endTime': end_timestamp,
                    'limit': self.max_klines_per_request
                }
                
                # Make API request
                url = f"{self.binance_api_url}/klines"
                async with self.session.get(url, params=params) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"Binance API error {response.status}: {error_text}")
                    
                    klines_raw = await response.json()
                
                if not klines_raw:
                    break
                
                # Process klines data
                for kline in klines_raw:
                    kline_data = {
                        'open_time': int(kline[0]),
                        'close_time': int(kline[6]),
                        'open': float(kline[1]),
                        'high': float(kline[2]),
                        'low': float(kline[3]),
                        'close': float(kline[4]),
                        'volume': float(kline[5]),
                        'quote_volume': float(kline[7]),
                        'trades_count': int(kline[8]),
                        'taker_buy_base_volume': float(kline[9]),
                        'taker_buy_quote_volume': float(kline[10])
                    }
                    all_klines.append(kline_data)
                
                # Update start time for next batch
                if klines_raw:
                    current_start = int(klines_raw[-1][6]) + 1  # Last close time + 1ms
                else:
                    break
                
                # Rate limiting - Binance allows 1200 requests per minute
                await asyncio.sleep(0.1)
            
            logger.info(f"📥 Fetched {len(all_klines)} klines from Binance for {symbol} {timeframe}")
            return all_klines
            
        except Exception as e:
            logger.error(f"❌ Error fetching from Binance: {e}")
            raise
    
    async def _store_klines_data(self, symbol: str, timeframe: str, klines_data: List[Dict]):
        """Store klines data in database with deduplication."""
        try:
            async with self.pool.acquire() as conn:
                # Use UPSERT to handle duplicates
                query = """
                    INSERT INTO historical_klines (
                        symbol, timeframe, open_time, close_time, open_price, high_price,
                        low_price, close_price, volume, quote_volume, trades_count,
                        taker_buy_base_volume, taker_buy_quote_volume
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                    ON CONFLICT (symbol, timeframe, open_time) DO UPDATE SET
                        close_time = EXCLUDED.close_time,
                        open_price = EXCLUDED.open_price,
                        high_price = EXCLUDED.high_price,
                        low_price = EXCLUDED.low_price,
                        close_price = EXCLUDED.close_price,
                        volume = EXCLUDED.volume,
                        quote_volume = EXCLUDED.quote_volume,
                        trades_count = EXCLUDED.trades_count,
                        taker_buy_base_volume = EXCLUDED.taker_buy_base_volume,
                        taker_buy_quote_volume = EXCLUDED.taker_buy_quote_volume
                """
                
                # Prepare data for batch insert
                values = []
                for kline in klines_data:
                    values.append((
                        symbol, timeframe, kline['open_time'], kline['close_time'],
                        Decimal(str(kline['open'])), Decimal(str(kline['high'])),
                        Decimal(str(kline['low'])), Decimal(str(kline['close'])),
                        Decimal(str(kline['volume'])), Decimal(str(kline['quote_volume'])),
                        kline['trades_count'], Decimal(str(kline['taker_buy_base_volume'])),
                        Decimal(str(kline['taker_buy_quote_volume']))
                    ))
                
                # Execute batch insert
                await conn.executemany(query, values)
                
        except Exception as e:
            logger.error(f"❌ Error storing klines data: {e}")
            raise
    
    async def get_data_for_backtesting(
        self, 
        symbol: str, 
        timeframe: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> List[Dict]:
        """
        Get historical data optimized for backtesting.
        Ensures data is available and fetches if missing.
        """
        # First try to get existing data
        data = await self._get_existing_data(symbol, timeframe, start_date, end_date)
        
        # If no data or incomplete data, fetch from API
        if not data:
            data = await self.fetch_historical_data(symbol, timeframe, start_date, end_date)
        
        return data
    
    async def get_available_symbols(self) -> List[str]:
        """Get list of symbols with available historical data."""
        try:
            async with self.pool.acquire() as conn:
                query = "SELECT DISTINCT symbol FROM historical_klines ORDER BY symbol"
                rows = await conn.fetch(query)
                return [row['symbol'] for row in rows]
        except Exception as e:
            logger.error(f"❌ Error getting available symbols: {e}")
            return []
    
    async def get_data_range(self, symbol: str, timeframe: str) -> Optional[Tuple[datetime, datetime]]:
        """Get the date range of available data for a symbol and timeframe."""
        try:
            async with self.pool.acquire() as conn:
                query = """
                    SELECT MIN(open_time) as min_time, MAX(close_time) as max_time
                    FROM historical_klines 
                    WHERE symbol = $1 AND timeframe = $2
                """
                row = await conn.fetchrow(query, symbol, timeframe)
                
                if row and row['min_time'] and row['max_time']:
                    min_date = datetime.fromtimestamp(row['min_time'] / 1000)
                    max_date = datetime.fromtimestamp(row['max_time'] / 1000)
                    return (min_date, max_date)
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Error getting data range: {e}")
            return None

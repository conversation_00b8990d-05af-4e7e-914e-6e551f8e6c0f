"""
Trading Strategy Framework
Implements various trading strategies for backtesting
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime
from decimal import Decimal
import statistics

from ..shared.logging_config import setup_logging

logger = setup_logging("trading_strategies")


class TradingStrategy(ABC):
    """Abstract base class for trading strategies."""
    
    def __init__(self, parameters: Dict[str, Any]):
        self.parameters = parameters
        self.name = self.__class__.__name__
        self.indicators_cache = {}
    
    @abstractmethod
    async def generate_signals(
        self, 
        market_data: Dict[str, Dict], 
        portfolio: Any, 
        config: Any
    ) -> List[Dict]:
        """Generate trading signals based on market data and portfolio state."""
        pass
    
    def calculate_sma(self, prices: List[float], period: int) -> Optional[float]:
        """Calculate Simple Moving Average."""
        if len(prices) < period:
            return None
        return statistics.mean(prices[-period:])
    
    def calculate_ema(self, prices: List[float], period: int, previous_ema: Optional[float] = None) -> Optional[float]:
        """Calculate Exponential Moving Average."""
        if not prices:
            return None
        
        if previous_ema is None:
            # Use SMA for first EMA calculation
            if len(prices) < period:
                return None
            return self.calculate_sma(prices[:period], period)
        
        multiplier = 2 / (period + 1)
        return (prices[-1] * multiplier) + (previous_ema * (1 - multiplier))
    
    def calculate_rsi(self, prices: List[float], period: int = 14) -> Optional[float]:
        """Calculate Relative Strength Index."""
        if len(prices) < period + 1:
            return None
        
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(change))
        
        if len(gains) < period:
            return None
        
        avg_gain = statistics.mean(gains[-period:])
        avg_loss = statistics.mean(losses[-period:])
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_bollinger_bands(
        self, 
        prices: List[float], 
        period: int = 20, 
        std_dev: float = 2
    ) -> Optional[Dict[str, float]]:
        """Calculate Bollinger Bands."""
        if len(prices) < period:
            return None
        
        sma = self.calculate_sma(prices, period)
        if sma is None:
            return None
        
        variance = statistics.variance(prices[-period:])
        std = variance ** 0.5
        
        return {
            'upper': sma + (std * std_dev),
            'middle': sma,
            'lower': sma - (std * std_dev)
        }
    
    def calculate_macd(
        self, 
        prices: List[float], 
        fast_period: int = 12, 
        slow_period: int = 26, 
        signal_period: int = 9
    ) -> Optional[Dict[str, float]]:
        """Calculate MACD (Moving Average Convergence Divergence)."""
        if len(prices) < slow_period:
            return None
        
        # Calculate EMAs
        fast_ema = self.calculate_ema(prices, fast_period)
        slow_ema = self.calculate_ema(prices, slow_period)
        
        if fast_ema is None or slow_ema is None:
            return None
        
        macd_line = fast_ema - slow_ema
        
        # For signal line, we'd need historical MACD values
        # Simplified version returns just the MACD line
        return {
            'macd': macd_line,
            'signal': 0,  # Simplified
            'histogram': macd_line
        }


class SMAStrategy(TradingStrategy):
    """Simple Moving Average Crossover Strategy."""
    
    def __init__(self, parameters: Dict[str, Any]):
        super().__init__(parameters)
        self.fast_period = parameters.get('fast_period', 10)
        self.slow_period = parameters.get('slow_period', 30)
        self.price_history = {}
    
    async def generate_signals(
        self, 
        market_data: Dict[str, Dict], 
        portfolio: Any, 
        config: Any
    ) -> List[Dict]:
        """Generate SMA crossover signals."""
        signals = []
        
        for symbol, data in market_data.items():
            if symbol not in config.trading_pairs:
                continue
            
            # Update price history
            if symbol not in self.price_history:
                self.price_history[symbol] = []
            
            close_price = float(data['close'])
            self.price_history[symbol].append(close_price)
            
            # Keep only necessary history
            max_period = max(self.fast_period, self.slow_period)
            if len(self.price_history[symbol]) > max_period * 2:
                self.price_history[symbol] = self.price_history[symbol][-max_period * 2:]
            
            prices = self.price_history[symbol]
            
            # Calculate SMAs
            fast_sma = self.calculate_sma(prices, self.fast_period)
            slow_sma = self.calculate_sma(prices, self.slow_period)
            
            if fast_sma is None or slow_sma is None:
                continue
            
            # Check for crossover signals
            if len(prices) >= self.slow_period + 1:
                prev_fast_sma = self.calculate_sma(prices[:-1], self.fast_period)
                prev_slow_sma = self.calculate_sma(prices[:-1], self.slow_period)
                
                if prev_fast_sma is not None and prev_slow_sma is not None:
                    # Bullish crossover (fast SMA crosses above slow SMA)
                    if prev_fast_sma <= prev_slow_sma and fast_sma > slow_sma:
                        if symbol not in portfolio.positions:
                            signals.append({
                                'symbol': symbol,
                                'action': 'BUY',
                                'reason': f'SMA Bullish Crossover (Fast: {fast_sma:.4f}, Slow: {slow_sma:.4f})',
                                'confidence': 0.7,
                                'position_size': 10  # 10% of portfolio
                            })
                    
                    # Bearish crossover (fast SMA crosses below slow SMA)
                    elif prev_fast_sma >= prev_slow_sma and fast_sma < slow_sma:
                        if symbol in portfolio.positions:
                            signals.append({
                                'symbol': symbol,
                                'action': 'CLOSE',
                                'reason': f'SMA Bearish Crossover (Fast: {fast_sma:.4f}, Slow: {slow_sma:.4f})'
                            })
        
        return signals


class RSIMeanReversionStrategy(TradingStrategy):
    """RSI-based Mean Reversion Strategy."""
    
    def __init__(self, parameters: Dict[str, Any]):
        super().__init__(parameters)
        self.rsi_period = parameters.get('rsi_period', 14)
        self.oversold_threshold = parameters.get('oversold', 30)
        self.overbought_threshold = parameters.get('overbought', 70)
        self.price_history = {}
    
    async def generate_signals(
        self, 
        market_data: Dict[str, Dict], 
        portfolio: Any, 
        config: Any
    ) -> List[Dict]:
        """Generate RSI mean reversion signals."""
        signals = []
        
        for symbol, data in market_data.items():
            if symbol not in config.trading_pairs:
                continue
            
            # Update price history
            if symbol not in self.price_history:
                self.price_history[symbol] = []
            
            close_price = float(data['close'])
            self.price_history[symbol].append(close_price)
            
            # Keep only necessary history
            if len(self.price_history[symbol]) > self.rsi_period * 3:
                self.price_history[symbol] = self.price_history[symbol][-self.rsi_period * 3:]
            
            prices = self.price_history[symbol]
            
            # Calculate RSI
            rsi = self.calculate_rsi(prices, self.rsi_period)
            
            if rsi is None:
                continue
            
            # Generate signals based on RSI levels
            if rsi <= self.oversold_threshold:
                if symbol not in portfolio.positions:
                    signals.append({
                        'symbol': symbol,
                        'action': 'BUY',
                        'reason': f'RSI Oversold ({rsi:.2f})',
                        'confidence': min(0.9, (self.oversold_threshold - rsi) / self.oversold_threshold + 0.5),
                        'position_size': 8,  # 8% of portfolio
                        'take_profit': close_price * 1.05,  # 5% profit target
                        'stop_loss': close_price * 0.97   # 3% stop loss
                    })
            
            elif rsi >= self.overbought_threshold:
                if symbol in portfolio.positions:
                    signals.append({
                        'symbol': symbol,
                        'action': 'CLOSE',
                        'reason': f'RSI Overbought ({rsi:.2f})'
                    })
        
        return signals


class MomentumBreakoutStrategy(TradingStrategy):
    """Momentum-based Breakout Strategy."""
    
    def __init__(self, parameters: Dict[str, Any]):
        super().__init__(parameters)
        self.lookback_period = parameters.get('lookback_period', 20)
        self.breakout_threshold = parameters.get('breakout_threshold', 2.0)  # Standard deviations
        self.price_history = {}
        self.volume_history = {}
    
    async def generate_signals(
        self, 
        market_data: Dict[str, Dict], 
        portfolio: Any, 
        config: Any
    ) -> List[Dict]:
        """Generate momentum breakout signals."""
        signals = []
        
        for symbol, data in market_data.items():
            if symbol not in config.trading_pairs:
                continue
            
            # Update price and volume history
            if symbol not in self.price_history:
                self.price_history[symbol] = []
                self.volume_history[symbol] = []
            
            close_price = float(data['close'])
            volume = float(data['volume'])
            
            self.price_history[symbol].append(close_price)
            self.volume_history[symbol].append(volume)
            
            # Keep only necessary history
            if len(self.price_history[symbol]) > self.lookback_period * 2:
                self.price_history[symbol] = self.price_history[symbol][-self.lookback_period * 2:]
                self.volume_history[symbol] = self.volume_history[symbol][-self.lookback_period * 2:]
            
            prices = self.price_history[symbol]
            volumes = self.volume_history[symbol]
            
            if len(prices) < self.lookback_period:
                continue
            
            # Calculate breakout levels
            recent_prices = prices[-self.lookback_period:]
            mean_price = statistics.mean(recent_prices)
            std_price = statistics.stdev(recent_prices) if len(recent_prices) > 1 else 0
            
            upper_breakout = mean_price + (std_price * self.breakout_threshold)
            lower_breakout = mean_price - (std_price * self.breakout_threshold)
            
            # Calculate volume confirmation
            recent_volumes = volumes[-self.lookback_period:]
            avg_volume = statistics.mean(recent_volumes)
            volume_multiplier = volume / avg_volume if avg_volume > 0 else 1
            
            # Generate signals
            if close_price > upper_breakout and volume_multiplier > 1.5:
                if symbol not in portfolio.positions:
                    confidence = min(0.8, (close_price - upper_breakout) / upper_breakout + 0.4)
                    signals.append({
                        'symbol': symbol,
                        'action': 'BUY',
                        'reason': f'Upward Breakout (Price: {close_price:.4f}, Breakout: {upper_breakout:.4f}, Vol: {volume_multiplier:.2f}x)',
                        'confidence': confidence,
                        'position_size': 12,  # 12% of portfolio
                        'take_profit': close_price * 1.08,  # 8% profit target
                        'stop_loss': close_price * 0.95    # 5% stop loss
                    })
            
            elif close_price < lower_breakout and volume_multiplier > 1.5:
                if symbol in portfolio.positions:
                    signals.append({
                        'symbol': symbol,
                        'action': 'CLOSE',
                        'reason': f'Downward Breakout (Price: {close_price:.4f}, Breakout: {lower_breakout:.4f})'
                    })
        
        return signals


class StrategyFactory:
    """Factory class for creating trading strategies."""
    
    STRATEGIES = {
        'sma_crossover': SMAStrategy,
        'rsi_mean_reversion': RSIMeanReversionStrategy,
        'momentum_breakout': MomentumBreakoutStrategy
    }
    
    @classmethod
    def create_strategy(cls, strategy_id: str, parameters: Dict[str, Any]) -> TradingStrategy:
        """Create a trading strategy instance."""
        if strategy_id not in cls.STRATEGIES:
            raise ValueError(f"Unknown strategy: {strategy_id}")
        
        strategy_class = cls.STRATEGIES[strategy_id]
        return strategy_class(parameters)
    
    @classmethod
    def get_available_strategies(cls) -> List[Dict[str, Any]]:
        """Get list of available strategies with their descriptions."""
        return [
            {
                'id': 'sma_crossover',
                'name': 'SMA Crossover',
                'description': 'Simple Moving Average crossover strategy',
                'category': 'trend_following',
                'parameters': {
                    'fast_period': {'type': 'int', 'default': 10, 'min': 5, 'max': 50},
                    'slow_period': {'type': 'int', 'default': 30, 'min': 20, 'max': 200}
                }
            },
            {
                'id': 'rsi_mean_reversion',
                'name': 'RSI Mean Reversion',
                'description': 'RSI-based mean reversion strategy',
                'category': 'mean_reversion',
                'parameters': {
                    'rsi_period': {'type': 'int', 'default': 14, 'min': 5, 'max': 30},
                    'oversold': {'type': 'float', 'default': 30, 'min': 10, 'max': 40},
                    'overbought': {'type': 'float', 'default': 70, 'min': 60, 'max': 90}
                }
            },
            {
                'id': 'momentum_breakout',
                'name': 'Momentum Breakout',
                'description': 'Momentum-based breakout strategy',
                'category': 'momentum',
                'parameters': {
                    'lookback_period': {'type': 'int', 'default': 20, 'min': 10, 'max': 50},
                    'breakout_threshold': {'type': 'float', 'default': 2.0, 'min': 1.0, 'max': 3.0}
                }
            }
        ]

"""
Core Backtesting Engine
Simulates trading strategy execution on historical data with comprehensive tracking
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal, ROUND_HALF_UP
from dataclasses import dataclass, field
from enum import Enum
import asyncpg

from ..infrastructure.database_config import DatabaseConfig
from ..shared.logging_config import setup_logging
from .historical_data_service import HistoricalDataService

logger = setup_logging("backtesting_engine")


class OrderSide(Enum):
    BUY = "BUY"
    SELL = "SELL"


class OrderStatus(Enum):
    PENDING = "PENDING"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"


@dataclass
class Position:
    """Represents a trading position."""
    symbol: str
    side: OrderSide
    quantity: Decimal
    entry_price: Decimal
    entry_time: datetime
    entry_reason: str = ""
    stop_loss: Optional[Decimal] = None
    take_profit: Optional[Decimal] = None


@dataclass
class Trade:
    """Represents a completed trade."""
    id: str
    symbol: str
    side: OrderSide
    entry_time: datetime
    exit_time: datetime
    entry_price: Decimal
    exit_price: Decimal
    quantity: Decimal
    commission: Decimal
    slippage: Decimal
    pnl: Decimal
    pnl_percent: Decimal
    duration_minutes: int
    entry_reason: str = ""
    exit_reason: str = ""


@dataclass
class Portfolio:
    """Represents portfolio state at a point in time."""
    timestamp: datetime
    cash: Decimal
    positions: Dict[str, Position] = field(default_factory=dict)
    total_value: Decimal = Decimal('0')
    unrealized_pnl: Decimal = Decimal('0')


@dataclass
class BacktestConfig:
    """Backtesting configuration."""
    strategy_id: str
    strategy_name: str
    strategy_parameters: Dict[str, Any]
    trading_pairs: List[str]
    timeframe: str
    start_date: datetime
    end_date: datetime
    initial_capital: Decimal
    commission: Decimal = Decimal('0.001')
    slippage: Decimal = Decimal('0.001')
    risk_management: Dict[str, Any] = field(default_factory=dict)
    benchmark: Optional[str] = None


class BacktestingEngine:
    """
    Core backtesting engine that simulates strategy execution.
    
    Features:
    - Position management and tracking
    - Order execution simulation with slippage and commission
    - Real-time portfolio valuation
    - Risk management integration
    - Comprehensive trade logging
    - Progress tracking for long-running backtests
    """
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.historical_data_service = HistoricalDataService()
        
        # Backtesting state
        self.current_backtest_id: Optional[str] = None
        self.portfolio: Optional[Portfolio] = None
        self.trades: List[Trade] = []
        self.equity_curve: List[Dict] = []
        self.progress_callback: Optional[callable] = None
        
        # Performance tracking
        self.peak_portfolio_value = Decimal('0')
        self.max_drawdown = Decimal('0')
        self.total_commission_paid = Decimal('0')
        self.total_slippage_paid = Decimal('0')
    
    async def connect(self) -> bool:
        """Connect to database and initialize services."""
        try:
            # Connect to database
            config = DatabaseConfig()
            self.pool = await asyncpg.create_pool(**config.connection_params)
            
            # Test connection
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            
            # Connect historical data service
            await self.historical_data_service.connect()
            
            logger.info("✅ Backtesting Engine connected successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect Backtesting Engine: {e}")
            return False
    
    async def disconnect(self):
        """Close database connections."""
        if self.pool:
            await self.pool.close()
        await self.historical_data_service.disconnect()
        logger.info("🔌 Backtesting Engine disconnected")
    
    async def run_backtest(
        self, 
        config: BacktestConfig, 
        strategy_func: callable,
        progress_callback: Optional[callable] = None
    ) -> str:
        """
        Run a complete backtest.
        
        Args:
            config: Backtesting configuration
            strategy_func: Strategy function that generates signals
            progress_callback: Optional callback for progress updates
            
        Returns:
            Backtest ID
        """
        if not self.pool:
            raise Exception("Engine not connected")
        
        # Generate backtest ID
        backtest_id = str(uuid.uuid4())
        self.current_backtest_id = backtest_id
        self.progress_callback = progress_callback
        
        logger.info(f"🚀 Starting backtest {backtest_id}")
        
        try:
            # Create backtest record
            await self._create_backtest_record(backtest_id, config)
            
            # Initialize portfolio
            self._initialize_portfolio(config)
            
            # Fetch historical data for all symbols
            historical_data = await self._fetch_all_historical_data(config)
            
            if not historical_data:
                raise Exception("No historical data available for backtesting")
            
            # Run simulation
            await self._run_simulation(config, historical_data, strategy_func)
            
            # Calculate final metrics
            results = await self._calculate_results(config)
            
            # Store results in database
            await self._store_results(backtest_id, results)
            
            # Mark backtest as completed
            await self._update_backtest_status(backtest_id, 'completed')
            
            logger.info(f"✅ Backtest {backtest_id} completed successfully")
            return backtest_id
            
        except Exception as e:
            logger.error(f"❌ Backtest {backtest_id} failed: {e}")
            await self._update_backtest_status(backtest_id, 'failed', str(e))
            raise
    
    async def _create_backtest_record(self, backtest_id: str, config: BacktestConfig):
        """Create initial backtest record in database."""
        try:
            async with self.pool.acquire() as conn:
                query = """
                    INSERT INTO backtesting_runs (
                        id, strategy_id, strategy_name, strategy_parameters,
                        trading_pairs, timeframe, start_date, end_date,
                        initial_capital, commission, slippage, risk_management,
                        benchmark, status, started_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
                """
                
                await conn.execute(
                    query, backtest_id, config.strategy_id, config.strategy_name,
                    config.strategy_parameters, config.trading_pairs, config.timeframe,
                    config.start_date, config.end_date, config.initial_capital,
                    config.commission, config.slippage, config.risk_management,
                    config.benchmark, 'running', datetime.utcnow()
                )
                
        except Exception as e:
            logger.error(f"❌ Error creating backtest record: {e}")
            raise
    
    def _initialize_portfolio(self, config: BacktestConfig):
        """Initialize portfolio with starting capital."""
        self.portfolio = Portfolio(
            timestamp=config.start_date,
            cash=config.initial_capital,
            total_value=config.initial_capital
        )
        self.trades = []
        self.equity_curve = []
        self.peak_portfolio_value = config.initial_capital
        self.max_drawdown = Decimal('0')
        self.total_commission_paid = Decimal('0')
        self.total_slippage_paid = Decimal('0')
        
        # Record initial equity point
        self.equity_curve.append({
            'timestamp': config.start_date,
            'portfolio_value': float(config.initial_capital),
            'drawdown': 0.0,
            'drawdown_percent': 0.0
        })
    
    async def _fetch_all_historical_data(self, config: BacktestConfig) -> Dict[str, List[Dict]]:
        """Fetch historical data for all trading pairs."""
        historical_data = {}
        
        for symbol in config.trading_pairs:
            try:
                data = await self.historical_data_service.get_data_for_backtesting(
                    symbol, config.timeframe, config.start_date, config.end_date
                )
                
                if data:
                    historical_data[symbol] = data
                    logger.info(f"📊 Loaded {len(data)} data points for {symbol}")
                else:
                    logger.warning(f"⚠️ No data available for {symbol}")
                    
            except Exception as e:
                logger.error(f"❌ Error fetching data for {symbol}: {e}")
        
        return historical_data
    
    async def _run_simulation(
        self, 
        config: BacktestConfig, 
        historical_data: Dict[str, List[Dict]], 
        strategy_func: callable
    ):
        """Run the main simulation loop."""
        # Get all timestamps and sort them
        all_timestamps = set()
        for symbol_data in historical_data.values():
            for candle in symbol_data:
                all_timestamps.add(candle['open_time'])
        
        sorted_timestamps = sorted(all_timestamps)
        total_steps = len(sorted_timestamps)
        
        logger.info(f"🔄 Running simulation with {total_steps} time steps")
        
        for i, timestamp in enumerate(sorted_timestamps):
            current_time = datetime.fromtimestamp(timestamp / 1000)
            
            # Update portfolio timestamp
            self.portfolio.timestamp = current_time
            
            # Get current market data for all symbols
            current_market_data = {}
            for symbol, symbol_data in historical_data.items():
                # Find the candle for this timestamp
                for candle in symbol_data:
                    if candle['open_time'] == timestamp:
                        current_market_data[symbol] = candle
                        break
            
            # Update portfolio valuation
            await self._update_portfolio_valuation(current_market_data)
            
            # Check for stop losses and take profits
            await self._check_exit_conditions(current_market_data, config)
            
            # Generate trading signals using strategy
            try:
                signals = await strategy_func(current_market_data, self.portfolio, config)
                
                # Execute signals
                if signals:
                    await self._execute_signals(signals, current_market_data, config)
                    
            except Exception as e:
                logger.error(f"❌ Strategy error at {current_time}: {e}")
            
            # Record equity curve point
            await self._record_equity_point(current_time)
            
            # Update progress
            progress = int((i + 1) / total_steps * 100)
            if self.progress_callback:
                await self.progress_callback(progress)
            
            # Update database progress every 10%
            if progress % 10 == 0:
                await self._update_backtest_progress(self.current_backtest_id, progress)
    
    async def _update_portfolio_valuation(self, current_market_data: Dict[str, Dict]):
        """Update portfolio valuation based on current market prices."""
        total_position_value = Decimal('0')
        unrealized_pnl = Decimal('0')
        
        for symbol, position in self.portfolio.positions.items():
            if symbol in current_market_data:
                current_price = Decimal(str(current_market_data[symbol]['close']))
                position_value = position.quantity * current_price
                total_position_value += position_value
                
                # Calculate unrealized PnL
                if position.side == OrderSide.BUY:
                    pnl = (current_price - position.entry_price) * position.quantity
                else:  # SELL (short position)
                    pnl = (position.entry_price - current_price) * position.quantity
                
                unrealized_pnl += pnl
        
        self.portfolio.total_value = self.portfolio.cash + total_position_value
        self.portfolio.unrealized_pnl = unrealized_pnl
        
        # Update peak value and drawdown
        if self.portfolio.total_value > self.peak_portfolio_value:
            self.peak_portfolio_value = self.portfolio.total_value
        
        current_drawdown = self.peak_portfolio_value - self.portfolio.total_value
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
    
    async def _check_exit_conditions(self, current_market_data: Dict[str, Dict], config: BacktestConfig):
        """Check and execute stop loss and take profit orders."""
        positions_to_close = []
        
        for symbol, position in self.portfolio.positions.items():
            if symbol not in current_market_data:
                continue
            
            current_price = Decimal(str(current_market_data[symbol]['close']))
            should_exit = False
            exit_reason = ""
            
            if position.side == OrderSide.BUY:
                # Long position checks
                if position.stop_loss and current_price <= position.stop_loss:
                    should_exit = True
                    exit_reason = "Stop Loss"
                elif position.take_profit and current_price >= position.take_profit:
                    should_exit = True
                    exit_reason = "Take Profit"
            else:
                # Short position checks
                if position.stop_loss and current_price >= position.stop_loss:
                    should_exit = True
                    exit_reason = "Stop Loss"
                elif position.take_profit and current_price <= position.take_profit:
                    should_exit = True
                    exit_reason = "Take Profit"
            
            if should_exit:
                positions_to_close.append((symbol, exit_reason))
        
        # Close positions
        for symbol, exit_reason in positions_to_close:
            await self._close_position(symbol, current_market_data[symbol], config, exit_reason)
    
    async def _execute_signals(self, signals: List[Dict], current_market_data: Dict[str, Dict], config: BacktestConfig):
        """Execute trading signals."""
        for signal in signals:
            try:
                symbol = signal['symbol']
                action = signal['action']  # 'BUY', 'SELL', 'CLOSE'
                
                if action == 'CLOSE' and symbol in self.portfolio.positions:
                    await self._close_position(symbol, current_market_data[symbol], config, signal.get('reason', 'Strategy Signal'))
                elif action in ['BUY', 'SELL']:
                    await self._open_position(signal, current_market_data[symbol], config)
                    
            except Exception as e:
                logger.error(f"❌ Error executing signal: {e}")
    
    async def _open_position(self, signal: Dict, market_data: Dict, config: BacktestConfig):
        """Open a new position based on signal."""
        symbol = signal['symbol']
        side = OrderSide(signal['action'])
        
        # Check if position already exists
        if symbol in self.portfolio.positions:
            logger.warning(f"⚠️ Position already exists for {symbol}")
            return
        
        # Calculate position size
        position_size = self._calculate_position_size(signal, config)
        if position_size <= 0:
            return
        
        # Get execution price with slippage
        base_price = Decimal(str(market_data['close']))
        execution_price = self._apply_slippage(base_price, side, config.slippage)
        
        # Calculate total cost including commission
        total_cost = position_size * execution_price
        commission = total_cost * config.commission
        
        # Check if we have enough cash
        required_cash = total_cost + commission
        if required_cash > self.portfolio.cash:
            logger.warning(f"⚠️ Insufficient cash for {symbol} position")
            return
        
        # Create position
        position = Position(
            symbol=symbol,
            side=side,
            quantity=position_size,
            entry_price=execution_price,
            entry_time=self.portfolio.timestamp,
            entry_reason=signal.get('reason', 'Strategy Signal'),
            stop_loss=signal.get('stop_loss'),
            take_profit=signal.get('take_profit')
        )
        
        # Update portfolio
        self.portfolio.positions[symbol] = position
        self.portfolio.cash -= required_cash
        self.total_commission_paid += commission
        self.total_slippage_paid += abs(execution_price - base_price) * position_size
        
        logger.debug(f"📈 Opened {side.value} position: {symbol} @ {execution_price}")
    
    async def _close_position(self, symbol: str, market_data: Dict, config: BacktestConfig, exit_reason: str):
        """Close an existing position."""
        if symbol not in self.portfolio.positions:
            return
        
        position = self.portfolio.positions[symbol]
        
        # Get execution price with slippage
        base_price = Decimal(str(market_data['close']))
        # Reverse slippage direction for closing
        opposite_side = OrderSide.SELL if position.side == OrderSide.BUY else OrderSide.BUY
        execution_price = self._apply_slippage(base_price, opposite_side, config.slippage)
        
        # Calculate proceeds and commission
        proceeds = position.quantity * execution_price
        commission = proceeds * config.commission
        net_proceeds = proceeds - commission
        
        # Calculate PnL
        if position.side == OrderSide.BUY:
            pnl = (execution_price - position.entry_price) * position.quantity - commission
        else:  # Short position
            pnl = (position.entry_price - execution_price) * position.quantity - commission
        
        pnl_percent = (pnl / (position.entry_price * position.quantity)) * 100
        
        # Create trade record
        duration = self.portfolio.timestamp - position.entry_time
        trade = Trade(
            id=str(uuid.uuid4()),
            symbol=symbol,
            side=position.side,
            entry_time=position.entry_time,
            exit_time=self.portfolio.timestamp,
            entry_price=position.entry_price,
            exit_price=execution_price,
            quantity=position.quantity,
            commission=commission,
            slippage=abs(execution_price - base_price) * position.quantity,
            pnl=pnl,
            pnl_percent=pnl_percent,
            duration_minutes=int(duration.total_seconds() / 60),
            entry_reason=position.entry_reason,
            exit_reason=exit_reason
        )
        
        # Update portfolio
        self.portfolio.cash += net_proceeds
        del self.portfolio.positions[symbol]
        self.trades.append(trade)
        self.total_commission_paid += commission
        self.total_slippage_paid += abs(execution_price - base_price) * position.quantity
        
        logger.debug(f"📉 Closed {position.side.value} position: {symbol} @ {execution_price}, PnL: {pnl}")
    
    def _calculate_position_size(self, signal: Dict, config: BacktestConfig) -> Decimal:
        """Calculate position size based on risk management rules."""
        risk_mgmt = config.risk_management
        position_sizing = risk_mgmt.get('positionSizing', 'percentage')
        max_position_size = Decimal(str(risk_mgmt.get('maxPositionSize', 10)))  # Default 10%
        
        if position_sizing == 'fixed':
            # Fixed dollar amount
            fixed_amount = Decimal(str(signal.get('position_size', 1000)))
            return min(fixed_amount, self.portfolio.cash * max_position_size / 100)
        
        elif position_sizing == 'percentage':
            # Percentage of portfolio
            percentage = Decimal(str(signal.get('position_size', max_position_size)))
            return self.portfolio.total_value * percentage / 100
        
        else:
            # Default to 5% of portfolio
            return self.portfolio.total_value * Decimal('0.05')
    
    def _apply_slippage(self, price: Decimal, side: OrderSide, slippage_rate: Decimal) -> Decimal:
        """Apply slippage to execution price."""
        if side == OrderSide.BUY:
            # Buy orders get worse price (higher)
            return price * (Decimal('1') + slippage_rate)
        else:
            # Sell orders get worse price (lower)
            return price * (Decimal('1') - slippage_rate)
    
    async def _record_equity_point(self, timestamp: datetime):
        """Record equity curve point."""
        drawdown = float(self.peak_portfolio_value - self.portfolio.total_value)
        drawdown_percent = (drawdown / float(self.peak_portfolio_value)) * 100 if self.peak_portfolio_value > 0 else 0
        
        equity_point = {
            'timestamp': timestamp,
            'portfolio_value': float(self.portfolio.total_value),
            'drawdown': drawdown,
            'drawdown_percent': drawdown_percent
        }
        
        self.equity_curve.append(equity_point)
    
    async def _update_backtest_progress(self, backtest_id: str, progress: int):
        """Update backtest progress in database."""
        try:
            async with self.pool.acquire() as conn:
                await conn.execute(
                    "UPDATE backtesting_runs SET progress = $1, updated_at = $2 WHERE id = $3",
                    progress, datetime.utcnow(), backtest_id
                )
        except Exception as e:
            logger.error(f"❌ Error updating progress: {e}")
    
    async def _update_backtest_status(self, backtest_id: str, status: str, error_message: str = None):
        """Update backtest status in database."""
        try:
            async with self.pool.acquire() as conn:
                if status == 'completed':
                    await conn.execute(
                        "UPDATE backtesting_runs SET status = $1, completed_at = $2, progress = 100, updated_at = $2 WHERE id = $3",
                        status, datetime.utcnow(), backtest_id
                    )
                else:
                    await conn.execute(
                        "UPDATE backtesting_runs SET status = $1, error_message = $2, updated_at = $3 WHERE id = $4",
                        status, error_message, datetime.utcnow(), backtest_id
                    )
        except Exception as e:
            logger.error(f"❌ Error updating status: {e}")
    
    async def _calculate_results(self, config: BacktestConfig) -> Dict:
        """Calculate comprehensive backtest results."""
        if not self.trades or not self.equity_curve:
            return self._get_empty_results()

        # Basic metrics
        total_trades = len(self.trades)
        winning_trades = len([t for t in self.trades if t.pnl > 0])
        losing_trades = total_trades - winning_trades

        # Returns
        initial_capital = float(config.initial_capital)
        final_value = float(self.portfolio.total_value)
        total_return = final_value - initial_capital
        total_return_percent = (total_return / initial_capital) * 100

        # Calculate annualized return
        days = (config.end_date - config.start_date).days
        years = days / 365.25
        annualized_return = ((final_value / initial_capital) ** (1 / years) - 1) * 100 if years > 0 else 0

        # Win/Loss metrics
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0

        winning_pnls = [float(t.pnl) for t in self.trades if t.pnl > 0]
        losing_pnls = [float(t.pnl) for t in self.trades if t.pnl < 0]

        average_win = sum(winning_pnls) / len(winning_pnls) if winning_pnls else 0
        average_loss = sum(losing_pnls) / len(losing_pnls) if losing_pnls else 0
        largest_win = max(winning_pnls) if winning_pnls else 0
        largest_loss = min(losing_pnls) if losing_pnls else 0

        # Profit factor
        gross_profit = sum(winning_pnls)
        gross_loss = abs(sum(losing_pnls))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

        # Drawdown metrics
        max_drawdown_value = float(self.max_drawdown)
        max_drawdown_percent = (max_drawdown_value / float(self.peak_portfolio_value)) * 100 if self.peak_portfolio_value > 0 else 0

        # Risk metrics
        returns = self._calculate_daily_returns()
        sharpe_ratio = self._calculate_sharpe_ratio(returns)
        sortino_ratio = self._calculate_sortino_ratio(returns)
        volatility = self._calculate_volatility(returns)

        # Additional metrics
        calmar_ratio = annualized_return / abs(max_drawdown_percent) if max_drawdown_percent != 0 else 0
        recovery_factor = total_return / max_drawdown_value if max_drawdown_value > 0 else 0
        payoff_ratio = abs(average_win / average_loss) if average_loss != 0 else 0
        expected_value = sum(float(t.pnl) for t in self.trades) / total_trades if total_trades > 0 else 0

        # Average trade duration
        durations = [t.duration_minutes for t in self.trades]
        average_trade_duration = sum(durations) / len(durations) / 60 if durations else 0  # Convert to hours

        return {
            'total_return': total_return,
            'total_return_percent': total_return_percent,
            'annualized_return': annualized_return,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'max_drawdown': max_drawdown_value,
            'max_drawdown_percent': max_drawdown_percent,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'average_win': average_win,
            'average_loss': average_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'average_trade_duration': average_trade_duration,
            'volatility': volatility,
            'calmar_ratio': calmar_ratio,
            'recovery_factor': recovery_factor,
            'payoff_ratio': payoff_ratio,
            'expected_value': expected_value
        }

    def _get_empty_results(self) -> Dict:
        """Return empty results structure."""
        return {
            'total_return': 0, 'total_return_percent': 0, 'annualized_return': 0,
            'sharpe_ratio': 0, 'sortino_ratio': 0, 'max_drawdown': 0,
            'max_drawdown_percent': 0, 'win_rate': 0, 'profit_factor': 0,
            'total_trades': 0, 'winning_trades': 0, 'losing_trades': 0,
            'average_win': 0, 'average_loss': 0, 'largest_win': 0,
            'largest_loss': 0, 'average_trade_duration': 0, 'volatility': 0,
            'calmar_ratio': 0, 'recovery_factor': 0, 'payoff_ratio': 0,
            'expected_value': 0
        }

    def _calculate_daily_returns(self) -> List[float]:
        """Calculate daily returns from equity curve."""
        if len(self.equity_curve) < 2:
            return []

        returns = []
        for i in range(1, len(self.equity_curve)):
            prev_value = self.equity_curve[i-1]['portfolio_value']
            curr_value = self.equity_curve[i]['portfolio_value']

            if prev_value > 0:
                daily_return = (curr_value - prev_value) / prev_value
                returns.append(daily_return)

        return returns

    def _calculate_sharpe_ratio(self, returns: List[float], risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio."""
        if not returns or len(returns) < 2:
            return 0

        import statistics

        mean_return = statistics.mean(returns)
        std_return = statistics.stdev(returns)

        if std_return == 0:
            return 0

        # Annualize the metrics
        annual_mean = mean_return * 252  # 252 trading days
        annual_std = std_return * (252 ** 0.5)

        return (annual_mean - risk_free_rate) / annual_std

    def _calculate_sortino_ratio(self, returns: List[float], risk_free_rate: float = 0.02) -> float:
        """Calculate Sortino ratio (downside deviation)."""
        if not returns or len(returns) < 2:
            return 0

        import statistics

        mean_return = statistics.mean(returns)
        downside_returns = [r for r in returns if r < 0]

        if not downside_returns:
            return float('inf') if mean_return > risk_free_rate else 0

        downside_std = statistics.stdev(downside_returns) if len(downside_returns) > 1 else 0

        if downside_std == 0:
            return 0

        # Annualize the metrics
        annual_mean = mean_return * 252
        annual_downside_std = downside_std * (252 ** 0.5)

        return (annual_mean - risk_free_rate) / annual_downside_std

    def _calculate_volatility(self, returns: List[float]) -> float:
        """Calculate annualized volatility."""
        if not returns or len(returns) < 2:
            return 0

        import statistics

        std_return = statistics.stdev(returns)
        return std_return * (252 ** 0.5) * 100  # Annualized percentage

    async def _store_results(self, backtest_id: str, results: Dict):
        """Store backtest results in database."""
        try:
            async with self.pool.acquire() as conn:
                # Store main results
                results_query = """
                    INSERT INTO backtesting_results (
                        backtest_id, total_return, total_return_percent, annualized_return,
                        sharpe_ratio, sortino_ratio, max_drawdown, max_drawdown_percent,
                        win_rate, profit_factor, total_trades, winning_trades, losing_trades,
                        average_win, average_loss, largest_win, largest_loss,
                        average_trade_duration, volatility, calmar_ratio, recovery_factor,
                        payoff_ratio, expected_value
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23)
                """

                await conn.execute(
                    results_query, backtest_id, results['total_return'], results['total_return_percent'],
                    results['annualized_return'], results['sharpe_ratio'], results['sortino_ratio'],
                    results['max_drawdown'], results['max_drawdown_percent'], results['win_rate'],
                    results['profit_factor'], results['total_trades'], results['winning_trades'],
                    results['losing_trades'], results['average_win'], results['average_loss'],
                    results['largest_win'], results['largest_loss'], results['average_trade_duration'],
                    results['volatility'], results['calmar_ratio'], results['recovery_factor'],
                    results['payoff_ratio'], results['expected_value']
                )

                # Store trades
                if self.trades:
                    trade_query = """
                        INSERT INTO backtesting_trades (
                            id, backtest_id, symbol, side, entry_time, exit_time,
                            entry_price, exit_price, quantity, commission, slippage,
                            pnl, pnl_percent, duration_minutes, entry_reason, exit_reason
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                    """

                    trade_values = []
                    for trade in self.trades:
                        trade_values.append((
                            trade.id, backtest_id, trade.symbol, trade.side.value,
                            trade.entry_time, trade.exit_time, trade.entry_price,
                            trade.exit_price, trade.quantity, trade.commission,
                            trade.slippage, trade.pnl, trade.pnl_percent,
                            trade.duration_minutes, trade.entry_reason, trade.exit_reason
                        ))

                    await conn.executemany(trade_query, trade_values)

                # Store equity curve
                if self.equity_curve:
                    equity_query = """
                        INSERT INTO backtesting_equity_curve (
                            backtest_id, timestamp, portfolio_value, drawdown, drawdown_percent
                        ) VALUES ($1, $2, $3, $4, $5)
                    """

                    equity_values = []
                    for point in self.equity_curve:
                        equity_values.append((
                            backtest_id, point['timestamp'], point['portfolio_value'],
                            point['drawdown'], point['drawdown_percent']
                        ))

                    await conn.executemany(equity_query, equity_values)

                # Store monthly returns
                monthly_returns = self._calculate_monthly_returns()
                if monthly_returns:
                    monthly_query = """
                        INSERT INTO backtesting_monthly_returns (
                            backtest_id, year, month, month_name, return_value
                        ) VALUES ($1, $2, $3, $4, $5)
                    """

                    await conn.executemany(monthly_query, monthly_returns)

        except Exception as e:
            logger.error(f"❌ Error storing results: {e}")
            raise

    def _calculate_monthly_returns(self) -> List[Tuple]:
        """Calculate monthly returns from equity curve."""
        if len(self.equity_curve) < 2:
            return []

        monthly_data = {}

        for point in self.equity_curve:
            timestamp = point['timestamp']
            value = point['portfolio_value']

            year_month = (timestamp.year, timestamp.month)

            if year_month not in monthly_data:
                monthly_data[year_month] = {'start': value, 'end': value}
            else:
                monthly_data[year_month]['end'] = value

        monthly_returns = []
        month_names = [
            'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
        ]

        for (year, month), data in monthly_data.items():
            if data['start'] > 0:
                return_pct = ((data['end'] - data['start']) / data['start']) * 100
                monthly_returns.append((
                    str(uuid.uuid4()), year, month, month_names[month-1], return_pct
                ))

        return monthly_returns

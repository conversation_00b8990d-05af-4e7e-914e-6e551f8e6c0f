"""
Backtesting History Management Service
Manages storage, retrieval, and analysis of historical backtesting runs
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
import asyncpg

from ..infrastructure.database_config import DatabaseConfig
from ..shared.logging_config import setup_logging

logger = setup_logging("backtesting_history")


class BacktestingHistoryService:
    """
    Service for managing backtesting history and analytics.
    
    Features:
    - Store and retrieve backtest runs
    - Filter and search backtests
    - Compare multiple backtests
    - Generate analytics and insights
    - Manage backtest lifecycle
    """
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
    
    async def connect(self) -> bool:
        """Connect to database."""
        try:
            config = DatabaseConfig()
            self.pool = await asyncpg.create_pool(**config.connection_params)
            
            # Test connection
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            
            logger.info("✅ Backtesting History Service connected successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect Backtesting History Service: {e}")
            return False
    
    async def disconnect(self):
        """Close database connection."""
        if self.pool:
            await self.pool.close()
        logger.info("🔌 Backtesting History Service disconnected")
    
    async def get_user_backtests(
        self,
        user_id: int,
        limit: int = 20,
        offset: int = 0,
        status_filter: Optional[str] = None,
        strategy_filter: Optional[str] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        sort_by: str = "created_at",
        sort_order: str = "DESC"
    ) -> Dict[str, Any]:
        """
        Get paginated list of user's backtests with filtering and sorting.
        
        Args:
            user_id: User identifier
            limit: Maximum number of results
            offset: Results offset for pagination
            status_filter: Filter by status ('completed', 'running', 'failed')
            strategy_filter: Filter by strategy ID
            date_from: Filter backtests created after this date
            date_to: Filter backtests created before this date
            sort_by: Sort field ('created_at', 'total_return', 'sharpe_ratio', etc.)
            sort_order: Sort order ('ASC' or 'DESC')
            
        Returns:
            Dictionary with backtests list and pagination info
        """
        if not self.pool:
            raise Exception("Service not connected")
        
        try:
            async with self.pool.acquire() as conn:
                # Build WHERE clause
                where_conditions = ["br.user_id = $1"]
                params = [user_id]
                param_count = 1
                
                if status_filter:
                    param_count += 1
                    where_conditions.append(f"br.status = ${param_count}")
                    params.append(status_filter)
                
                if strategy_filter:
                    param_count += 1
                    where_conditions.append(f"br.strategy_id = ${param_count}")
                    params.append(strategy_filter)
                
                if date_from:
                    param_count += 1
                    where_conditions.append(f"br.created_at >= ${param_count}")
                    params.append(date_from)
                
                if date_to:
                    param_count += 1
                    where_conditions.append(f"br.created_at <= ${param_count}")
                    params.append(date_to)
                
                where_clause = " AND ".join(where_conditions)
                
                # Validate sort field
                valid_sort_fields = [
                    'created_at', 'completed_at', 'strategy_name', 'initial_capital',
                    'total_return', 'total_return_percent', 'sharpe_ratio', 
                    'max_drawdown_percent', 'total_trades', 'win_rate'
                ]
                if sort_by not in valid_sort_fields:
                    sort_by = 'created_at'
                
                if sort_order.upper() not in ['ASC', 'DESC']:
                    sort_order = 'DESC'
                
                # Get total count
                count_query = f"""
                    SELECT COUNT(*) as total 
                    FROM backtesting_runs br 
                    WHERE {where_clause}
                """
                count_row = await conn.fetchrow(count_query, *params)
                total = count_row['total'] if count_row else 0
                
                # Get backtests with results
                param_count += 1
                limit_param = param_count
                param_count += 1
                offset_param = param_count
                
                query = f"""
                    SELECT br.*, bres.total_return, bres.total_return_percent, 
                           bres.sharpe_ratio, bres.sortino_ratio, bres.max_drawdown_percent, 
                           bres.win_rate, bres.total_trades, bres.profit_factor
                    FROM backtesting_runs br
                    LEFT JOIN backtesting_results bres ON br.id = bres.backtest_id
                    WHERE {where_clause}
                    ORDER BY {sort_by} {sort_order}
                    LIMIT ${limit_param} OFFSET ${offset_param}
                """
                
                params.extend([limit, offset])
                rows = await conn.fetch(query, *params)
                
                backtests = []
                for row in rows:
                    backtest = {
                        'id': str(row['id']),
                        'name': row['name'] or f"{row['strategy_name']} - {row['created_at'].strftime('%Y-%m-%d')}",
                        'strategy_id': row['strategy_id'],
                        'strategy_name': row['strategy_name'],
                        'strategy_parameters': row['strategy_parameters'],
                        'trading_pairs': row['trading_pairs'],
                        'timeframe': row['timeframe'],
                        'start_date': row['start_date'].isoformat(),
                        'end_date': row['end_date'].isoformat(),
                        'initial_capital': float(row['initial_capital']),
                        'commission': float(row['commission']),
                        'slippage': float(row['slippage']),
                        'status': row['status'],
                        'progress': row['progress'],
                        'created_at': row['created_at'].isoformat(),
                        'started_at': row['started_at'].isoformat() if row['started_at'] else None,
                        'completed_at': row['completed_at'].isoformat() if row['completed_at'] else None,
                        'error_message': row['error_message'],
                        # Results (may be None for incomplete backtests)
                        'total_return': float(row['total_return']) if row['total_return'] else None,
                        'total_return_percent': float(row['total_return_percent']) if row['total_return_percent'] else None,
                        'sharpe_ratio': float(row['sharpe_ratio']) if row['sharpe_ratio'] else None,
                        'sortino_ratio': float(row['sortino_ratio']) if row['sortino_ratio'] else None,
                        'max_drawdown_percent': float(row['max_drawdown_percent']) if row['max_drawdown_percent'] else None,
                        'win_rate': float(row['win_rate']) if row['win_rate'] else None,
                        'total_trades': row['total_trades'] if row['total_trades'] else None,
                        'profit_factor': float(row['profit_factor']) if row['profit_factor'] else None
                    }
                    backtests.append(backtest)
                
                return {
                    'backtests': backtests,
                    'total': total,
                    'limit': limit,
                    'offset': offset,
                    'has_more': offset + limit < total
                }
                
        except Exception as e:
            logger.error(f"Error getting user backtests: {e}")
            raise
    
    async def get_backtest_summary(self, backtest_id: str, user_id: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """Get summary information for a specific backtest."""
        if not self.pool:
            raise Exception("Service not connected")
        
        try:
            async with self.pool.acquire() as conn:
                query = """
                    SELECT br.*, bres.* FROM backtesting_runs br
                    LEFT JOIN backtesting_results bres ON br.id = bres.backtest_id
                    WHERE br.id = $1
                """
                params = [backtest_id]
                
                if user_id:
                    query += " AND br.user_id = $2"
                    params.append(user_id)
                
                row = await conn.fetchrow(query, *params)
                
                if not row:
                    return None
                
                return {
                    'id': str(row['id']),
                    'name': row['name'],
                    'strategy_name': row['strategy_name'],
                    'status': row['status'],
                    'created_at': row['created_at'].isoformat(),
                    'duration_minutes': self._calculate_duration_minutes(row),
                    'total_return_percent': float(row['total_return_percent']) if row['total_return_percent'] else None,
                    'sharpe_ratio': float(row['sharpe_ratio']) if row['sharpe_ratio'] else None,
                    'max_drawdown_percent': float(row['max_drawdown_percent']) if row['max_drawdown_percent'] else None,
                    'total_trades': row['total_trades'] if row['total_trades'] else None
                }
                
        except Exception as e:
            logger.error(f"Error getting backtest summary: {e}")
            return None
    
    async def compare_backtests(self, backtest_ids: List[str], user_id: Optional[int] = None) -> Dict[str, Any]:
        """Compare multiple backtests side by side."""
        if not self.pool:
            raise Exception("Service not connected")
        
        if len(backtest_ids) < 2:
            raise ValueError("At least 2 backtests required for comparison")
        
        try:
            async with self.pool.acquire() as conn:
                # Get backtest data
                placeholders = ', '.join([f'${i+1}' for i in range(len(backtest_ids))])
                query = f"""
                    SELECT br.*, bres.* FROM backtesting_runs br
                    LEFT JOIN backtesting_results bres ON br.id = bres.backtest_id
                    WHERE br.id IN ({placeholders})
                """
                params = backtest_ids
                
                if user_id:
                    query += f" AND br.user_id = ${len(backtest_ids) + 1}"
                    params.append(user_id)
                
                rows = await conn.fetch(query, *params)
                
                if len(rows) != len(backtest_ids):
                    raise ValueError("Some backtests not found or access denied")
                
                # Prepare comparison data
                comparison = {
                    'backtests': [],
                    'metrics_comparison': {},
                    'rankings': {}
                }
                
                metrics_to_compare = [
                    'total_return_percent', 'annualized_return', 'sharpe_ratio', 
                    'sortino_ratio', 'max_drawdown_percent', 'win_rate', 
                    'profit_factor', 'total_trades', 'volatility'
                ]
                
                # Collect data
                for row in rows:
                    backtest_data = {
                        'id': str(row['id']),
                        'name': row['name'] or f"{row['strategy_name']} - {row['created_at'].strftime('%Y-%m-%d')}",
                        'strategy_name': row['strategy_name'],
                        'status': row['status']
                    }
                    
                    # Add metrics
                    for metric in metrics_to_compare:
                        value = row.get(metric)
                        backtest_data[metric] = float(value) if value is not None else None
                    
                    comparison['backtests'].append(backtest_data)
                
                # Calculate rankings
                for metric in metrics_to_compare:
                    values = []
                    for bt in comparison['backtests']:
                        if bt[metric] is not None:
                            values.append((bt['id'], bt[metric]))
                    
                    if values:
                        # Sort by metric (higher is better for most metrics, except drawdown)
                        reverse = metric != 'max_drawdown_percent'
                        sorted_values = sorted(values, key=lambda x: x[1], reverse=reverse)
                        comparison['rankings'][metric] = [bt_id for bt_id, _ in sorted_values]
                
                return comparison
                
        except Exception as e:
            logger.error(f"Error comparing backtests: {e}")
            raise
    
    async def get_strategy_performance_stats(self, user_id: int) -> Dict[str, Any]:
        """Get performance statistics grouped by strategy."""
        if not self.pool:
            raise Exception("Service not connected")
        
        try:
            async with self.pool.acquire() as conn:
                query = """
                    SELECT br.strategy_id, br.strategy_name,
                           COUNT(*) as total_runs,
                           COUNT(CASE WHEN br.status = 'completed' THEN 1 END) as completed_runs,
                           AVG(bres.total_return_percent) as avg_return,
                           AVG(bres.sharpe_ratio) as avg_sharpe,
                           AVG(bres.max_drawdown_percent) as avg_drawdown,
                           AVG(bres.win_rate) as avg_win_rate,
                           MAX(bres.total_return_percent) as best_return,
                           MIN(bres.max_drawdown_percent) as worst_drawdown
                    FROM backtesting_runs br
                    LEFT JOIN backtesting_results bres ON br.id = bres.backtest_id
                    WHERE br.user_id = $1
                    GROUP BY br.strategy_id, br.strategy_name
                    ORDER BY completed_runs DESC, avg_return DESC
                """
                
                rows = await conn.fetch(query, user_id)
                
                strategies = []
                for row in rows:
                    strategy_stats = {
                        'strategy_id': row['strategy_id'],
                        'strategy_name': row['strategy_name'],
                        'total_runs': row['total_runs'],
                        'completed_runs': row['completed_runs'],
                        'success_rate': (row['completed_runs'] / row['total_runs']) * 100 if row['total_runs'] > 0 else 0,
                        'avg_return': float(row['avg_return']) if row['avg_return'] else None,
                        'avg_sharpe': float(row['avg_sharpe']) if row['avg_sharpe'] else None,
                        'avg_drawdown': float(row['avg_drawdown']) if row['avg_drawdown'] else None,
                        'avg_win_rate': float(row['avg_win_rate']) if row['avg_win_rate'] else None,
                        'best_return': float(row['best_return']) if row['best_return'] else None,
                        'worst_drawdown': float(row['worst_drawdown']) if row['worst_drawdown'] else None
                    }
                    strategies.append(strategy_stats)
                
                return {
                    'strategies': strategies,
                    'total_strategies': len(strategies)
                }
                
        except Exception as e:
            logger.error(f"Error getting strategy stats: {e}")
            raise
    
    async def delete_backtest(self, backtest_id: str, user_id: int) -> bool:
        """Delete a backtest and all associated data."""
        if not self.pool:
            raise Exception("Service not connected")
        
        try:
            async with self.pool.acquire() as conn:
                # Check ownership
                row = await conn.fetchrow(
                    "SELECT user_id FROM backtesting_runs WHERE id = $1",
                    backtest_id
                )
                
                if not row:
                    return False
                
                if row['user_id'] != user_id:
                    raise PermissionError("Access denied")
                
                # Delete backtest (cascade will handle related tables)
                result = await conn.execute(
                    "DELETE FROM backtesting_runs WHERE id = $1",
                    backtest_id
                )
                
                return result == "DELETE 1"
                
        except Exception as e:
            logger.error(f"Error deleting backtest: {e}")
            raise
    
    async def cleanup_old_backtests(self, days_old: int = 90, keep_completed: bool = True) -> int:
        """Clean up old backtests to save storage space."""
        if not self.pool:
            raise Exception("Service not connected")
        
        try:
            async with self.pool.acquire() as conn:
                cutoff_date = datetime.utcnow() - timedelta(days=days_old)
                
                if keep_completed:
                    # Only delete failed or cancelled backtests
                    query = """
                        DELETE FROM backtesting_runs 
                        WHERE created_at < $1 AND status IN ('failed', 'cancelled')
                    """
                else:
                    # Delete all old backtests
                    query = """
                        DELETE FROM backtesting_runs 
                        WHERE created_at < $1
                    """
                
                result = await conn.execute(query, cutoff_date)
                deleted_count = int(result.split()[-1]) if result.startswith("DELETE") else 0
                
                logger.info(f"Cleaned up {deleted_count} old backtests")
                return deleted_count
                
        except Exception as e:
            logger.error(f"Error cleaning up backtests: {e}")
            raise
    
    def _calculate_duration_minutes(self, row: Dict) -> Optional[int]:
        """Calculate backtest duration in minutes."""
        if row['started_at'] and row['completed_at']:
            duration = row['completed_at'] - row['started_at']
            return int(duration.total_seconds() / 60)
        return None
    
    async def get_user_statistics(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive statistics for a user's backtesting activity."""
        if not self.pool:
            raise Exception("Service not connected")
        
        try:
            async with self.pool.acquire() as conn:
                # Basic counts
                stats_query = """
                    SELECT 
                        COUNT(*) as total_backtests,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_backtests,
                        COUNT(CASE WHEN status = 'running' THEN 1 END) as running_backtests,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_backtests,
                        AVG(EXTRACT(EPOCH FROM (completed_at - started_at))/60) as avg_duration_minutes
                    FROM backtesting_runs 
                    WHERE user_id = $1
                """
                
                stats_row = await conn.fetchrow(stats_query, user_id)
                
                # Performance stats
                perf_query = """
                    SELECT 
                        AVG(bres.total_return_percent) as avg_return,
                        MAX(bres.total_return_percent) as best_return,
                        MIN(bres.total_return_percent) as worst_return,
                        AVG(bres.sharpe_ratio) as avg_sharpe,
                        MAX(bres.sharpe_ratio) as best_sharpe,
                        AVG(bres.max_drawdown_percent) as avg_drawdown,
                        MIN(bres.max_drawdown_percent) as worst_drawdown,
                        AVG(bres.win_rate) as avg_win_rate
                    FROM backtesting_runs br
                    JOIN backtesting_results bres ON br.id = bres.backtest_id
                    WHERE br.user_id = $1
                """
                
                perf_row = await conn.fetchrow(perf_query, user_id)
                
                return {
                    'total_backtests': stats_row['total_backtests'],
                    'completed_backtests': stats_row['completed_backtests'],
                    'running_backtests': stats_row['running_backtests'],
                    'failed_backtests': stats_row['failed_backtests'],
                    'success_rate': (stats_row['completed_backtests'] / stats_row['total_backtests']) * 100 if stats_row['total_backtests'] > 0 else 0,
                    'avg_duration_minutes': float(stats_row['avg_duration_minutes']) if stats_row['avg_duration_minutes'] else None,
                    'avg_return': float(perf_row['avg_return']) if perf_row and perf_row['avg_return'] else None,
                    'best_return': float(perf_row['best_return']) if perf_row and perf_row['best_return'] else None,
                    'worst_return': float(perf_row['worst_return']) if perf_row and perf_row['worst_return'] else None,
                    'avg_sharpe': float(perf_row['avg_sharpe']) if perf_row and perf_row['avg_sharpe'] else None,
                    'best_sharpe': float(perf_row['best_sharpe']) if perf_row and perf_row['best_sharpe'] else None,
                    'avg_drawdown': float(perf_row['avg_drawdown']) if perf_row and perf_row['avg_drawdown'] else None,
                    'worst_drawdown': float(perf_row['worst_drawdown']) if perf_row and perf_row['worst_drawdown'] else None,
                    'avg_win_rate': float(perf_row['avg_win_rate']) if perf_row and perf_row['avg_win_rate'] else None
                }
                
        except Exception as e:
            logger.error(f"Error getting user statistics: {e}")
            raise

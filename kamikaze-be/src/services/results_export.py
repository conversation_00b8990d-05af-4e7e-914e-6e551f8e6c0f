"""
Results Export and Visualization Service
Export backtesting results in various formats and prepare visualization data
"""

import csv
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from io import StringIO, BytesIO
import asyncpg

from ..infrastructure.database_config import DatabaseConfig
from ..shared.logging_config import setup_logging

logger = setup_logging("results_export")


class ResultsExportService:
    """
    Service for exporting backtesting results in various formats.
    
    Features:
    - Export to CSV, JSON, Excel
    - Generate visualization-ready data
    - Create performance reports
    - Export trade logs
    - Generate summary statistics
    """
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
    
    async def connect(self) -> bool:
        """Connect to database."""
        try:
            config = DatabaseConfig()
            self.pool = await asyncpg.create_pool(**config.connection_params)
            
            # Test connection
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            
            logger.info("✅ Results Export Service connected successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect Results Export Service: {e}")
            return False
    
    async def disconnect(self):
        """Close database connection."""
        if self.pool:
            await self.pool.close()
        logger.info("🔌 Results Export Service disconnected")
    
    async def export_backtest_results(
        self, 
        backtest_id: str, 
        format_type: str = "json",
        include_trades: bool = True,
        include_equity_curve: bool = True
    ) -> Dict[str, Any]:
        """
        Export complete backtest results in specified format.
        
        Args:
            backtest_id: Backtest identifier
            format_type: Export format ('json', 'csv', 'excel')
            include_trades: Include individual trades
            include_equity_curve: Include equity curve data
            
        Returns:
            Dictionary with export data and metadata
        """
        if not self.pool:
            raise Exception("Service not connected")
        
        # Get backtest data
        backtest_data = await self._get_complete_backtest_data(
            backtest_id, include_trades, include_equity_curve
        )
        
        if not backtest_data:
            raise ValueError(f"Backtest {backtest_id} not found")
        
        # Export based on format
        if format_type.lower() == "json":
            return await self._export_to_json(backtest_data)
        elif format_type.lower() == "csv":
            return await self._export_to_csv(backtest_data)
        elif format_type.lower() == "excel":
            return await self._export_to_excel(backtest_data)
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    async def _get_complete_backtest_data(
        self, 
        backtest_id: str, 
        include_trades: bool = True,
        include_equity_curve: bool = True
    ) -> Optional[Dict[str, Any]]:
        """Get complete backtest data from database."""
        try:
            async with self.pool.acquire() as conn:
                # Get main backtest info
                run_query = """
                    SELECT br.*, bres.* FROM backtesting_runs br
                    LEFT JOIN backtesting_results bres ON br.id = bres.backtest_id
                    WHERE br.id = $1
                """
                run_row = await conn.fetchrow(run_query, backtest_id)
                
                if not run_row:
                    return None
                
                backtest_data = {
                    'backtest_info': dict(run_row),
                    'trades': [],
                    'equity_curve': [],
                    'monthly_returns': []
                }
                
                # Get trades if requested
                if include_trades:
                    trade_rows = await conn.fetch(
                        "SELECT * FROM backtesting_trades WHERE backtest_id = $1 ORDER BY entry_time",
                        backtest_id
                    )
                    backtest_data['trades'] = [dict(row) for row in trade_rows]
                
                # Get equity curve if requested
                if include_equity_curve:
                    equity_rows = await conn.fetch(
                        "SELECT * FROM backtesting_equity_curve WHERE backtest_id = $1 ORDER BY timestamp",
                        backtest_id
                    )
                    backtest_data['equity_curve'] = [dict(row) for row in equity_rows]
                
                # Get monthly returns
                monthly_rows = await conn.fetch(
                    "SELECT * FROM backtesting_monthly_returns WHERE backtest_id = $1 ORDER BY year, month",
                    backtest_id
                )
                backtest_data['monthly_returns'] = [dict(row) for row in monthly_rows]
                
                return backtest_data
                
        except Exception as e:
            logger.error(f"Error getting backtest data: {e}")
            return None
    
    async def _export_to_json(self, backtest_data: Dict[str, Any]) -> Dict[str, Any]:
        """Export backtest data to JSON format."""
        # Convert datetime objects to ISO strings
        def serialize_datetime(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            elif hasattr(obj, '__dict__'):
                return {k: serialize_datetime(v) for k, v in obj.__dict__.items()}
            elif isinstance(obj, dict):
                return {k: serialize_datetime(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [serialize_datetime(item) for item in obj]
            else:
                return obj
        
        serialized_data = serialize_datetime(backtest_data)
        
        return {
            'format': 'json',
            'data': serialized_data,
            'content_type': 'application/json',
            'filename': f"backtest_{backtest_data['backtest_info']['id']}.json"
        }
    
    async def _export_to_csv(self, backtest_data: Dict[str, Any]) -> Dict[str, Any]:
        """Export backtest data to CSV format (multiple files)."""
        csv_files = {}
        
        # Export summary
        summary_data = self._prepare_summary_data(backtest_data)
        summary_csv = StringIO()
        summary_writer = csv.writer(summary_csv)
        
        # Write summary headers and data
        for key, value in summary_data.items():
            summary_writer.writerow([key, value])
        
        csv_files['summary.csv'] = summary_csv.getvalue()
        
        # Export trades
        if backtest_data['trades']:
            trades_csv = StringIO()
            trades_writer = csv.DictWriter(
                trades_csv, 
                fieldnames=list(backtest_data['trades'][0].keys())
            )
            trades_writer.writeheader()
            trades_writer.writerows(backtest_data['trades'])
            csv_files['trades.csv'] = trades_csv.getvalue()
        
        # Export equity curve
        if backtest_data['equity_curve']:
            equity_csv = StringIO()
            equity_writer = csv.DictWriter(
                equity_csv,
                fieldnames=list(backtest_data['equity_curve'][0].keys())
            )
            equity_writer.writeheader()
            equity_writer.writerows(backtest_data['equity_curve'])
            csv_files['equity_curve.csv'] = equity_csv.getvalue()
        
        # Export monthly returns
        if backtest_data['monthly_returns']:
            monthly_csv = StringIO()
            monthly_writer = csv.DictWriter(
                monthly_csv,
                fieldnames=list(backtest_data['monthly_returns'][0].keys())
            )
            monthly_writer.writeheader()
            monthly_writer.writerows(backtest_data['monthly_returns'])
            csv_files['monthly_returns.csv'] = monthly_csv.getvalue()
        
        return {
            'format': 'csv',
            'data': csv_files,
            'content_type': 'application/zip',
            'filename': f"backtest_{backtest_data['backtest_info']['id']}_csv.zip"
        }
    
    async def _export_to_excel(self, backtest_data: Dict[str, Any]) -> Dict[str, Any]:
        """Export backtest data to Excel format."""
        try:
            import pandas as pd
            from io import BytesIO
            
            # Create Excel writer
            excel_buffer = BytesIO()
            
            with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                # Summary sheet
                summary_data = self._prepare_summary_data(backtest_data)
                summary_df = pd.DataFrame(list(summary_data.items()), columns=['Metric', 'Value'])
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
                
                # Trades sheet
                if backtest_data['trades']:
                    trades_df = pd.DataFrame(backtest_data['trades'])
                    trades_df.to_excel(writer, sheet_name='Trades', index=False)
                
                # Equity curve sheet
                if backtest_data['equity_curve']:
                    equity_df = pd.DataFrame(backtest_data['equity_curve'])
                    equity_df.to_excel(writer, sheet_name='Equity Curve', index=False)
                
                # Monthly returns sheet
                if backtest_data['monthly_returns']:
                    monthly_df = pd.DataFrame(backtest_data['monthly_returns'])
                    monthly_df.to_excel(writer, sheet_name='Monthly Returns', index=False)
            
            excel_buffer.seek(0)
            
            return {
                'format': 'excel',
                'data': excel_buffer.getvalue(),
                'content_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'filename': f"backtest_{backtest_data['backtest_info']['id']}.xlsx"
            }
            
        except ImportError:
            raise Exception("pandas and openpyxl are required for Excel export")
    
    def _prepare_summary_data(self, backtest_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare summary data for export."""
        info = backtest_data['backtest_info']
        
        summary = {
            'Backtest ID': str(info.get('id', '')),
            'Strategy': info.get('strategy_name', ''),
            'Trading Pairs': ', '.join(info.get('trading_pairs', [])),
            'Timeframe': info.get('timeframe', ''),
            'Start Date': info.get('start_date', ''),
            'End Date': info.get('end_date', ''),
            'Initial Capital': info.get('initial_capital', 0),
            'Commission': info.get('commission', 0),
            'Slippage': info.get('slippage', 0),
            'Status': info.get('status', ''),
            'Total Return': info.get('total_return', 0),
            'Total Return %': info.get('total_return_percent', 0),
            'Annualized Return %': info.get('annualized_return', 0),
            'Sharpe Ratio': info.get('sharpe_ratio', 0),
            'Sortino Ratio': info.get('sortino_ratio', 0),
            'Max Drawdown': info.get('max_drawdown', 0),
            'Max Drawdown %': info.get('max_drawdown_percent', 0),
            'Win Rate %': info.get('win_rate', 0),
            'Profit Factor': info.get('profit_factor', 0),
            'Total Trades': info.get('total_trades', 0),
            'Winning Trades': info.get('winning_trades', 0),
            'Losing Trades': info.get('losing_trades', 0),
            'Average Win': info.get('average_win', 0),
            'Average Loss': info.get('average_loss', 0),
            'Largest Win': info.get('largest_win', 0),
            'Largest Loss': info.get('largest_loss', 0),
            'Average Trade Duration (hours)': info.get('average_trade_duration', 0),
            'Volatility %': info.get('volatility', 0),
            'Calmar Ratio': info.get('calmar_ratio', 0),
            'Recovery Factor': info.get('recovery_factor', 0),
            'Payoff Ratio': info.get('payoff_ratio', 0),
            'Expected Value': info.get('expected_value', 0)
        }
        
        return summary
    
    async def prepare_visualization_data(self, backtest_id: str) -> Dict[str, Any]:
        """Prepare data optimized for frontend visualization."""
        backtest_data = await self._get_complete_backtest_data(backtest_id, True, True)
        
        if not backtest_data:
            raise ValueError(f"Backtest {backtest_id} not found")
        
        # Prepare equity curve for charting
        equity_chart_data = []
        for point in backtest_data['equity_curve']:
            equity_chart_data.append({
                'timestamp': point['timestamp'].isoformat(),
                'value': float(point['portfolio_value']),
                'drawdown': float(point['drawdown']),
                'drawdown_percent': float(point['drawdown_percent'])
            })
        
        # Prepare trade distribution data
        trades = backtest_data['trades']
        trade_distribution = {
            'by_symbol': {},
            'by_hour': {},
            'by_day_of_week': {},
            'pnl_distribution': []
        }
        
        for trade in trades:
            # By symbol
            symbol = trade['symbol']
            if symbol not in trade_distribution['by_symbol']:
                trade_distribution['by_symbol'][symbol] = {'count': 0, 'total_pnl': 0}
            trade_distribution['by_symbol'][symbol]['count'] += 1
            trade_distribution['by_symbol'][symbol]['total_pnl'] += float(trade.get('pnl', 0))
            
            # PnL distribution
            trade_distribution['pnl_distribution'].append(float(trade.get('pnl', 0)))
        
        # Prepare monthly returns for heatmap
        monthly_heatmap = {}
        for month_data in backtest_data['monthly_returns']:
            year = month_data['year']
            month = month_data['month']
            if year not in monthly_heatmap:
                monthly_heatmap[year] = {}
            monthly_heatmap[year][month] = float(month_data['return_value'])
        
        return {
            'equity_curve': equity_chart_data,
            'trade_distribution': trade_distribution,
            'monthly_heatmap': monthly_heatmap,
            'summary_metrics': self._prepare_summary_data(backtest_data),
            'trade_count': len(trades),
            'equity_points': len(equity_chart_data)
        }
    
    async def generate_performance_report(self, backtest_id: str) -> str:
        """Generate a comprehensive performance report in markdown format."""
        backtest_data = await self._get_complete_backtest_data(backtest_id, True, True)
        
        if not backtest_data:
            raise ValueError(f"Backtest {backtest_id} not found")
        
        info = backtest_data['backtest_info']
        
        report = f"""# Backtesting Performance Report

## Backtest Configuration
- **Strategy**: {info.get('strategy_name', 'N/A')}
- **Trading Pairs**: {', '.join(info.get('trading_pairs', []))}
- **Timeframe**: {info.get('timeframe', 'N/A')}
- **Period**: {info.get('start_date', 'N/A')} to {info.get('end_date', 'N/A')}
- **Initial Capital**: ${info.get('initial_capital', 0):,.2f}

## Performance Summary
- **Total Return**: ${info.get('total_return', 0):,.2f} ({info.get('total_return_percent', 0):.2f}%)
- **Annualized Return**: {info.get('annualized_return', 0):.2f}%
- **Sharpe Ratio**: {info.get('sharpe_ratio', 0):.3f}
- **Sortino Ratio**: {info.get('sortino_ratio', 0):.3f}
- **Maximum Drawdown**: ${info.get('max_drawdown', 0):,.2f} ({info.get('max_drawdown_percent', 0):.2f}%)

## Trading Statistics
- **Total Trades**: {info.get('total_trades', 0)}
- **Win Rate**: {info.get('win_rate', 0):.2f}%
- **Profit Factor**: {info.get('profit_factor', 0):.3f}
- **Average Win**: ${info.get('average_win', 0):,.2f}
- **Average Loss**: ${info.get('average_loss', 0):,.2f}
- **Largest Win**: ${info.get('largest_win', 0):,.2f}
- **Largest Loss**: ${info.get('largest_loss', 0):,.2f}

## Risk Metrics
- **Volatility**: {info.get('volatility', 0):.2f}%
- **Calmar Ratio**: {info.get('calmar_ratio', 0):.3f}
- **Recovery Factor**: {info.get('recovery_factor', 0):.3f}
- **Expected Value per Trade**: ${info.get('expected_value', 0):,.2f}

---
*Report generated on {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC*
"""
        
        return report

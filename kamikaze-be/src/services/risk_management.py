"""
Risk Management System
Comprehensive risk management for backtesting and live trading
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime, timedelta
from enum import Enum
import statistics

from ..shared.logging_config import setup_logging

logger = setup_logging("risk_management")


class PositionSizingMethod(Enum):
    FIXED = "fixed"
    PERCENTAGE = "percentage"
    KELLY = "kelly"
    VOLATILITY = "volatility"
    RISK_PARITY = "risk_parity"


class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


class RiskManager:
    """
    Comprehensive risk management system for trading strategies.
    
    Features:
    - Position sizing algorithms
    - Portfolio risk limits
    - Drawdown protection
    - Correlation analysis
    - Dynamic risk adjustment
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.position_sizing_method = PositionSizingMethod(
            config.get('positionSizing', 'percentage')
        )
        self.max_position_size = Decimal(str(config.get('maxPositionSize', 10)))  # %
        self.max_portfolio_risk = Decimal(str(config.get('maxPortfolioRisk', 20)))  # %
        self.max_drawdown_limit = Decimal(str(config.get('maxDrawdown', 20)))  # %
        self.max_correlation = config.get('maxCorrelation', 0.7)
        self.risk_free_rate = config.get('riskFreeRate', 0.02)
        
        # Dynamic risk adjustment
        self.enable_dynamic_sizing = config.get('enableDynamicSizing', True)
        self.volatility_lookback = config.get('volatilityLookback', 20)
        
        # Risk tracking
        self.position_history = []
        self.drawdown_history = []
        self.volatility_cache = {}
    
    def calculate_position_size(
        self,
        signal: Dict[str, Any],
        portfolio: Any,
        market_data: Dict[str, Dict],
        price_history: Optional[Dict[str, List[float]]] = None
    ) -> Decimal:
        """
        Calculate optimal position size based on risk management rules.
        
        Args:
            signal: Trading signal with symbol, action, confidence, etc.
            portfolio: Current portfolio state
            market_data: Current market data
            price_history: Historical price data for volatility calculation
            
        Returns:
            Position size in base currency
        """
        symbol = signal['symbol']
        
        # Base position size calculation
        if self.position_sizing_method == PositionSizingMethod.FIXED:
            base_size = self._calculate_fixed_size(signal, portfolio)
        elif self.position_sizing_method == PositionSizingMethod.PERCENTAGE:
            base_size = self._calculate_percentage_size(signal, portfolio)
        elif self.position_sizing_method == PositionSizingMethod.KELLY:
            base_size = self._calculate_kelly_size(signal, portfolio, price_history)
        elif self.position_sizing_method == PositionSizingMethod.VOLATILITY:
            base_size = self._calculate_volatility_size(signal, portfolio, market_data, price_history)
        else:
            base_size = self._calculate_percentage_size(signal, portfolio)
        
        # Apply risk limits
        size_after_limits = self._apply_risk_limits(base_size, signal, portfolio, market_data)
        
        # Apply correlation limits
        final_size = self._apply_correlation_limits(size_after_limits, signal, portfolio, market_data)
        
        # Apply dynamic adjustments
        if self.enable_dynamic_sizing:
            final_size = self._apply_dynamic_adjustments(final_size, signal, portfolio)
        
        return max(Decimal('0'), final_size)
    
    def _calculate_fixed_size(self, signal: Dict[str, Any], portfolio: Any) -> Decimal:
        """Calculate fixed dollar amount position size."""
        fixed_amount = Decimal(str(signal.get('position_size', 1000)))
        return min(fixed_amount, portfolio.cash)
    
    def _calculate_percentage_size(self, signal: Dict[str, Any], portfolio: Any) -> Decimal:
        """Calculate percentage-based position size."""
        percentage = Decimal(str(signal.get('position_size', self.max_position_size)))
        percentage = min(percentage, self.max_position_size)
        
        return portfolio.total_value * percentage / 100
    
    def _calculate_kelly_size(
        self, 
        signal: Dict[str, Any], 
        portfolio: Any, 
        price_history: Optional[Dict[str, List[float]]]
    ) -> Decimal:
        """Calculate Kelly Criterion position size."""
        symbol = signal['symbol']
        
        if not price_history or symbol not in price_history:
            # Fallback to percentage sizing
            return self._calculate_percentage_size(signal, portfolio)
        
        prices = price_history[symbol]
        if len(prices) < 30:  # Need sufficient history
            return self._calculate_percentage_size(signal, portfolio)
        
        # Calculate historical win rate and average win/loss
        returns = []
        for i in range(1, len(prices)):
            ret = (prices[i] - prices[i-1]) / prices[i-1]
            returns.append(ret)
        
        positive_returns = [r for r in returns if r > 0]
        negative_returns = [r for r in returns if r < 0]
        
        if not positive_returns or not negative_returns:
            return self._calculate_percentage_size(signal, portfolio)
        
        win_rate = len(positive_returns) / len(returns)
        avg_win = statistics.mean(positive_returns)
        avg_loss = abs(statistics.mean(negative_returns))
        
        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_rate, q = 1 - win_rate
        if avg_loss == 0:
            return self._calculate_percentage_size(signal, portfolio)
        
        b = avg_win / avg_loss
        p = win_rate
        q = 1 - win_rate
        
        kelly_fraction = (b * p - q) / b
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%
        
        # Adjust by signal confidence
        confidence = signal.get('confidence', 0.5)
        adjusted_fraction = kelly_fraction * confidence
        
        return portfolio.total_value * Decimal(str(adjusted_fraction))
    
    def _calculate_volatility_size(
        self,
        signal: Dict[str, Any],
        portfolio: Any,
        market_data: Dict[str, Dict],
        price_history: Optional[Dict[str, List[float]]]
    ) -> Decimal:
        """Calculate volatility-adjusted position size."""
        symbol = signal['symbol']
        
        # Calculate volatility
        volatility = self._get_volatility(symbol, price_history)
        
        if volatility == 0:
            return self._calculate_percentage_size(signal, portfolio)
        
        # Target volatility (e.g., 2% per position)
        target_volatility = 0.02
        
        # Inverse volatility sizing
        vol_adjustment = target_volatility / volatility
        vol_adjustment = min(vol_adjustment, 2.0)  # Cap adjustment
        
        base_percentage = min(self.max_position_size, Decimal('10'))
        adjusted_percentage = base_percentage * Decimal(str(vol_adjustment))
        
        return portfolio.total_value * adjusted_percentage / 100
    
    def _get_volatility(self, symbol: str, price_history: Optional[Dict[str, List[float]]]) -> float:
        """Calculate volatility for a symbol."""
        if not price_history or symbol not in price_history:
            return 0.15  # Default volatility assumption
        
        prices = price_history[symbol]
        if len(prices) < self.volatility_lookback:
            return 0.15
        
        # Calculate returns
        returns = []
        recent_prices = prices[-self.volatility_lookback:]
        for i in range(1, len(recent_prices)):
            ret = (recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1]
            returns.append(ret)
        
        if len(returns) < 2:
            return 0.15
        
        # Annualized volatility
        volatility = statistics.stdev(returns) * (252 ** 0.5)
        return max(0.01, volatility)  # Minimum 1% volatility
    
    def _apply_risk_limits(
        self,
        position_size: Decimal,
        signal: Dict[str, Any],
        portfolio: Any,
        market_data: Dict[str, Dict]
    ) -> Decimal:
        """Apply various risk limits to position size."""
        symbol = signal['symbol']
        
        # Maximum position size limit
        max_position_value = portfolio.total_value * self.max_position_size / 100
        position_size = min(position_size, max_position_value)
        
        # Cash availability check
        position_size = min(position_size, portfolio.cash)
        
        # Portfolio concentration limit
        current_position_value = Decimal('0')
        if symbol in portfolio.positions:
            current_price = Decimal(str(market_data[symbol]['close']))
            current_position_value = portfolio.positions[symbol].quantity * current_price
        
        total_symbol_exposure = current_position_value + position_size
        max_symbol_exposure = portfolio.total_value * self.max_position_size / 100
        
        if total_symbol_exposure > max_symbol_exposure:
            position_size = max(Decimal('0'), max_symbol_exposure - current_position_value)
        
        return position_size
    
    def _apply_correlation_limits(
        self,
        position_size: Decimal,
        signal: Dict[str, Any],
        portfolio: Any,
        market_data: Dict[str, Dict]
    ) -> Decimal:
        """Apply correlation-based position limits."""
        # Simplified correlation check
        # In a full implementation, this would calculate correlations between assets
        
        symbol = signal['symbol']
        
        # Count similar positions (simplified by checking if symbols start with same letters)
        similar_positions = 0
        for pos_symbol in portfolio.positions.keys():
            if pos_symbol[:3] == symbol[:3]:  # Rough similarity check
                similar_positions += 1
        
        # Reduce position size if too many correlated positions
        if similar_positions >= 3:
            correlation_adjustment = Decimal('0.5')  # 50% reduction
            position_size *= correlation_adjustment
        
        return position_size
    
    def _apply_dynamic_adjustments(
        self,
        position_size: Decimal,
        signal: Dict[str, Any],
        portfolio: Any
    ) -> Decimal:
        """Apply dynamic risk adjustments based on recent performance."""
        # Adjust based on recent drawdown
        if hasattr(portfolio, 'total_value') and hasattr(portfolio, 'cash'):
            # Simple drawdown estimation
            if len(self.drawdown_history) > 0:
                recent_drawdown = max(self.drawdown_history[-10:]) if len(self.drawdown_history) >= 10 else 0
                
                if recent_drawdown > float(self.max_drawdown_limit) * 0.5:
                    # Reduce position size during significant drawdown
                    drawdown_adjustment = Decimal('0.7')
                    position_size *= drawdown_adjustment
        
        # Adjust based on signal confidence
        confidence = signal.get('confidence', 0.5)
        confidence_adjustment = Decimal(str(max(0.3, min(1.2, confidence))))
        position_size *= confidence_adjustment
        
        return position_size
    
    def check_portfolio_risk(self, portfolio: Any, market_data: Dict[str, Dict]) -> Dict[str, Any]:
        """Check overall portfolio risk metrics."""
        risk_metrics = {
            'risk_level': RiskLevel.LOW,
            'warnings': [],
            'recommendations': [],
            'portfolio_var': 0,
            'concentration_risk': 0,
            'leverage': 0
        }
        
        if not portfolio.positions:
            return risk_metrics
        
        # Calculate portfolio concentration
        total_position_value = Decimal('0')
        largest_position = Decimal('0')
        
        for symbol, position in portfolio.positions.items():
            if symbol in market_data:
                current_price = Decimal(str(market_data[symbol]['close']))
                position_value = position.quantity * current_price
                total_position_value += position_value
                
                if position_value > largest_position:
                    largest_position = position_value
        
        # Concentration risk
        if total_position_value > 0:
            concentration = float(largest_position / total_position_value) * 100
            risk_metrics['concentration_risk'] = concentration
            
            if concentration > 40:
                risk_metrics['warnings'].append(f"High concentration risk: {concentration:.1f}%")
                risk_metrics['risk_level'] = RiskLevel.HIGH
        
        # Portfolio utilization
        portfolio_utilization = float(total_position_value / portfolio.total_value) * 100
        
        if portfolio_utilization > 90:
            risk_metrics['warnings'].append(f"High portfolio utilization: {portfolio_utilization:.1f}%")
            risk_metrics['risk_level'] = RiskLevel.HIGH
        elif portfolio_utilization > 70:
            risk_metrics['risk_level'] = RiskLevel.MEDIUM
        
        # Add recommendations
        if risk_metrics['risk_level'] == RiskLevel.HIGH:
            risk_metrics['recommendations'].append("Consider reducing position sizes")
            risk_metrics['recommendations'].append("Diversify across more assets")
        
        return risk_metrics
    
    def should_stop_trading(self, portfolio: Any, initial_capital: Decimal) -> Tuple[bool, str]:
        """Check if trading should be stopped due to risk limits."""
        if portfolio.total_value <= 0:
            return True, "Portfolio value is zero or negative"
        
        # Maximum drawdown check
        drawdown_percent = float((initial_capital - portfolio.total_value) / initial_capital * 100)
        
        if drawdown_percent >= float(self.max_drawdown_limit):
            return True, f"Maximum drawdown limit reached: {drawdown_percent:.2f}%"
        
        # Minimum capital check
        min_capital_threshold = initial_capital * Decimal('0.1')  # 10% of initial
        if portfolio.total_value < min_capital_threshold:
            return True, f"Portfolio below minimum threshold: {float(portfolio.total_value):.2f}"
        
        return False, ""
    
    def update_risk_metrics(self, portfolio: Any, initial_capital: Decimal):
        """Update risk tracking metrics."""
        if portfolio.total_value > 0:
            drawdown_percent = float((initial_capital - portfolio.total_value) / initial_capital * 100)
            self.drawdown_history.append(max(0, drawdown_percent))
            
            # Keep only recent history
            if len(self.drawdown_history) > 100:
                self.drawdown_history = self.drawdown_history[-100:]
    
    def get_risk_summary(self, portfolio: Any, initial_capital: Decimal) -> Dict[str, Any]:
        """Get comprehensive risk summary."""
        current_drawdown = 0
        if initial_capital > 0:
            current_drawdown = float((initial_capital - portfolio.total_value) / initial_capital * 100)
        
        max_historical_drawdown = max(self.drawdown_history) if self.drawdown_history else 0
        
        return {
            'current_drawdown_percent': max(0, current_drawdown),
            'max_drawdown_percent': max_historical_drawdown,
            'drawdown_limit_percent': float(self.max_drawdown_limit),
            'portfolio_utilization_percent': float(
                sum(pos.quantity * Decimal('100') for pos in portfolio.positions.values()) / portfolio.total_value * 100
            ) if portfolio.total_value > 0 else 0,
            'cash_percentage': float(portfolio.cash / portfolio.total_value * 100) if portfolio.total_value > 0 else 0,
            'number_of_positions': len(portfolio.positions),
            'risk_level': self._assess_overall_risk_level(current_drawdown, len(portfolio.positions))
        }
    
    def _assess_overall_risk_level(self, current_drawdown: float, num_positions: int) -> str:
        """Assess overall risk level."""
        if current_drawdown > float(self.max_drawdown_limit) * 0.8:
            return RiskLevel.EXTREME.value
        elif current_drawdown > float(self.max_drawdown_limit) * 0.5:
            return RiskLevel.HIGH.value
        elif num_positions > 10 or current_drawdown > 10:
            return RiskLevel.MEDIUM.value
        else:
            return RiskLevel.LOW.value

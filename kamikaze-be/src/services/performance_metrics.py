"""
Performance Metrics Calculator
Comprehensive quantitative finance metrics for backtesting results
"""

import logging
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from decimal import Decimal
import math

from ..shared.logging_config import setup_logging

logger = setup_logging("performance_metrics")


class PerformanceMetrics:
    """
    Comprehensive performance metrics calculator for trading strategies.
    
    Implements industry-standard quantitative finance metrics including:
    - Return metrics (total, annualized, CAGR)
    - Risk metrics (Sharpe, Sortino, Calmar ratios)
    - Drawdown analysis
    - Trade statistics
    - Risk-adjusted returns
    """
    
    @staticmethod
    def calculate_returns(
        initial_value: float, 
        final_value: float, 
        start_date: datetime, 
        end_date: datetime
    ) -> Dict[str, float]:
        """Calculate various return metrics."""
        total_return = final_value - initial_value
        total_return_percent = (total_return / initial_value) * 100 if initial_value > 0 else 0
        
        # Calculate time period
        days = (end_date - start_date).days
        years = days / 365.25
        
        # Annualized return (CAGR)
        if years > 0 and initial_value > 0:
            cagr = ((final_value / initial_value) ** (1 / years) - 1) * 100
        else:
            cagr = 0
        
        return {
            'total_return': total_return,
            'total_return_percent': total_return_percent,
            'annualized_return': cagr,
            'period_days': days,
            'period_years': years
        }
    
    @staticmethod
    def calculate_sharpe_ratio(
        returns: List[float], 
        risk_free_rate: float = 0.02,
        periods_per_year: int = 252
    ) -> float:
        """
        Calculate Sharpe ratio.
        
        Args:
            returns: List of periodic returns
            risk_free_rate: Annual risk-free rate (default 2%)
            periods_per_year: Number of periods per year (252 for daily)
        """
        if not returns or len(returns) < 2:
            return 0
        
        try:
            mean_return = statistics.mean(returns)
            std_return = statistics.stdev(returns)
            
            if std_return == 0:
                return 0
            
            # Convert to annual terms
            annual_mean = mean_return * periods_per_year
            annual_std = std_return * math.sqrt(periods_per_year)
            
            return (annual_mean - risk_free_rate) / annual_std
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0
    
    @staticmethod
    def calculate_sortino_ratio(
        returns: List[float], 
        risk_free_rate: float = 0.02,
        periods_per_year: int = 252
    ) -> float:
        """
        Calculate Sortino ratio (uses downside deviation instead of total volatility).
        """
        if not returns or len(returns) < 2:
            return 0
        
        try:
            mean_return = statistics.mean(returns)
            
            # Calculate downside deviation
            downside_returns = [r for r in returns if r < 0]
            
            if not downside_returns:
                return float('inf') if mean_return > risk_free_rate / periods_per_year else 0
            
            if len(downside_returns) == 1:
                downside_std = abs(downside_returns[0])
            else:
                downside_std = statistics.stdev(downside_returns)
            
            if downside_std == 0:
                return 0
            
            # Convert to annual terms
            annual_mean = mean_return * periods_per_year
            annual_downside_std = downside_std * math.sqrt(periods_per_year)
            
            return (annual_mean - risk_free_rate) / annual_downside_std
            
        except Exception as e:
            logger.error(f"Error calculating Sortino ratio: {e}")
            return 0
    
    @staticmethod
    def calculate_calmar_ratio(annualized_return: float, max_drawdown_percent: float) -> float:
        """Calculate Calmar ratio (annualized return / max drawdown)."""
        if max_drawdown_percent == 0:
            return 0
        return annualized_return / abs(max_drawdown_percent)
    
    @staticmethod
    def calculate_volatility(returns: List[float], periods_per_year: int = 252) -> float:
        """Calculate annualized volatility."""
        if not returns or len(returns) < 2:
            return 0
        
        try:
            std_return = statistics.stdev(returns)
            return std_return * math.sqrt(periods_per_year) * 100  # Convert to percentage
        except Exception as e:
            logger.error(f"Error calculating volatility: {e}")
            return 0
    
    @staticmethod
    def calculate_drawdown_metrics(equity_curve: List[Dict]) -> Dict[str, float]:
        """
        Calculate comprehensive drawdown metrics.
        
        Args:
            equity_curve: List of {'timestamp': datetime, 'value': float} dicts
        """
        if not equity_curve or len(equity_curve) < 2:
            return {
                'max_drawdown': 0,
                'max_drawdown_percent': 0,
                'max_drawdown_duration_days': 0,
                'recovery_factor': 0,
                'average_drawdown': 0,
                'drawdown_periods': 0
            }
        
        peak_value = equity_curve[0]['value'] if 'value' in equity_curve[0] else equity_curve[0]['portfolio_value']
        max_drawdown = 0
        max_drawdown_percent = 0
        current_drawdown_start = None
        max_drawdown_duration = timedelta(0)
        drawdown_periods = 0
        total_drawdown = 0
        
        for point in equity_curve:
            current_value = point['value'] if 'value' in point else point['portfolio_value']
            timestamp = point['timestamp']
            
            # Update peak
            if current_value > peak_value:
                peak_value = current_value
                # End of drawdown period
                if current_drawdown_start:
                    duration = timestamp - current_drawdown_start
                    if duration > max_drawdown_duration:
                        max_drawdown_duration = duration
                    current_drawdown_start = None
            else:
                # In drawdown
                if current_drawdown_start is None:
                    current_drawdown_start = timestamp
                    drawdown_periods += 1
                
                drawdown = peak_value - current_value
                drawdown_percent = (drawdown / peak_value) * 100 if peak_value > 0 else 0
                
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
                    max_drawdown_percent = drawdown_percent
                
                total_drawdown += drawdown_percent
        
        # Handle ongoing drawdown
        if current_drawdown_start and equity_curve:
            duration = equity_curve[-1]['timestamp'] - current_drawdown_start
            if duration > max_drawdown_duration:
                max_drawdown_duration = duration
        
        average_drawdown = total_drawdown / len(equity_curve) if equity_curve else 0
        
        # Recovery factor
        final_value = equity_curve[-1]['value'] if 'value' in equity_curve[-1] else equity_curve[-1]['portfolio_value']
        initial_value = equity_curve[0]['value'] if 'value' in equity_curve[0] else equity_curve[0]['portfolio_value']
        total_return = final_value - initial_value
        recovery_factor = total_return / max_drawdown if max_drawdown > 0 else 0
        
        return {
            'max_drawdown': max_drawdown,
            'max_drawdown_percent': max_drawdown_percent,
            'max_drawdown_duration_days': max_drawdown_duration.days,
            'recovery_factor': recovery_factor,
            'average_drawdown': average_drawdown,
            'drawdown_periods': drawdown_periods
        }
    
    @staticmethod
    def calculate_trade_statistics(trades: List[Dict]) -> Dict[str, float]:
        """
        Calculate comprehensive trade statistics.
        
        Args:
            trades: List of trade dictionaries with 'pnl', 'duration_minutes', etc.
        """
        if not trades:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0,
                'average_win': 0,
                'average_loss': 0,
                'largest_win': 0,
                'largest_loss': 0,
                'profit_factor': 0,
                'payoff_ratio': 0,
                'average_trade_duration_hours': 0,
                'expected_value': 0,
                'consecutive_wins_max': 0,
                'consecutive_losses_max': 0
            }
        
        total_trades = len(trades)
        pnls = [float(trade.get('pnl', 0)) for trade in trades]
        
        winning_trades = [pnl for pnl in pnls if pnl > 0]
        losing_trades = [pnl for pnl in pnls if pnl < 0]
        
        win_count = len(winning_trades)
        loss_count = len(losing_trades)
        win_rate = (win_count / total_trades) * 100 if total_trades > 0 else 0
        
        # Win/Loss averages
        average_win = sum(winning_trades) / len(winning_trades) if winning_trades else 0
        average_loss = sum(losing_trades) / len(losing_trades) if losing_trades else 0
        
        # Largest win/loss
        largest_win = max(winning_trades) if winning_trades else 0
        largest_loss = min(losing_trades) if losing_trades else 0
        
        # Profit factor
        gross_profit = sum(winning_trades)
        gross_loss = abs(sum(losing_trades))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
        
        # Payoff ratio
        payoff_ratio = abs(average_win / average_loss) if average_loss != 0 else 0
        
        # Expected value
        expected_value = sum(pnls) / total_trades if total_trades > 0 else 0
        
        # Average trade duration
        durations = [trade.get('duration_minutes', 0) for trade in trades]
        average_duration_hours = sum(durations) / len(durations) / 60 if durations else 0
        
        # Consecutive wins/losses
        consecutive_wins_max = 0
        consecutive_losses_max = 0
        current_wins = 0
        current_losses = 0
        
        for pnl in pnls:
            if pnl > 0:
                current_wins += 1
                current_losses = 0
                consecutive_wins_max = max(consecutive_wins_max, current_wins)
            elif pnl < 0:
                current_losses += 1
                current_wins = 0
                consecutive_losses_max = max(consecutive_losses_max, current_losses)
            else:
                current_wins = 0
                current_losses = 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': win_count,
            'losing_trades': loss_count,
            'win_rate': win_rate,
            'average_win': average_win,
            'average_loss': average_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'profit_factor': profit_factor,
            'payoff_ratio': payoff_ratio,
            'average_trade_duration_hours': average_duration_hours,
            'expected_value': expected_value,
            'consecutive_wins_max': consecutive_wins_max,
            'consecutive_losses_max': consecutive_losses_max
        }
    
    @staticmethod
    def calculate_risk_metrics(
        returns: List[float], 
        equity_curve: List[Dict],
        benchmark_returns: Optional[List[float]] = None
    ) -> Dict[str, float]:
        """Calculate comprehensive risk metrics."""
        metrics = {}
        
        # Basic risk metrics
        if returns:
            metrics['volatility'] = PerformanceMetrics.calculate_volatility(returns)
            metrics['sharpe_ratio'] = PerformanceMetrics.calculate_sharpe_ratio(returns)
            metrics['sortino_ratio'] = PerformanceMetrics.calculate_sortino_ratio(returns)
            
            # Value at Risk (95% confidence)
            if len(returns) > 20:
                sorted_returns = sorted(returns)
                var_index = int(len(sorted_returns) * 0.05)
                metrics['var_95'] = sorted_returns[var_index] * 100  # Convert to percentage
            else:
                metrics['var_95'] = 0
        
        # Drawdown metrics
        drawdown_metrics = PerformanceMetrics.calculate_drawdown_metrics(equity_curve)
        metrics.update(drawdown_metrics)
        
        # Beta calculation (if benchmark provided)
        if benchmark_returns and returns and len(returns) == len(benchmark_returns):
            try:
                covariance = statistics.covariance(returns, benchmark_returns)
                benchmark_variance = statistics.variance(benchmark_returns)
                metrics['beta'] = covariance / benchmark_variance if benchmark_variance > 0 else 0
                
                # Alpha calculation
                risk_free_rate = 0.02 / 252  # Daily risk-free rate
                portfolio_return = statistics.mean(returns)
                benchmark_return = statistics.mean(benchmark_returns)
                metrics['alpha'] = (portfolio_return - risk_free_rate) - metrics['beta'] * (benchmark_return - risk_free_rate)
                metrics['alpha'] *= 252 * 100  # Annualize and convert to percentage
                
                # Correlation
                metrics['correlation'] = statistics.correlation(returns, benchmark_returns)
                
            except Exception as e:
                logger.error(f"Error calculating beta/alpha: {e}")
                metrics['beta'] = 0
                metrics['alpha'] = 0
                metrics['correlation'] = 0
        else:
            metrics['beta'] = 0
            metrics['alpha'] = 0
            metrics['correlation'] = 0
        
        return metrics
    
    @staticmethod
    def calculate_monthly_returns(equity_curve: List[Dict]) -> List[Dict[str, any]]:
        """Calculate monthly returns from equity curve."""
        if len(equity_curve) < 2:
            return []
        
        monthly_data = {}
        
        for point in equity_curve:
            timestamp = point['timestamp']
            value = point['value'] if 'value' in point else point['portfolio_value']
            
            year_month = (timestamp.year, timestamp.month)
            
            if year_month not in monthly_data:
                monthly_data[year_month] = {'start': value, 'end': value, 'timestamp': timestamp}
            else:
                monthly_data[year_month]['end'] = value
        
        monthly_returns = []
        month_names = [
            'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
        ]
        
        for (year, month), data in sorted(monthly_data.items()):
            if data['start'] > 0:
                return_pct = ((data['end'] - data['start']) / data['start']) * 100
                monthly_returns.append({
                    'year': year,
                    'month': month,
                    'month_name': month_names[month-1],
                    'return': return_pct,
                    'timestamp': data['timestamp']
                })
        
        return monthly_returns
    
    @staticmethod
    def calculate_information_ratio(
        portfolio_returns: List[float], 
        benchmark_returns: List[float]
    ) -> float:
        """Calculate Information Ratio (active return / tracking error)."""
        if not portfolio_returns or not benchmark_returns or len(portfolio_returns) != len(benchmark_returns):
            return 0
        
        try:
            active_returns = [p - b for p, b in zip(portfolio_returns, benchmark_returns)]
            
            if not active_returns or len(active_returns) < 2:
                return 0
            
            mean_active_return = statistics.mean(active_returns)
            tracking_error = statistics.stdev(active_returns)
            
            if tracking_error == 0:
                return 0
            
            # Annualize
            return (mean_active_return / tracking_error) * math.sqrt(252)
            
        except Exception as e:
            logger.error(f"Error calculating Information Ratio: {e}")
            return 0

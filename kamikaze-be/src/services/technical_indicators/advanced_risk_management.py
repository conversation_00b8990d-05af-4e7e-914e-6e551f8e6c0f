"""
Advanced Risk Management for Technical Indicator Strategies
Sophisticated risk management with correlation analysis and dynamic position sizing
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from decimal import Decimal
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import statistics
import math

from .signal_engine import MultiTimeframeSignal, SignalResult, SignalType
from .indicators_library import TechnicalIndicatorsLibrary
from ...shared.logging_config import setup_logging

logger = setup_logging("advanced_risk_management")


class RiskLevel(Enum):
    """Risk levels for position sizing."""
    CONSERVATIVE = "CONSERVATIVE"
    MODERATE = "MODERATE"
    AGGRESSIVE = "AGGRESSIVE"
    VERY_AGGRESSIVE = "VERY_AGGRESSIVE"


class PositionSizingMethod(Enum):
    """Position sizing methods."""
    FIXED_AMOUNT = "FIXED_AMOUNT"
    FIXED_PERCENTAGE = "FIXED_PERCENTAGE"
    VOLATILITY_ADJUSTED = "VOLATILITY_ADJUSTED"
    KELLY_CRITERION = "KELLY_CRITERION"
    SIGNAL_STRENGTH = "SIGNAL_STRENGTH"
    CORRELATION_ADJUSTED = "CORRELATION_ADJUSTED"


@dataclass
class RiskMetrics:
    """Risk metrics for portfolio analysis."""
    portfolio_var: float
    portfolio_volatility: float
    max_correlation: float
    concentration_risk: float
    leverage_ratio: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    risk_adjusted_return: float


@dataclass
class PositionRisk:
    """Risk analysis for individual position."""
    symbol: str
    position_size: Decimal
    risk_amount: Decimal
    risk_percentage: float
    volatility: float
    correlation_with_portfolio: float
    expected_return: float
    risk_reward_ratio: float
    confidence_level: float


@dataclass
class RiskManagementConfig:
    """Configuration for risk management system."""
    max_portfolio_risk: float = 2.0  # Maximum portfolio risk percentage
    max_position_risk: float = 1.0   # Maximum single position risk percentage
    max_correlation: float = 0.7     # Maximum correlation between positions
    max_concentration: float = 20.0  # Maximum concentration in single asset
    max_leverage: float = 1.0        # Maximum leverage ratio
    position_sizing_method: PositionSizingMethod = PositionSizingMethod.VOLATILITY_ADJUSTED
    risk_level: RiskLevel = RiskLevel.MODERATE
    stop_loss_atr_multiplier: float = 2.0
    take_profit_atr_multiplier: float = 3.0
    correlation_lookback_days: int = 30
    volatility_lookback_days: int = 20
    enable_dynamic_sizing: bool = True
    enable_correlation_filter: bool = True


class AdvancedRiskManager:
    """
    Advanced risk management system for technical indicator strategies.
    
    Features:
    - Dynamic position sizing based on signal strength and volatility
    - Correlation analysis to prevent over-concentration
    - Portfolio-level risk monitoring
    - Adaptive stop-loss and take-profit levels
    - Risk-adjusted performance tracking
    """
    
    def __init__(self, config: RiskManagementConfig):
        self.config = config
        self.indicators_library = TechnicalIndicatorsLibrary()
        self.position_history: List[PositionRisk] = []
        self.correlation_matrix: Dict[Tuple[str, str], float] = {}
        self.volatility_cache: Dict[str, float] = {}
        
    def calculate_position_size(self, 
                              signal: MultiTimeframeSignal,
                              symbol: str,
                              current_price: float,
                              portfolio_value: Decimal,
                              current_positions: Dict[str, Any],
                              market_data: Dict[str, np.ndarray]) -> Tuple[Decimal, Dict[str, Any]]:
        """
        Calculate optimal position size based on risk management rules.
        
        Returns:
            (position_size, risk_analysis)
        """
        try:
            # Calculate base position size
            base_size = self._calculate_base_position_size(
                signal, portfolio_value, current_price
            )
            
            # Apply volatility adjustment
            volatility_adjusted_size = self._apply_volatility_adjustment(
                base_size, symbol, market_data, current_price
            )
            
            # Apply correlation adjustment
            correlation_adjusted_size = self._apply_correlation_adjustment(
                volatility_adjusted_size, symbol, current_positions, market_data
            )
            
            # Apply portfolio risk limits
            final_size = self._apply_portfolio_risk_limits(
                correlation_adjusted_size, portfolio_value, current_positions
            )
            
            # Calculate risk metrics
            risk_analysis = self._calculate_position_risk_analysis(
                final_size, symbol, current_price, signal, market_data
            )
            
            # Validate against maximum limits
            final_size = self._validate_position_limits(final_size, risk_analysis, portfolio_value)
            
            logger.debug(f"Position size calculated for {symbol}: {final_size} "
                        f"(Risk: {risk_analysis['risk_percentage']:.2f}%)")
            
            return final_size, risk_analysis
            
        except Exception as e:
            logger.error(f"Error calculating position size for {symbol}: {e}")
            # Return conservative fallback
            fallback_size = portfolio_value * Decimal('0.01')  # 1% of portfolio
            return fallback_size, {'error': str(e)}
    
    def _calculate_base_position_size(self, 
                                    signal: MultiTimeframeSignal,
                                    portfolio_value: Decimal,
                                    current_price: float) -> Decimal:
        """Calculate base position size before adjustments."""
        if self.config.position_sizing_method == PositionSizingMethod.FIXED_AMOUNT:
            return Decimal('1000')  # Fixed $1000
            
        elif self.config.position_sizing_method == PositionSizingMethod.FIXED_PERCENTAGE:
            percentage = self._get_risk_level_percentage()
            return portfolio_value * Decimal(str(percentage / 100))
            
        elif self.config.position_sizing_method == PositionSizingMethod.SIGNAL_STRENGTH:
            # Size based on signal strength and confidence
            strength_factor = signal.consensus_score * signal.primary_signal.confidence
            base_percentage = self._get_risk_level_percentage()
            adjusted_percentage = base_percentage * strength_factor
            return portfolio_value * Decimal(str(adjusted_percentage / 100))
            
        else:
            # Default to moderate percentage
            return portfolio_value * Decimal('0.05')  # 5%
    
    def _apply_volatility_adjustment(self, 
                                   base_size: Decimal,
                                   symbol: str,
                                   market_data: Dict[str, np.ndarray],
                                   current_price: float) -> Decimal:
        """Adjust position size based on asset volatility."""
        if not self.config.enable_dynamic_sizing:
            return base_size
        
        try:
            # Calculate ATR-based volatility
            if 'high' in market_data and 'low' in market_data and 'close' in market_data:
                atr_result = self.indicators_library.ta_lib.atr(
                    market_data, period=self.config.volatility_lookback_days
                )
                latest_atr = atr_result.get_latest_value()
                
                if latest_atr:
                    # Calculate volatility as percentage of price
                    volatility_pct = (latest_atr / current_price) * 100
                    
                    # Inverse volatility scaling (higher volatility = smaller position)
                    # Target volatility of 2%
                    target_volatility = 2.0
                    volatility_adjustment = target_volatility / max(volatility_pct, 0.5)
                    volatility_adjustment = min(2.0, max(0.5, volatility_adjustment))  # Cap adjustment
                    
                    adjusted_size = base_size * Decimal(str(volatility_adjustment))
                    
                    # Cache volatility for correlation calculations
                    self.volatility_cache[symbol] = volatility_pct
                    
                    return adjusted_size
            
            return base_size
            
        except Exception as e:
            logger.error(f"Error applying volatility adjustment for {symbol}: {e}")
            return base_size
    
    def _apply_correlation_adjustment(self, 
                                    base_size: Decimal,
                                    symbol: str,
                                    current_positions: Dict[str, Any],
                                    market_data: Dict[str, np.ndarray]) -> Decimal:
        """Adjust position size based on correlation with existing positions."""
        if not self.config.enable_correlation_filter or not current_positions:
            return base_size
        
        try:
            max_correlation = 0.0
            
            for existing_symbol in current_positions.keys():
                if existing_symbol != symbol:
                    correlation = self._calculate_correlation(symbol, existing_symbol, market_data)
                    max_correlation = max(max_correlation, abs(correlation))
            
            # Reduce position size if high correlation
            if max_correlation > self.config.max_correlation:
                correlation_penalty = (max_correlation - self.config.max_correlation) / (1.0 - self.config.max_correlation)
                adjustment_factor = 1.0 - (correlation_penalty * 0.5)  # Up to 50% reduction
                adjusted_size = base_size * Decimal(str(adjustment_factor))
                
                logger.debug(f"Applied correlation adjustment for {symbol}: "
                           f"{adjustment_factor:.3f} (max correlation: {max_correlation:.3f})")
                
                return adjusted_size
            
            return base_size
            
        except Exception as e:
            logger.error(f"Error applying correlation adjustment for {symbol}: {e}")
            return base_size
    
    def _apply_portfolio_risk_limits(self, 
                                   base_size: Decimal,
                                   portfolio_value: Decimal,
                                   current_positions: Dict[str, Any]) -> Decimal:
        """Apply portfolio-level risk limits."""
        # Calculate current portfolio risk
        current_risk = self._calculate_current_portfolio_risk(current_positions, portfolio_value)
        
        # Calculate position risk
        position_risk_pct = float(base_size / portfolio_value) * 100
        
        # Check individual position risk limit
        if position_risk_pct > self.config.max_position_risk:
            max_position_size = portfolio_value * Decimal(str(self.config.max_position_risk / 100))
            base_size = min(base_size, max_position_size)
        
        # Check portfolio risk limit
        total_risk = current_risk + position_risk_pct
        if total_risk > self.config.max_portfolio_risk:
            available_risk = self.config.max_portfolio_risk - current_risk
            if available_risk > 0:
                max_size = portfolio_value * Decimal(str(available_risk / 100))
                base_size = min(base_size, max_size)
            else:
                base_size = Decimal('0')  # No room for new positions
        
        return base_size
    
    def _calculate_position_risk_analysis(self, 
                                        position_size: Decimal,
                                        symbol: str,
                                        current_price: float,
                                        signal: MultiTimeframeSignal,
                                        market_data: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """Calculate comprehensive risk analysis for position."""
        try:
            # Basic risk metrics
            position_value = position_size * Decimal(str(current_price))
            
            # Calculate stop loss based on ATR
            stop_loss_price = self._calculate_dynamic_stop_loss(
                current_price, signal.primary_signal.signal_type, market_data
            )
            
            # Calculate risk amount
            risk_per_share = abs(current_price - stop_loss_price)
            total_risk = position_size * Decimal(str(risk_per_share))
            
            # Calculate expected return (simplified)
            take_profit_price = self._calculate_dynamic_take_profit(
                current_price, signal.primary_signal.signal_type, market_data
            )
            expected_return_per_share = abs(take_profit_price - current_price)
            total_expected_return = position_size * Decimal(str(expected_return_per_share))
            
            # Risk-reward ratio
            risk_reward_ratio = float(total_expected_return / total_risk) if total_risk > 0 else 0
            
            return {
                'position_size': float(position_size),
                'position_value': float(position_value),
                'risk_amount': float(total_risk),
                'risk_percentage': float(total_risk / position_value * 100) if position_value > 0 else 0,
                'expected_return': float(total_expected_return),
                'risk_reward_ratio': risk_reward_ratio,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'confidence_level': signal.primary_signal.confidence,
                'signal_strength': signal.primary_signal.strength,
                'volatility': self.volatility_cache.get(symbol, 0)
            }
            
        except Exception as e:
            logger.error(f"Error calculating risk analysis for {symbol}: {e}")
            return {'error': str(e)}
    
    def _calculate_dynamic_stop_loss(self, 
                                   current_price: float,
                                   signal_type: SignalType,
                                   market_data: Dict[str, np.ndarray]) -> float:
        """Calculate dynamic stop loss based on ATR."""
        try:
            atr_result = self.indicators_library.ta_lib.atr(market_data, period=14)
            latest_atr = atr_result.get_latest_value()
            
            if latest_atr:
                atr_distance = latest_atr * self.config.stop_loss_atr_multiplier
                
                if signal_type == SignalType.BUY:
                    return current_price - atr_distance
                else:
                    return current_price + atr_distance
            
            # Fallback to percentage-based stop loss
            fallback_pct = 0.02  # 2%
            if signal_type == SignalType.BUY:
                return current_price * (1 - fallback_pct)
            else:
                return current_price * (1 + fallback_pct)
                
        except Exception as e:
            logger.error(f"Error calculating dynamic stop loss: {e}")
            return current_price * 0.98  # 2% fallback
    
    def _calculate_dynamic_take_profit(self, 
                                     current_price: float,
                                     signal_type: SignalType,
                                     market_data: Dict[str, np.ndarray]) -> float:
        """Calculate dynamic take profit based on ATR."""
        try:
            atr_result = self.indicators_library.ta_lib.atr(market_data, period=14)
            latest_atr = atr_result.get_latest_value()
            
            if latest_atr:
                atr_distance = latest_atr * self.config.take_profit_atr_multiplier
                
                if signal_type == SignalType.BUY:
                    return current_price + atr_distance
                else:
                    return current_price - atr_distance
            
            # Fallback to percentage-based take profit
            fallback_pct = 0.04  # 4%
            if signal_type == SignalType.BUY:
                return current_price * (1 + fallback_pct)
            else:
                return current_price * (1 - fallback_pct)
                
        except Exception as e:
            logger.error(f"Error calculating dynamic take profit: {e}")
            return current_price * 1.04  # 4% fallback
    
    def _calculate_correlation(self,
                             symbol1: str,
                             symbol2: str,
                             market_data: Dict[str, np.ndarray]) -> float:
        """Calculate correlation between two assets."""
        cache_key = tuple(sorted([symbol1, symbol2]))

        if cache_key in self.correlation_matrix:
            return self.correlation_matrix[cache_key]

        try:
            # TODO: Implement proper correlation calculation using historical price data
            # This should:
            # 1. Fetch historical returns for both symbols
            # 2. Calculate Pearson correlation coefficient
            # 3. Use appropriate lookback period (e.g., 30 days)
            # 4. Handle missing data appropriately

            # For now, return neutral correlation
            correlation = 0.0

            self.correlation_matrix[cache_key] = correlation
            return correlation

        except Exception as e:
            logger.error(f"Error calculating correlation between {symbol1} and {symbol2}: {e}")
            return 0.0
    
    def _calculate_current_portfolio_risk(self, 
                                        current_positions: Dict[str, Any],
                                        portfolio_value: Decimal) -> float:
        """Calculate current portfolio risk percentage."""
        total_risk = Decimal('0')
        
        for position in current_positions.values():
            # Simplified risk calculation
            position_value = position.get('value', Decimal('0'))
            risk_pct = position.get('risk_percentage', 1.0)  # Default 1% risk
            position_risk = position_value * Decimal(str(risk_pct / 100))
            total_risk += position_risk
        
        return float(total_risk / portfolio_value * 100) if portfolio_value > 0 else 0
    
    def _get_risk_level_percentage(self) -> float:
        """Get base percentage based on risk level."""
        risk_percentages = {
            RiskLevel.CONSERVATIVE: 2.0,
            RiskLevel.MODERATE: 5.0,
            RiskLevel.AGGRESSIVE: 10.0,
            RiskLevel.VERY_AGGRESSIVE: 15.0
        }
        return risk_percentages.get(self.config.risk_level, 5.0)
    
    def _validate_position_limits(self, 
                                position_size: Decimal,
                                risk_analysis: Dict[str, Any],
                                portfolio_value: Decimal) -> Decimal:
        """Final validation against all position limits."""
        # Maximum position value (concentration limit)
        max_position_value = portfolio_value * Decimal(str(self.config.max_concentration / 100))
        current_position_value = position_size * Decimal(str(risk_analysis.get('position_value', 0)))
        
        if current_position_value > max_position_value:
            # Reduce position size to meet concentration limit
            position_size = max_position_value / Decimal(str(risk_analysis.get('position_value', 1)))
        
        # Ensure minimum position size
        min_position_size = Decimal('0.01')  # Minimum $0.01
        position_size = max(position_size, min_position_size)
        
        return position_size
    
    def calculate_portfolio_risk_metrics(self, 
                                       current_positions: Dict[str, Any],
                                       portfolio_value: Decimal,
                                       historical_returns: List[float]) -> RiskMetrics:
        """Calculate comprehensive portfolio risk metrics."""
        try:
            # Portfolio volatility
            portfolio_volatility = statistics.stdev(historical_returns) if len(historical_returns) > 1 else 0
            
            # Portfolio VaR (95% confidence)
            if len(historical_returns) > 20:
                sorted_returns = sorted(historical_returns)
                var_index = int(len(sorted_returns) * 0.05)
                portfolio_var = abs(sorted_returns[var_index])
            else:
                portfolio_var = 0
            
            # Maximum correlation
            correlations = list(self.correlation_matrix.values())
            max_correlation = max(correlations) if correlations else 0
            
            # Concentration risk
            if current_positions:
                position_values = [pos.get('value', 0) for pos in current_positions.values()]
                max_position = max(position_values) if position_values else 0
                concentration_risk = (max_position / float(portfolio_value)) * 100 if portfolio_value > 0 else 0
            else:
                concentration_risk = 0
            
            # Leverage ratio (simplified)
            total_position_value = sum(pos.get('value', 0) for pos in current_positions.values())
            leverage_ratio = total_position_value / float(portfolio_value) if portfolio_value > 0 else 0
            
            # Sharpe ratio (simplified)
            if historical_returns and portfolio_volatility > 0:
                avg_return = statistics.mean(historical_returns)
                risk_free_rate = 0.02 / 252  # Daily risk-free rate
                sharpe_ratio = (avg_return - risk_free_rate) / portfolio_volatility
            else:
                sharpe_ratio = 0
            
            # Sortino ratio (downside deviation)
            downside_returns = [r for r in historical_returns if r < 0]
            if downside_returns and len(downside_returns) > 1:
                downside_deviation = statistics.stdev(downside_returns)
                avg_return = statistics.mean(historical_returns)
                risk_free_rate = 0.02 / 252
                sortino_ratio = (avg_return - risk_free_rate) / downside_deviation
            else:
                sortino_ratio = 0
            
            # Maximum drawdown (simplified)
            if historical_returns:
                cumulative_returns = np.cumprod(1 + np.array(historical_returns))
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = (cumulative_returns - running_max) / running_max
                max_drawdown = abs(np.min(drawdowns)) * 100
            else:
                max_drawdown = 0
            
            # Risk-adjusted return
            risk_adjusted_return = sharpe_ratio * 100  # Convert to percentage
            
            return RiskMetrics(
                portfolio_var=portfolio_var * 100,  # Convert to percentage
                portfolio_volatility=portfolio_volatility * 100,
                max_correlation=max_correlation,
                concentration_risk=concentration_risk,
                leverage_ratio=leverage_ratio,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                max_drawdown=max_drawdown,
                risk_adjusted_return=risk_adjusted_return
            )
            
        except Exception as e:
            logger.error(f"Error calculating portfolio risk metrics: {e}")
            return RiskMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0)
    
    def should_reduce_risk(self, risk_metrics: RiskMetrics) -> Tuple[bool, List[str]]:
        """Determine if risk should be reduced based on current metrics."""
        warnings = []
        should_reduce = False
        
        if risk_metrics.portfolio_var > self.config.max_portfolio_risk:
            warnings.append(f"Portfolio VaR ({risk_metrics.portfolio_var:.2f}%) exceeds limit")
            should_reduce = True
        
        if risk_metrics.max_correlation > self.config.max_correlation:
            warnings.append(f"Maximum correlation ({risk_metrics.max_correlation:.3f}) exceeds limit")
            should_reduce = True
        
        if risk_metrics.concentration_risk > self.config.max_concentration:
            warnings.append(f"Concentration risk ({risk_metrics.concentration_risk:.2f}%) exceeds limit")
            should_reduce = True
        
        if risk_metrics.leverage_ratio > self.config.max_leverage:
            warnings.append(f"Leverage ratio ({risk_metrics.leverage_ratio:.2f}) exceeds limit")
            should_reduce = True
        
        if risk_metrics.max_drawdown > 15.0:  # 15% drawdown threshold
            warnings.append(f"Maximum drawdown ({risk_metrics.max_drawdown:.2f}%) is high")
            should_reduce = True
        
        return should_reduce, warnings
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk management summary."""
        return {
            'config': {
                'max_portfolio_risk': self.config.max_portfolio_risk,
                'max_position_risk': self.config.max_position_risk,
                'max_correlation': self.config.max_correlation,
                'position_sizing_method': self.config.position_sizing_method.value,
                'risk_level': self.config.risk_level.value
            },
            'statistics': {
                'total_positions_analyzed': len(self.position_history),
                'correlation_pairs_calculated': len(self.correlation_matrix),
                'volatility_cache_size': len(self.volatility_cache)
            }
        }

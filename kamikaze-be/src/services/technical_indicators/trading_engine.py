"""
Real-time Technical Indicator Trading Engine
Production-ready trading engine for executing technical indicator strategies
"""

import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any, Callable
from decimal import Decimal
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import json
import numpy as np

from .signal_engine import AdvancedSignalEngine, MultiTimeframeSignal, TimeFrame, SignalFilter
from .advanced_risk_management import AdvancedRiskManager, RiskManagementConfig
from .strategy_framework import MultiIndicatorStrategy
from .data_provider import get_data_provider, DataProviderInterface
from ...shared.logging_config import setup_logging

logger = setup_logging("trading_engine")


class OrderType(Enum):
    """Order types for trading."""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"


class OrderStatus(Enum):
    """Order status tracking."""
    PENDING = "PENDING"
    SUBMITTED = "SUBMITTED"
    FILLED = "FILLED"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"


class EngineState(Enum):
    """Trading engine states."""
    STOPPED = "STOPPED"
    STARTING = "STARTING"
    RUNNING = "RUNNING"
    PAUSED = "PAUSED"
    STOPPING = "STOPPING"
    ERROR = "ERROR"


@dataclass
class TradingOrder:
    """Trading order representation."""
    id: str
    symbol: str
    side: str  # BUY/SELL
    order_type: OrderType
    quantity: Decimal
    price: Optional[Decimal] = None
    stop_price: Optional[Decimal] = None
    time_in_force: str = "GTC"  # Good Till Cancelled
    status: OrderStatus = OrderStatus.PENDING
    created_at: datetime = field(default_factory=datetime.utcnow)
    filled_at: Optional[datetime] = None
    filled_quantity: Decimal = Decimal('0')
    filled_price: Optional[Decimal] = None
    commission: Decimal = Decimal('0')
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Position:
    """Trading position representation."""
    symbol: str
    quantity: Decimal
    entry_price: Decimal
    current_price: Decimal
    unrealized_pnl: Decimal
    realized_pnl: Decimal = Decimal('0')
    entry_time: datetime = field(default_factory=datetime.utcnow)
    stop_loss: Optional[Decimal] = None
    take_profit: Optional[Decimal] = None
    strategy_name: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def market_value(self) -> Decimal:
        """Current market value of position."""
        return self.quantity * self.current_price
    
    @property
    def total_pnl(self) -> Decimal:
        """Total PnL (realized + unrealized)."""
        return self.realized_pnl + self.unrealized_pnl


@dataclass
class TradingEngineConfig:
    """Configuration for trading engine."""
    max_concurrent_orders: int = 10
    order_timeout_seconds: int = 30
    position_update_interval: int = 5  # seconds
    signal_generation_interval: int = 60  # seconds
    enable_paper_trading: bool = True
    enable_order_validation: bool = True
    enable_risk_checks: bool = True
    max_daily_trades: int = 50
    max_daily_loss_pct: float = 5.0
    emergency_stop_loss_pct: float = 10.0
    slippage_tolerance_pct: float = 0.1
    commission_rate: float = 0.001


class TechnicalIndicatorTradingEngine:
    """
    Production-ready trading engine for technical indicator strategies.
    
    Features:
    - Real-time signal generation and execution
    - Advanced risk management integration
    - Order management and tracking
    - Position monitoring and updates
    - Performance tracking and reporting
    - Error handling and recovery
    - Paper trading support
    """
    
    def __init__(self,
                 config: TradingEngineConfig,
                 risk_config: RiskManagementConfig,
                 signal_filter: Optional[SignalFilter] = None,
                 data_provider: Optional[DataProviderInterface] = None):
        self.config = config
        self.signal_engine = AdvancedSignalEngine()
        self.risk_manager = AdvancedRiskManager(risk_config)
        self.signal_filter = signal_filter or SignalFilter()
        self.data_provider = data_provider or get_data_provider()
        
        # State management
        self.state = EngineState.STOPPED
        self.start_time: Optional[datetime] = None
        self.last_signal_time: Optional[datetime] = None
        
        # Trading data
        self.positions: Dict[str, Position] = {}
        self.orders: Dict[str, TradingOrder] = {}
        self.portfolio_value = Decimal('100000')  # Starting capital
        self.cash_balance = Decimal('100000')
        
        # Performance tracking
        self.daily_trades = 0
        self.daily_pnl = Decimal('0')
        self.total_pnl = Decimal('0')
        self.trade_history: List[Dict[str, Any]] = []
        
        # Event handlers
        self.order_fill_handlers: List[Callable] = []
        self.position_update_handlers: List[Callable] = []
        self.error_handlers: List[Callable] = []
        
        # Background tasks
        self._running_tasks: List[asyncio.Task] = []
        
    async def start(self):
        """Start the trading engine."""
        if self.state != EngineState.STOPPED:
            raise RuntimeError(f"Cannot start engine in state: {self.state}")
        
        logger.info("🚀 Starting Technical Indicator Trading Engine")
        self.state = EngineState.STARTING
        
        try:
            # Initialize components
            await self._initialize_components()
            
            # Start background tasks
            self._running_tasks = [
                asyncio.create_task(self._signal_generation_loop()),
                asyncio.create_task(self._position_monitoring_loop()),
                asyncio.create_task(self._order_management_loop()),
                asyncio.create_task(self._risk_monitoring_loop())
            ]
            
            self.state = EngineState.RUNNING
            self.start_time = datetime.utcnow()
            
            logger.info("✅ Trading Engine started successfully")
            
        except Exception as e:
            self.state = EngineState.ERROR
            logger.error(f"❌ Failed to start trading engine: {e}")
            raise
    
    async def stop(self):
        """Stop the trading engine."""
        if self.state == EngineState.STOPPED:
            return
        
        logger.info("🛑 Stopping Trading Engine")
        self.state = EngineState.STOPPING
        
        try:
            # Cancel all running tasks
            for task in self._running_tasks:
                task.cancel()
            
            # Wait for tasks to complete
            await asyncio.gather(*self._running_tasks, return_exceptions=True)
            
            # Close all positions if configured
            if self.config.enable_paper_trading:
                await self._close_all_positions("Engine shutdown")
            
            self.state = EngineState.STOPPED
            logger.info("✅ Trading Engine stopped successfully")
            
        except Exception as e:
            self.state = EngineState.ERROR
            logger.error(f"❌ Error stopping trading engine: {e}")
            raise
    
    async def pause(self):
        """Pause the trading engine."""
        if self.state == EngineState.RUNNING:
            self.state = EngineState.PAUSED
            logger.info("⏸️ Trading Engine paused")
    
    async def resume(self):
        """Resume the trading engine."""
        if self.state == EngineState.PAUSED:
            self.state = EngineState.RUNNING
            logger.info("▶️ Trading Engine resumed")
    
    def add_strategy(self, strategy: MultiIndicatorStrategy):
        """Add a trading strategy to the engine."""
        self.signal_engine.add_strategy(strategy)
        logger.info(f"Added strategy '{strategy.name}' to trading engine")
    
    async def _initialize_components(self):
        """Initialize engine components."""
        # Reset daily counters
        self.daily_trades = 0
        self.daily_pnl = Decimal('0')
        
        # Clear old data
        self.orders.clear()
        
        logger.info("Engine components initialized")
    
    async def _signal_generation_loop(self):
        """Main signal generation loop."""
        while self.state in [EngineState.RUNNING, EngineState.PAUSED]:
            try:
                if self.state == EngineState.RUNNING:
                    await self._generate_and_process_signals()
                    self.last_signal_time = datetime.utcnow()
                
                await asyncio.sleep(self.config.signal_generation_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in signal generation loop: {e}")
                await self._handle_error(e)
                await asyncio.sleep(10)  # Wait before retrying
    
    async def _generate_and_process_signals(self):
        """Generate signals and process them for trading."""
        try:
            # Get market data (this would come from your data feed)
            market_data = await self._get_market_data()
            
            if not market_data:
                return
            
            # Generate multi-timeframe signals
            signals = self.signal_engine.generate_multi_timeframe_signals(
                market_data, 
                primary_timeframe=TimeFrame.H1,
                signal_filter=self.signal_filter
            )
            
            # Process each signal
            for signal in signals:
                await self._process_signal(signal)
                
        except Exception as e:
            logger.error(f"Error generating and processing signals: {e}")
            raise
    
    async def _process_signal(self, signal: MultiTimeframeSignal):
        """Process a trading signal."""
        try:
            symbol = signal.primary_signal.metadata.get('symbol', 'UNKNOWN')
            
            # Check daily limits
            if self.daily_trades >= self.config.max_daily_trades:
                logger.warning(f"Daily trade limit reached ({self.config.max_daily_trades})")
                return
            
            # Check if we already have a position
            existing_position = self.positions.get(symbol)
            
            if signal.primary_signal.signal_type.value in ['BUY', 'SELL']:
                if existing_position:
                    logger.debug(f"Already have position in {symbol}, skipping signal")
                    return
                
                await self._execute_entry_signal(signal, symbol)
                
            elif signal.primary_signal.signal_type.value in ['CLOSE_LONG', 'CLOSE_SHORT']:
                if existing_position:
                    await self._execute_exit_signal(signal, symbol)
                
        except Exception as e:
            logger.error(f"Error processing signal for {symbol}: {e}")
    
    async def _execute_entry_signal(self, signal: MultiTimeframeSignal, symbol: str):
        """Execute entry signal."""
        try:
            # Get current market price
            current_price = await self._get_current_price(symbol)
            if not current_price:
                return
            
            # Calculate position size using risk management
            position_size, risk_analysis = self.risk_manager.calculate_position_size(
                signal, symbol, current_price, self.portfolio_value, 
                self.positions, await self._get_market_data_for_symbol(symbol)
            )
            
            if position_size <= 0:
                logger.debug(f"Position size too small for {symbol}, skipping")
                return
            
            # Create and submit order
            order = await self._create_market_order(
                symbol=symbol,
                side=signal.primary_signal.signal_type.value,
                quantity=position_size,
                metadata={
                    'signal_confidence': signal.primary_signal.confidence,
                    'signal_strength': signal.primary_signal.strength,
                    'strategy_name': signal.primary_signal.metadata.get('rule_name', 'Unknown'),
                    'stop_loss': risk_analysis.get('stop_loss_price'),
                    'take_profit': risk_analysis.get('take_profit_price')
                }
            )
            
            await self._submit_order(order)
            
            logger.info(f"📈 Entry signal executed: {signal.primary_signal.signal_type.value} "
                       f"{position_size} {symbol} @ {current_price} "
                       f"(Confidence: {signal.primary_signal.confidence:.3f})")
            
        except Exception as e:
            logger.error(f"Error executing entry signal for {symbol}: {e}")
    
    async def _execute_exit_signal(self, signal: MultiTimeframeSignal, symbol: str):
        """Execute exit signal."""
        try:
            position = self.positions.get(symbol)
            if not position:
                return
            
            # Create exit order
            exit_side = "SELL" if position.quantity > 0 else "BUY"
            
            order = await self._create_market_order(
                symbol=symbol,
                side=exit_side,
                quantity=abs(position.quantity),
                metadata={
                    'exit_signal': True,
                    'exit_reason': signal.primary_signal.metadata.get('rule_name', 'Signal exit')
                }
            )
            
            await self._submit_order(order)
            
            logger.info(f"📉 Exit signal executed: {exit_side} {abs(position.quantity)} {symbol}")
            
        except Exception as e:
            logger.error(f"Error executing exit signal for {symbol}: {e}")
    
    async def _create_market_order(self, symbol: str, side: str, quantity: Decimal, 
                                 metadata: Optional[Dict[str, Any]] = None) -> TradingOrder:
        """Create a market order."""
        import uuid
        
        order = TradingOrder(
            id=str(uuid.uuid4()),
            symbol=symbol,
            side=side,
            order_type=OrderType.MARKET,
            quantity=quantity,
            metadata=metadata or {}
        )
        
        return order
    
    async def _submit_order(self, order: TradingOrder):
        """Submit order for execution."""
        try:
            # Validate order
            if self.config.enable_order_validation:
                validation_result = await self._validate_order(order)
                if not validation_result['valid']:
                    logger.warning(f"Order validation failed: {validation_result['reason']}")
                    return
            
            # Add to orders tracking
            self.orders[order.id] = order
            order.status = OrderStatus.SUBMITTED
            
            # Simulate order execution for paper trading
            if self.config.enable_paper_trading:
                await self._simulate_order_fill(order)
            else:
                # Here you would integrate with your broker's API
                pass
                
        except Exception as e:
            logger.error(f"Error submitting order {order.id}: {e}")
            order.status = OrderStatus.REJECTED
    
    async def _simulate_order_fill(self, order: TradingOrder):
        """Simulate order fill for paper trading."""
        try:
            # Get current price
            current_price = await self._get_current_price(order.symbol)
            if not current_price:
                order.status = OrderStatus.REJECTED
                return
            
            # Apply slippage
            slippage_factor = 1 + (self.config.slippage_tolerance_pct / 100)
            if order.side == "BUY":
                fill_price = current_price * slippage_factor
            else:
                fill_price = current_price / slippage_factor
            
            # Calculate commission
            commission = order.quantity * fill_price * Decimal(str(self.config.commission_rate))
            
            # Fill the order
            order.status = OrderStatus.FILLED
            order.filled_at = datetime.utcnow()
            order.filled_quantity = order.quantity
            order.filled_price = Decimal(str(fill_price))
            order.commission = commission
            
            # Update position
            await self._update_position_from_fill(order)
            
            # Update portfolio
            self._update_portfolio_from_fill(order)
            
            # Track trade
            self.daily_trades += 1
            
            logger.debug(f"Order {order.id} filled: {order.quantity} {order.symbol} @ {fill_price}")
            
            # Notify handlers
            for handler in self.order_fill_handlers:
                try:
                    await handler(order)
                except Exception as e:
                    logger.error(f"Error in order fill handler: {e}")
                    
        except Exception as e:
            logger.error(f"Error simulating order fill for {order.id}: {e}")
            order.status = OrderStatus.REJECTED
    
    async def _update_position_from_fill(self, order: TradingOrder):
        """Update position from order fill."""
        symbol = order.symbol
        
        if symbol not in self.positions:
            # New position
            if order.side == "BUY":
                quantity = order.filled_quantity
            else:
                quantity = -order.filled_quantity
            
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=quantity,
                entry_price=order.filled_price,
                current_price=order.filled_price,
                unrealized_pnl=Decimal('0'),
                strategy_name=order.metadata.get('strategy_name', ''),
                stop_loss=order.metadata.get('stop_loss'),
                take_profit=order.metadata.get('take_profit'),
                metadata=order.metadata
            )
        else:
            # Update existing position
            position = self.positions[symbol]
            
            if order.side == "BUY":
                new_quantity = position.quantity + order.filled_quantity
            else:
                new_quantity = position.quantity - order.filled_quantity
            
            if new_quantity == 0:
                # Position closed
                realized_pnl = self._calculate_realized_pnl(position, order)
                position.realized_pnl += realized_pnl
                self.daily_pnl += realized_pnl
                self.total_pnl += realized_pnl
                
                # Remove position
                del self.positions[symbol]
                
                logger.info(f"Position closed: {symbol} PnL: {realized_pnl:.2f}")
            else:
                # Update position
                position.quantity = new_quantity
                # Update average entry price if adding to position
                if (position.quantity > 0 and order.side == "BUY") or \
                   (position.quantity < 0 and order.side == "SELL"):
                    total_cost = (position.entry_price * abs(position.quantity - order.filled_quantity)) + \
                                (order.filled_price * order.filled_quantity)
                    position.entry_price = total_cost / abs(position.quantity)
    
    def _calculate_realized_pnl(self, position: Position, exit_order: TradingOrder) -> Decimal:
        """Calculate realized PnL from position exit."""
        if position.quantity > 0:  # Long position
            return (exit_order.filled_price - position.entry_price) * exit_order.filled_quantity
        else:  # Short position
            return (position.entry_price - exit_order.filled_price) * exit_order.filled_quantity
    
    def _update_portfolio_from_fill(self, order: TradingOrder):
        """Update portfolio values from order fill."""
        trade_value = order.filled_quantity * order.filled_price
        
        if order.side == "BUY":
            self.cash_balance -= (trade_value + order.commission)
        else:
            self.cash_balance += (trade_value - order.commission)
        
        # Update portfolio value (cash + positions market value)
        positions_value = sum(pos.market_value for pos in self.positions.values())
        self.portfolio_value = self.cash_balance + positions_value
    
    async def _position_monitoring_loop(self):
        """Monitor positions and update prices."""
        while self.state in [EngineState.RUNNING, EngineState.PAUSED]:
            try:
                if self.state == EngineState.RUNNING:
                    await self._update_position_prices()
                    await self._check_stop_loss_take_profit()
                
                await asyncio.sleep(self.config.position_update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in position monitoring loop: {e}")
                await asyncio.sleep(5)
    
    async def _update_position_prices(self):
        """Update current prices for all positions."""
        for symbol, position in self.positions.items():
            try:
                current_price = await self._get_current_price(symbol)
                if current_price:
                    position.current_price = Decimal(str(current_price))
                    
                    # Calculate unrealized PnL
                    if position.quantity > 0:  # Long
                        position.unrealized_pnl = (position.current_price - position.entry_price) * position.quantity
                    else:  # Short
                        position.unrealized_pnl = (position.entry_price - position.current_price) * abs(position.quantity)
                        
            except Exception as e:
                logger.error(f"Error updating price for {symbol}: {e}")
    
    async def _check_stop_loss_take_profit(self):
        """Check stop loss and take profit levels."""
        for symbol, position in list(self.positions.items()):
            try:
                current_price = position.current_price
                
                # Check stop loss
                if position.stop_loss:
                    if (position.quantity > 0 and current_price <= position.stop_loss) or \
                       (position.quantity < 0 and current_price >= position.stop_loss):
                        await self._close_position(symbol, "Stop loss triggered")
                        continue
                
                # Check take profit
                if position.take_profit:
                    if (position.quantity > 0 and current_price >= position.take_profit) or \
                       (position.quantity < 0 and current_price <= position.take_profit):
                        await self._close_position(symbol, "Take profit triggered")
                        continue
                        
            except Exception as e:
                logger.error(f"Error checking stop/profit for {symbol}: {e}")
    
    async def _close_position(self, symbol: str, reason: str):
        """Close a position."""
        position = self.positions.get(symbol)
        if not position:
            return
        
        exit_side = "SELL" if position.quantity > 0 else "BUY"
        
        order = await self._create_market_order(
            symbol=symbol,
            side=exit_side,
            quantity=abs(position.quantity),
            metadata={'exit_reason': reason}
        )
        
        await self._submit_order(order)
        logger.info(f"Position closed: {symbol} - {reason}")
    
    async def _close_all_positions(self, reason: str):
        """Close all open positions."""
        for symbol in list(self.positions.keys()):
            await self._close_position(symbol, reason)
    
    async def _order_management_loop(self):
        """Manage pending orders."""
        while self.state in [EngineState.RUNNING, EngineState.PAUSED]:
            try:
                if self.state == EngineState.RUNNING:
                    await self._check_pending_orders()
                
                await asyncio.sleep(5)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in order management loop: {e}")
                await asyncio.sleep(5)
    
    async def _check_pending_orders(self):
        """Check and manage pending orders."""
        current_time = datetime.utcnow()
        
        for order_id, order in list(self.orders.items()):
            if order.status == OrderStatus.SUBMITTED:
                # Check for timeout
                if (current_time - order.created_at).seconds > self.config.order_timeout_seconds:
                    order.status = OrderStatus.CANCELLED
                    logger.warning(f"Order {order_id} timed out and was cancelled")
    
    async def _risk_monitoring_loop(self):
        """Monitor portfolio risk levels."""
        while self.state in [EngineState.RUNNING, EngineState.PAUSED]:
            try:
                if self.state == EngineState.RUNNING:
                    await self._check_risk_limits()
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in risk monitoring loop: {e}")
                await asyncio.sleep(10)
    
    async def _check_risk_limits(self):
        """Check portfolio risk limits."""
        try:
            # Check daily loss limit
            daily_loss_pct = float(self.daily_pnl / self.portfolio_value * 100)
            if daily_loss_pct <= -self.config.max_daily_loss_pct:
                logger.critical(f"Daily loss limit reached: {daily_loss_pct:.2f}%")
                await self._emergency_stop("Daily loss limit exceeded")
                return
            
            # Check emergency stop loss
            total_loss_pct = float(self.total_pnl / Decimal('100000') * 100)  # vs initial capital
            if total_loss_pct <= -self.config.emergency_stop_loss_pct:
                logger.critical(f"Emergency stop loss triggered: {total_loss_pct:.2f}%")
                await self._emergency_stop("Emergency stop loss triggered")
                return
                
        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
    
    async def _emergency_stop(self, reason: str):
        """Emergency stop all trading."""
        logger.critical(f"🚨 EMERGENCY STOP: {reason}")
        
        # Close all positions
        await self._close_all_positions(f"Emergency stop: {reason}")
        
        # Pause the engine
        await self.pause()
        
        # Notify error handlers
        for handler in self.error_handlers:
            try:
                await handler(f"Emergency stop: {reason}")
            except Exception as e:
                logger.error(f"Error in emergency stop handler: {e}")
    
    async def _get_market_data(self) -> Dict[TimeFrame, Dict[str, np.ndarray]]:
        """Get market data for all timeframes."""
        try:
            # Get data for all configured symbols and timeframes
            from .config import get_config
            config = get_config()

            market_data = {}

            # For now, get data for the first supported symbol
            # In production, this should be configurable
            if config.supported_symbols:
                symbol = config.supported_symbols[0]

                lookback_periods = {
                    TimeFrame.M15: 200,
                    TimeFrame.H1: 200,
                    TimeFrame.H4: 200,
                    TimeFrame.D1: 200
                }

                multi_tf_data = await self.data_provider.get_multi_timeframe_data(
                    symbol, config.default_timeframes, lookback_periods
                )

                for timeframe, historical_data in multi_tf_data.items():
                    market_data[timeframe] = historical_data.data

            return market_data

        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return {}

    async def _get_market_data_for_symbol(self, symbol: str) -> Dict[str, np.ndarray]:
        """Get market data for specific symbol."""
        try:
            from datetime import datetime, timedelta

            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=30)  # 30 days of data

            historical_data = await self.data_provider.get_historical_data(
                symbol, TimeFrame.H1, start_time, end_time, 720  # 30 days * 24 hours
            )

            return historical_data.data

        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return {}

    async def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for symbol."""
        try:
            return await self.data_provider.get_current_price(symbol)
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            return None
    
    async def _validate_order(self, order: TradingOrder) -> Dict[str, Any]:
        """Validate order before submission."""
        # Basic validation
        if order.quantity <= 0:
            return {'valid': False, 'reason': 'Invalid quantity'}

        # TODO: Add proper symbol validation against your broker's supported symbols
        # TODO: Add balance validation
        # TODO: Add market hours validation
        # TODO: Add minimum order size validation

        return {'valid': True}
    
    async def _handle_error(self, error: Exception):
        """Handle engine errors."""
        logger.error(f"Trading engine error: {error}")
        
        # Notify error handlers
        for handler in self.error_handlers:
            try:
                await handler(error)
            except Exception as e:
                logger.error(f"Error in error handler: {e}")
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get comprehensive engine status."""
        return {
            'state': self.state.value,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'last_signal_time': self.last_signal_time.isoformat() if self.last_signal_time else None,
            'portfolio_value': float(self.portfolio_value),
            'cash_balance': float(self.cash_balance),
            'total_pnl': float(self.total_pnl),
            'daily_pnl': float(self.daily_pnl),
            'daily_trades': self.daily_trades,
            'open_positions': len(self.positions),
            'pending_orders': len([o for o in self.orders.values() if o.status == OrderStatus.SUBMITTED]),
            'strategies_loaded': len(self.signal_engine.strategies)
        }
    
    def add_order_fill_handler(self, handler: Callable):
        """Add order fill event handler."""
        self.order_fill_handlers.append(handler)
    
    def add_position_update_handler(self, handler: Callable):
        """Add position update event handler."""
        self.position_update_handlers.append(handler)
    
    def add_error_handler(self, handler: Callable):
        """Add error event handler."""
        self.error_handlers.append(handler)

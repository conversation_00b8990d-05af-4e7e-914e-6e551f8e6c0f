"""
Market Data Provider Interface
Abstract interface for market data providers with concrete implementations
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import numpy as np
from dataclasses import dataclass
from enum import Enum

from .signal_engine import TimeFrame
from .config import DataProviderConfig
from ...shared.logging_config import setup_logging

logger = setup_logging("data_provider")


@dataclass
class MarketData:
    """Market data structure."""
    symbol: str
    timeframe: TimeFrame
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float


@dataclass
class HistoricalData:
    """Historical market data structure."""
    symbol: str
    timeframe: TimeFrame
    data: Dict[str, np.ndarray]  # 'open', 'high', 'low', 'close', 'volume'
    start_time: datetime
    end_time: datetime


class DataProviderError(Exception):
    """Base exception for data provider errors."""
    pass


class DataProviderInterface(ABC):
    """Abstract interface for market data providers."""
    
    def __init__(self, config: DataProviderConfig):
        self.config = config
        self._cache: Dict[str, Any] = {}
        self._last_request_time: Dict[str, datetime] = {}
    
    @abstractmethod
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for symbol."""
        pass
    
    @abstractmethod
    async def get_historical_data(
        self, 
        symbol: str, 
        timeframe: TimeFrame, 
        start_time: datetime, 
        end_time: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> HistoricalData:
        """Get historical OHLCV data."""
        pass
    
    @abstractmethod
    async def get_multi_timeframe_data(
        self, 
        symbol: str, 
        timeframes: List[TimeFrame],
        lookback_periods: Dict[TimeFrame, int]
    ) -> Dict[TimeFrame, HistoricalData]:
        """Get data for multiple timeframes."""
        pass
    
    @abstractmethod
    async def get_supported_symbols(self) -> List[str]:
        """Get list of supported trading symbols."""
        pass
    
    @abstractmethod
    async def validate_symbol(self, symbol: str) -> bool:
        """Validate if symbol is supported."""
        pass
    
    def _check_rate_limit(self, endpoint: str) -> bool:
        """Check if request is within rate limits."""
        now = datetime.utcnow()
        last_request = self._last_request_time.get(endpoint)
        
        if last_request:
            time_diff = (now - last_request).total_seconds()
            min_interval = 60.0 / self.config.rate_limit_requests_per_minute
            
            if time_diff < min_interval:
                return False
        
        self._last_request_time[endpoint] = now
        return True
    
    def _get_cache_key(self, symbol: str, timeframe: TimeFrame, **kwargs) -> str:
        """Generate cache key for data."""
        key_parts = [symbol, timeframe.value]
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}:{v}")
        return "|".join(key_parts)
    
    def _is_cache_valid(self, cache_key: str, ttl_seconds: int = 300) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self._cache:
            return False
        
        cached_time = self._cache[cache_key].get('timestamp')
        if not cached_time:
            return False
        
        age = (datetime.utcnow() - cached_time).total_seconds()
        return age < ttl_seconds


class BinanceDataProvider(DataProviderInterface):
    """Binance data provider implementation."""
    
    def __init__(self, config: DataProviderConfig):
        super().__init__(config)
        self.base_url = config.base_url or "https://api.binance.com"
        
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price from Binance."""
        try:
            # TODO: Implement actual Binance API call
            # Example implementation:
            # import aiohttp
            # async with aiohttp.ClientSession() as session:
            #     url = f"{self.base_url}/api/v3/ticker/price"
            #     params = {"symbol": symbol}
            #     async with session.get(url, params=params) as response:
            #         data = await response.json()
            #         return float(data["price"])
            
            logger.warning("Binance API integration not implemented")
            return None
            
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            return None
    
    async def get_historical_data(
        self, 
        symbol: str, 
        timeframe: TimeFrame, 
        start_time: datetime, 
        end_time: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> HistoricalData:
        """Get historical data from Binance."""
        try:
            # TODO: Implement actual Binance API call
            # Example implementation:
            # import aiohttp
            # async with aiohttp.ClientSession() as session:
            #     url = f"{self.base_url}/api/v3/klines"
            #     params = {
            #         "symbol": symbol,
            #         "interval": self._timeframe_to_binance_interval(timeframe),
            #         "startTime": int(start_time.timestamp() * 1000),
            #         "limit": limit or 1000
            #     }
            #     if end_time:
            #         params["endTime"] = int(end_time.timestamp() * 1000)
            #     
            #     async with session.get(url, params=params) as response:
            #         klines = await response.json()
            #         return self._parse_binance_klines(symbol, timeframe, klines)
            
            logger.warning("Binance historical data API integration not implemented")
            raise DataProviderError("Binance API integration required")
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            raise DataProviderError(f"Failed to fetch historical data: {e}")
    
    async def get_multi_timeframe_data(
        self, 
        symbol: str, 
        timeframes: List[TimeFrame],
        lookback_periods: Dict[TimeFrame, int]
    ) -> Dict[TimeFrame, HistoricalData]:
        """Get multi-timeframe data from Binance."""
        result = {}
        
        for timeframe in timeframes:
            try:
                periods = lookback_periods.get(timeframe, 100)
                end_time = datetime.utcnow()
                start_time = end_time - timedelta(hours=periods)  # Simplified calculation
                
                data = await self.get_historical_data(
                    symbol, timeframe, start_time, end_time, periods
                )
                result[timeframe] = data
                
            except Exception as e:
                logger.error(f"Error getting {timeframe.value} data for {symbol}: {e}")
        
        return result
    
    async def get_supported_symbols(self) -> List[str]:
        """Get supported symbols from Binance."""
        try:
            # TODO: Implement actual Binance API call
            # Example implementation:
            # import aiohttp
            # async with aiohttp.ClientSession() as session:
            #     url = f"{self.base_url}/api/v3/exchangeInfo"
            #     async with session.get(url) as response:
            #         data = await response.json()
            #         return [s["symbol"] for s in data["symbols"] if s["status"] == "TRADING"]
            
            # Return common symbols for now
            return [
                'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT',
                'LTCUSDT', 'BCHUSDT', 'XLMUSDT', 'EOSUSDT', 'TRXUSDT'
            ]
            
        except Exception as e:
            logger.error(f"Error getting supported symbols: {e}")
            return []
    
    async def validate_symbol(self, symbol: str) -> bool:
        """Validate symbol with Binance."""
        supported_symbols = await self.get_supported_symbols()
        return symbol in supported_symbols
    
    def _timeframe_to_binance_interval(self, timeframe: TimeFrame) -> str:
        """Convert TimeFrame to Binance interval string."""
        mapping = {
            TimeFrame.M1: "1m",
            TimeFrame.M5: "5m",
            TimeFrame.M15: "15m",
            TimeFrame.M30: "30m",
            TimeFrame.H1: "1h",
            TimeFrame.H4: "4h",
            TimeFrame.D1: "1d",
            TimeFrame.W1: "1w"
        }
        return mapping.get(timeframe, "1h")
    
    def _parse_binance_klines(self, symbol: str, timeframe: TimeFrame, klines: List) -> HistoricalData:
        """Parse Binance klines data."""
        if not klines:
            raise DataProviderError("No klines data received")
        
        # Binance klines format: [timestamp, open, high, low, close, volume, ...]
        opens = []
        highs = []
        lows = []
        closes = []
        volumes = []
        
        for kline in klines:
            opens.append(float(kline[1]))
            highs.append(float(kline[2]))
            lows.append(float(kline[3]))
            closes.append(float(kline[4]))
            volumes.append(float(kline[5]))
        
        data = {
            'open': np.array(opens),
            'high': np.array(highs),
            'low': np.array(lows),
            'close': np.array(closes),
            'volume': np.array(volumes)
        }
        
        start_time = datetime.fromtimestamp(klines[0][0] / 1000)
        end_time = datetime.fromtimestamp(klines[-1][0] / 1000)
        
        return HistoricalData(
            symbol=symbol,
            timeframe=timeframe,
            data=data,
            start_time=start_time,
            end_time=end_time
        )


class PaperDataProvider(DataProviderInterface):
    """Paper trading data provider with simulated data."""
    
    def __init__(self, config: DataProviderConfig):
        super().__init__(config)
        self._simulated_prices: Dict[str, float] = {}
        self._price_trends: Dict[str, float] = {}
        
        # Initialize with some base prices
        base_prices = {
            'BTCUSDT': 45000.0,
            'ETHUSDT': 3000.0,
            'ADAUSDT': 1.2,
            'DOTUSDT': 25.0,
            'LINKUSDT': 15.0
        }
        
        for symbol, price in base_prices.items():
            self._simulated_prices[symbol] = price
            self._price_trends[symbol] = 0.0  # No initial trend
    
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get simulated current price."""
        if symbol not in self._simulated_prices:
            return None
        
        # Simulate price movement
        current_price = self._simulated_prices[symbol]
        
        # Add some random movement (±0.5%)
        import random
        movement = random.uniform(-0.005, 0.005)
        new_price = current_price * (1 + movement)
        
        self._simulated_prices[symbol] = new_price
        return new_price
    
    async def get_historical_data(
        self, 
        symbol: str, 
        timeframe: TimeFrame, 
        start_time: datetime, 
        end_time: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> HistoricalData:
        """Generate simulated historical data."""
        if symbol not in self._simulated_prices:
            raise DataProviderError(f"Symbol {symbol} not supported in paper trading")
        
        # Generate realistic OHLCV data
        base_price = self._simulated_prices[symbol]
        periods = limit or 100
        
        # Generate price series with some trend and volatility
        np.random.seed(hash(symbol) % 2**32)  # Consistent data for same symbol
        
        prices = []
        current_price = base_price
        
        for i in range(periods):
            # Add trend and noise
            trend = 0.0001 * (i - periods/2)  # Slight trend
            noise = np.random.normal(0, 0.02)  # 2% volatility
            
            price_change = trend + noise
            current_price *= (1 + price_change)
            prices.append(current_price)
        
        # Create OHLC from price series
        opens = prices[:-1] + [prices[-1]]
        closes = prices
        highs = [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices]
        lows = [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices]
        volumes = [np.random.uniform(1000, 10000) for _ in range(periods)]
        
        data = {
            'open': np.array(opens),
            'high': np.array(highs),
            'low': np.array(lows),
            'close': np.array(closes),
            'volume': np.array(volumes)
        }
        
        end_time = end_time or datetime.utcnow()
        start_time = start_time or (end_time - timedelta(hours=periods))
        
        return HistoricalData(
            symbol=symbol,
            timeframe=timeframe,
            data=data,
            start_time=start_time,
            end_time=end_time
        )
    
    async def get_multi_timeframe_data(
        self, 
        symbol: str, 
        timeframes: List[TimeFrame],
        lookback_periods: Dict[TimeFrame, int]
    ) -> Dict[TimeFrame, HistoricalData]:
        """Get multi-timeframe simulated data."""
        result = {}
        
        for timeframe in timeframes:
            periods = lookback_periods.get(timeframe, 100)
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(hours=periods)
            
            data = await self.get_historical_data(
                symbol, timeframe, start_time, end_time, periods
            )
            result[timeframe] = data
        
        return result
    
    async def get_supported_symbols(self) -> List[str]:
        """Get supported symbols for paper trading."""
        return list(self._simulated_prices.keys())
    
    async def validate_symbol(self, symbol: str) -> bool:
        """Validate symbol for paper trading."""
        return symbol in self._simulated_prices


def create_data_provider(config: DataProviderConfig) -> DataProviderInterface:
    """Factory function to create appropriate data provider."""
    if config.provider.lower() == "binance":
        return BinanceDataProvider(config)
    elif config.provider.lower() == "paper":
        return PaperDataProvider(config)
    else:
        raise ValueError(f"Unsupported data provider: {config.provider}")


# Global data provider instance
_data_provider: Optional[DataProviderInterface] = None


def get_data_provider() -> DataProviderInterface:
    """Get the global data provider instance."""
    global _data_provider
    if _data_provider is None:
        from .config import get_config
        config = get_config()
        _data_provider = create_data_provider(config.data_provider)
    
    return _data_provider


def set_data_provider(provider: DataProviderInterface):
    """Set the global data provider instance."""
    global _data_provider
    _data_provider = provider

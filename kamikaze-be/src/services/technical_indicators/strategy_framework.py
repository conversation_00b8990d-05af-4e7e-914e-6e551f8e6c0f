"""
Multi-Indicator Strategy Framework
Flexible framework for combining multiple technical indicators with logical operators
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union, Callable
from decimal import Decimal
from dataclasses import dataclass, field
from enum import Enum
import json
from datetime import datetime

from .indicators_library import TechnicalIndicatorsLibrary, IndicatorResult, MultiValueIndicatorResult
from ...shared.logging_config import setup_logging

logger = setup_logging("strategy_framework")


class LogicalOperator(Enum):
    """Logical operators for combining conditions."""
    AND = "AND"
    OR = "OR"
    NOT = "NOT"


class ComparisonOperator(Enum):
    """Comparison operators for indicator conditions."""
    GREATER_THAN = ">"
    LESS_THAN = "<"
    GREATER_EQUAL = ">="
    LESS_EQUAL = "<="
    EQUAL = "=="
    NOT_EQUAL = "!="
    CROSSES_ABOVE = "CROSSES_ABOVE"
    CROSSES_BELOW = "CROSSES_BELOW"
    BETWEEN = "BETWEEN"
    OUTSIDE = "OUTSIDE"


class SignalType(Enum):
    """Types of trading signals."""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    CLOSE_LONG = "CLOSE_LONG"
    CLOSE_SHORT = "CLOSE_SHORT"


@dataclass
class IndicatorCondition:
    """Single indicator condition for strategy evaluation."""
    indicator_name: str
    indicator_params: Dict[str, Any]
    comparison_operator: ComparisonOperator
    threshold_value: Optional[float] = None
    threshold_value_2: Optional[float] = None  # For BETWEEN/OUTSIDE operators
    indicator_field: Optional[str] = None  # For multi-value indicators
    weight: float = 1.0
    description: str = ""
    
    def __post_init__(self):
        if not self.description:
            self.description = f"{self.indicator_name} {self.comparison_operator.value}"
            if self.threshold_value is not None:
                self.description += f" {self.threshold_value}"


@dataclass
class ConditionGroup:
    """Group of conditions combined with logical operators."""
    conditions: List[Union[IndicatorCondition, 'ConditionGroup']]
    logical_operator: LogicalOperator = LogicalOperator.AND
    weight: float = 1.0
    description: str = ""


@dataclass
class StrategyRule:
    """Complete strategy rule with entry/exit conditions."""
    name: str
    entry_conditions: ConditionGroup
    exit_conditions: Optional[ConditionGroup] = None
    signal_type: SignalType = SignalType.BUY
    min_confidence: float = 0.5
    max_holding_period: Optional[int] = None  # In bars
    stop_loss_pct: Optional[float] = None
    take_profit_pct: Optional[float] = None
    description: str = ""


@dataclass
class SignalResult:
    """Result of signal evaluation."""
    signal_type: SignalType
    confidence: float
    strength: float
    timestamp: datetime
    conditions_met: List[str]
    conditions_failed: List[str]
    indicator_values: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)


class MultiIndicatorStrategy:
    """
    Multi-indicator strategy framework for combining technical indicators.
    
    Features:
    - Flexible condition building with logical operators
    - Signal strength calculation based on indicator convergence
    - Multi-timeframe analysis support
    - Custom weighting for indicators
    - Signal filtering and validation
    """
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.indicators_library = TechnicalIndicatorsLibrary()
        self.rules: List[StrategyRule] = []
        self.indicator_cache: Dict[str, Any] = {}
        self.historical_signals: List[SignalResult] = []
        
    def add_rule(self, rule: StrategyRule):
        """Add a strategy rule."""
        self.rules.append(rule)
        logger.info(f"Added rule '{rule.name}' to strategy '{self.name}'")
    
    def evaluate_condition(self, condition: IndicatorCondition, 
                          data: Dict[str, np.ndarray], 
                          current_index: int = -1) -> Tuple[bool, float, Dict[str, Any]]:
        """
        Evaluate a single indicator condition.
        
        Returns:
            (condition_met, confidence, indicator_values)
        """
        try:
            # Calculate indicator if not cached
            cache_key = f"{condition.indicator_name}_{json.dumps(condition.indicator_params, sort_keys=True)}"
            
            if cache_key not in self.indicator_cache:
                indicator_result = self.indicators_library.calculate_indicator(
                    condition.indicator_name, data, **condition.indicator_params
                )
                self.indicator_cache[cache_key] = indicator_result
            else:
                indicator_result = self.indicator_cache[cache_key]
            
            # Extract indicator value(s)
            if isinstance(indicator_result, MultiValueIndicatorResult):
                if condition.indicator_field:
                    if condition.indicator_field not in indicator_result.values:
                        raise ValueError(f"Field '{condition.indicator_field}' not found in {condition.indicator_name}")
                    current_value = indicator_result.values[condition.indicator_field][current_index]
                    prev_value = indicator_result.values[condition.indicator_field][current_index - 1] if current_index > 0 else None
                else:
                    # Use first available field
                    field_name = list(indicator_result.values.keys())[0]
                    current_value = indicator_result.values[field_name][current_index]
                    prev_value = indicator_result.values[field_name][current_index - 1] if current_index > 0 else None
                
                indicator_values = {k: v[current_index] for k, v in indicator_result.values.items()}
            else:
                current_value = indicator_result.values[current_index]
                prev_value = indicator_result.values[current_index - 1] if current_index > 0 else None
                indicator_values = {condition.indicator_name: current_value}
            
            # Handle NaN values
            if np.isnan(current_value):
                return False, 0.0, indicator_values
            
            # Evaluate condition
            condition_met = False
            confidence = 0.0
            
            if condition.comparison_operator == ComparisonOperator.GREATER_THAN:
                condition_met = current_value > condition.threshold_value
                confidence = min(1.0, (current_value - condition.threshold_value) / abs(condition.threshold_value) * 2)
                
            elif condition.comparison_operator == ComparisonOperator.LESS_THAN:
                condition_met = current_value < condition.threshold_value
                confidence = min(1.0, (condition.threshold_value - current_value) / abs(condition.threshold_value) * 2)
                
            elif condition.comparison_operator == ComparisonOperator.GREATER_EQUAL:
                condition_met = current_value >= condition.threshold_value
                confidence = 1.0 if condition_met else 0.0
                
            elif condition.comparison_operator == ComparisonOperator.LESS_EQUAL:
                condition_met = current_value <= condition.threshold_value
                confidence = 1.0 if condition_met else 0.0
                
            elif condition.comparison_operator == ComparisonOperator.EQUAL:
                tolerance = abs(condition.threshold_value * 0.01)  # 1% tolerance
                condition_met = abs(current_value - condition.threshold_value) <= tolerance
                confidence = 1.0 if condition_met else 0.0
                
            elif condition.comparison_operator == ComparisonOperator.CROSSES_ABOVE:
                if prev_value is not None:
                    condition_met = prev_value <= condition.threshold_value < current_value
                    confidence = 1.0 if condition_met else 0.0
                
            elif condition.comparison_operator == ComparisonOperator.CROSSES_BELOW:
                if prev_value is not None:
                    condition_met = prev_value >= condition.threshold_value > current_value
                    confidence = 1.0 if condition_met else 0.0
                
            elif condition.comparison_operator == ComparisonOperator.BETWEEN:
                if condition.threshold_value_2 is not None:
                    min_val = min(condition.threshold_value, condition.threshold_value_2)
                    max_val = max(condition.threshold_value, condition.threshold_value_2)
                    condition_met = min_val <= current_value <= max_val
                    confidence = 1.0 if condition_met else 0.0
                
            elif condition.comparison_operator == ComparisonOperator.OUTSIDE:
                if condition.threshold_value_2 is not None:
                    min_val = min(condition.threshold_value, condition.threshold_value_2)
                    max_val = max(condition.threshold_value, condition.threshold_value_2)
                    condition_met = current_value < min_val or current_value > max_val
                    confidence = 1.0 if condition_met else 0.0
            
            # Apply weight to confidence
            confidence = max(0.0, min(1.0, confidence * condition.weight))
            
            return condition_met, confidence, indicator_values
            
        except Exception as e:
            logger.error(f"Error evaluating condition {condition.description}: {e}")
            return False, 0.0, {}
    
    def evaluate_condition_group(self, group: ConditionGroup, 
                                data: Dict[str, np.ndarray], 
                                current_index: int = -1) -> Tuple[bool, float, List[str], List[str], Dict[str, Any]]:
        """
        Evaluate a group of conditions with logical operators.
        
        Returns:
            (group_met, confidence, conditions_met, conditions_failed, all_indicator_values)
        """
        conditions_met = []
        conditions_failed = []
        all_indicator_values = {}
        confidences = []
        results = []
        
        for condition in group.conditions:
            if isinstance(condition, IndicatorCondition):
                met, conf, values = self.evaluate_condition(condition, data, current_index)
                results.append(met)
                confidences.append(conf)
                all_indicator_values.update(values)
                
                if met:
                    conditions_met.append(condition.description)
                else:
                    conditions_failed.append(condition.description)
                    
            elif isinstance(condition, ConditionGroup):
                met, conf, sub_met, sub_failed, sub_values = self.evaluate_condition_group(
                    condition, data, current_index
                )
                results.append(met)
                confidences.append(conf)
                all_indicator_values.update(sub_values)
                conditions_met.extend(sub_met)
                conditions_failed.extend(sub_failed)
        
        # Apply logical operator
        if group.logical_operator == LogicalOperator.AND:
            group_met = all(results)
            # For AND, confidence is the minimum of all confidences
            group_confidence = min(confidences) if confidences else 0.0
            
        elif group.logical_operator == LogicalOperator.OR:
            group_met = any(results)
            # For OR, confidence is the maximum of all confidences
            group_confidence = max(confidences) if confidences else 0.0
            
        elif group.logical_operator == LogicalOperator.NOT:
            # NOT operator applies to the first condition only
            if results:
                group_met = not results[0]
                group_confidence = 1.0 - confidences[0] if confidences else 0.0
            else:
                group_met = False
                group_confidence = 0.0
        else:
            group_met = False
            group_confidence = 0.0
        
        # Apply group weight
        group_confidence = max(0.0, min(1.0, group_confidence * group.weight))
        
        return group_met, group_confidence, conditions_met, conditions_failed, all_indicator_values
    
    def generate_signals(self, data: Dict[str, np.ndarray], 
                        current_index: int = -1) -> List[SignalResult]:
        """Generate trading signals based on strategy rules."""
        signals = []
        
        # Clear cache for fresh calculations
        self.indicator_cache.clear()
        
        for rule in self.rules:
            try:
                # Evaluate entry conditions
                entry_met, entry_confidence, met_conditions, failed_conditions, indicator_values = \
                    self.evaluate_condition_group(rule.entry_conditions, data, current_index)
                
                if entry_met and entry_confidence >= rule.min_confidence:
                    # Calculate signal strength based on confidence and number of conditions met
                    total_conditions = len(met_conditions) + len(failed_conditions)
                    condition_ratio = len(met_conditions) / total_conditions if total_conditions > 0 else 0
                    signal_strength = (entry_confidence + condition_ratio) / 2
                    
                    signal = SignalResult(
                        signal_type=rule.signal_type,
                        confidence=entry_confidence,
                        strength=signal_strength,
                        timestamp=datetime.utcnow(),
                        conditions_met=met_conditions,
                        conditions_failed=failed_conditions,
                        indicator_values=indicator_values,
                        metadata={
                            'rule_name': rule.name,
                            'stop_loss_pct': rule.stop_loss_pct,
                            'take_profit_pct': rule.take_profit_pct,
                            'max_holding_period': rule.max_holding_period
                        }
                    )
                    
                    signals.append(signal)
                    logger.debug(f"Generated {rule.signal_type.value} signal with confidence {entry_confidence:.3f}")
                
                # Evaluate exit conditions if they exist
                if rule.exit_conditions:
                    exit_met, exit_confidence, exit_met_conditions, exit_failed_conditions, exit_indicator_values = \
                        self.evaluate_condition_group(rule.exit_conditions, data, current_index)
                    
                    if exit_met and exit_confidence >= rule.min_confidence:
                        exit_signal_type = SignalType.CLOSE_LONG if rule.signal_type == SignalType.BUY else SignalType.CLOSE_SHORT
                        
                        exit_signal = SignalResult(
                            signal_type=exit_signal_type,
                            confidence=exit_confidence,
                            strength=exit_confidence,
                            timestamp=datetime.utcnow(),
                            conditions_met=exit_met_conditions,
                            conditions_failed=exit_failed_conditions,
                            indicator_values=exit_indicator_values,
                            metadata={
                                'rule_name': f"{rule.name}_EXIT",
                                'exit_rule': True
                            }
                        )
                        
                        signals.append(exit_signal)
                        logger.debug(f"Generated {exit_signal_type.value} signal with confidence {exit_confidence:.3f}")
                        
            except Exception as e:
                logger.error(f"Error evaluating rule '{rule.name}': {e}")
        
        # Store signals in history
        self.historical_signals.extend(signals)
        
        # Sort signals by strength (highest first)
        signals.sort(key=lambda x: x.strength, reverse=True)
        
        return signals
    
    def get_strategy_summary(self) -> Dict[str, Any]:
        """Get comprehensive strategy summary."""
        return {
            'name': self.name,
            'description': self.description,
            'total_rules': len(self.rules),
            'rules': [
                {
                    'name': rule.name,
                    'signal_type': rule.signal_type.value,
                    'min_confidence': rule.min_confidence,
                    'description': rule.description,
                    'has_exit_conditions': rule.exit_conditions is not None
                }
                for rule in self.rules
            ],
            'total_signals_generated': len(self.historical_signals),
            'signal_types_count': {
                signal_type.value: len([s for s in self.historical_signals if s.signal_type == signal_type])
                for signal_type in SignalType
            }
        }
    
    def clear_history(self):
        """Clear historical signals and cache."""
        self.historical_signals.clear()
        self.indicator_cache.clear()
        logger.info(f"Cleared history for strategy '{self.name}'")
    
    def export_strategy(self) -> Dict[str, Any]:
        """Export strategy configuration to dictionary."""
        def serialize_condition(condition):
            if isinstance(condition, IndicatorCondition):
                return {
                    'type': 'condition',
                    'indicator_name': condition.indicator_name,
                    'indicator_params': condition.indicator_params,
                    'comparison_operator': condition.comparison_operator.value,
                    'threshold_value': condition.threshold_value,
                    'threshold_value_2': condition.threshold_value_2,
                    'indicator_field': condition.indicator_field,
                    'weight': condition.weight,
                    'description': condition.description
                }
            elif isinstance(condition, ConditionGroup):
                return {
                    'type': 'group',
                    'conditions': [serialize_condition(c) for c in condition.conditions],
                    'logical_operator': condition.logical_operator.value,
                    'weight': condition.weight,
                    'description': condition.description
                }
        
        return {
            'name': self.name,
            'description': self.description,
            'rules': [
                {
                    'name': rule.name,
                    'entry_conditions': serialize_condition(rule.entry_conditions),
                    'exit_conditions': serialize_condition(rule.exit_conditions) if rule.exit_conditions else None,
                    'signal_type': rule.signal_type.value,
                    'min_confidence': rule.min_confidence,
                    'max_holding_period': rule.max_holding_period,
                    'stop_loss_pct': rule.stop_loss_pct,
                    'take_profit_pct': rule.take_profit_pct,
                    'description': rule.description
                }
                for rule in self.rules
            ]
        }
    
    @classmethod
    def import_strategy(cls, config: Dict[str, Any]) -> 'MultiIndicatorStrategy':
        """Import strategy from configuration dictionary."""
        strategy = cls(config['name'], config.get('description', ''))
        
        def deserialize_condition(condition_data):
            if condition_data['type'] == 'condition':
                return IndicatorCondition(
                    indicator_name=condition_data['indicator_name'],
                    indicator_params=condition_data['indicator_params'],
                    comparison_operator=ComparisonOperator(condition_data['comparison_operator']),
                    threshold_value=condition_data.get('threshold_value'),
                    threshold_value_2=condition_data.get('threshold_value_2'),
                    indicator_field=condition_data.get('indicator_field'),
                    weight=condition_data.get('weight', 1.0),
                    description=condition_data.get('description', '')
                )
            elif condition_data['type'] == 'group':
                return ConditionGroup(
                    conditions=[deserialize_condition(c) for c in condition_data['conditions']],
                    logical_operator=LogicalOperator(condition_data['logical_operator']),
                    weight=condition_data.get('weight', 1.0),
                    description=condition_data.get('description', '')
                )
        
        for rule_data in config['rules']:
            rule = StrategyRule(
                name=rule_data['name'],
                entry_conditions=deserialize_condition(rule_data['entry_conditions']),
                exit_conditions=deserialize_condition(rule_data['exit_conditions']) if rule_data.get('exit_conditions') else None,
                signal_type=SignalType(rule_data['signal_type']),
                min_confidence=rule_data.get('min_confidence', 0.5),
                max_holding_period=rule_data.get('max_holding_period'),
                stop_loss_pct=rule_data.get('stop_loss_pct'),
                take_profit_pct=rule_data.get('take_profit_pct'),
                description=rule_data.get('description', '')
            )
            strategy.add_rule(rule)
        
        return strategy

"""
Strategy Builder Interface
Intuitive interface for creating and managing technical indicator strategies
"""

import logging
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import json

from .strategy_framework import (
    MultiIndicatorStrategy, IndicatorCondition, ConditionGroup, StrategyRule,
    LogicalOperator, ComparisonOperator, SignalType
)
from .indicators_library import TechnicalIndicatorsLibrary, IndicatorType
from ...shared.logging_config import setup_logging

logger = setup_logging("strategy_builder")


@dataclass
class StrategyTemplate:
    """Pre-built strategy template."""
    name: str
    description: str
    category: str
    indicators_used: List[str]
    complexity_level: str  # "Beginner", "Intermediate", "Advanced"
    expected_performance: Dict[str, Any]
    config: Dict[str, Any]


class StrategyBuilder:
    """
    Intuitive interface for building technical indicator strategies.
    
    Features:
    - Visual strategy construction
    - Pre-built templates
    - Parameter validation
    - Strategy testing and optimization
    - Export/import capabilities
    """
    
    def __init__(self):
        self.indicators_library = TechnicalIndicatorsLibrary()
        self.available_indicators = self.indicators_library.get_all_available_indicators()
        self.strategy_templates = self._load_strategy_templates()
        
    def _load_strategy_templates(self) -> Dict[str, StrategyTemplate]:
        """Load pre-built strategy templates."""
        templates = {}
        
        # RSI Oversold/Overbought Strategy
        templates["rsi_mean_reversion"] = StrategyTemplate(
            name="RSI Mean Reversion",
            description="Buy when RSI is oversold (<30), sell when overbought (>70)",
            category="Mean Reversion",
            indicators_used=["RSI"],
            complexity_level="Beginner",
            expected_performance={
                "win_rate": "60-70%",
                "avg_trade_duration": "2-5 days",
                "best_markets": "Range-bound markets"
            },
            config={
                "entry_conditions": {
                    "buy": {"RSI": {"operator": "<", "value": 30}},
                    "sell": {"RSI": {"operator": ">", "value": 70}}
                },
                "exit_conditions": {
                    "buy_exit": {"RSI": {"operator": ">", "value": 50}},
                    "sell_exit": {"RSI": {"operator": "<", "value": 50}}
                }
            }
        )
        
        # MACD Crossover Strategy
        templates["macd_crossover"] = StrategyTemplate(
            name="MACD Crossover",
            description="Buy when MACD crosses above signal line, sell when crosses below",
            category="Trend Following",
            indicators_used=["MACD"],
            complexity_level="Beginner",
            expected_performance={
                "win_rate": "45-55%",
                "avg_trade_duration": "1-3 weeks",
                "best_markets": "Trending markets"
            },
            config={
                "entry_conditions": {
                    "buy": {"MACD": {"field": "macd", "operator": "CROSSES_ABOVE", "reference_field": "signal"}},
                    "sell": {"MACD": {"field": "macd", "operator": "CROSSES_BELOW", "reference_field": "signal"}}
                }
            }
        )
        
        # Bollinger Bands Squeeze Strategy
        templates["bollinger_squeeze"] = StrategyTemplate(
            name="Bollinger Bands Squeeze",
            description="Trade breakouts from low volatility periods",
            category="Volatility Breakout",
            indicators_used=["BBANDS", "ATR"],
            complexity_level="Intermediate",
            expected_performance={
                "win_rate": "50-60%",
                "avg_trade_duration": "3-7 days",
                "best_markets": "All market conditions"
            },
            config={
                "entry_conditions": {
                    "buy": {
                        "AND": [
                            {"CLOSE": {"operator": ">", "reference": "BBANDS.upper"}},
                            {"ATR": {"operator": "<", "value": "ATR_SMA_20 * 0.8"}}
                        ]
                    }
                }
            }
        )
        
        # Multi-Timeframe Trend Strategy
        templates["multi_timeframe_trend"] = StrategyTemplate(
            name="Multi-Timeframe Trend",
            description="Align multiple timeframes for high-probability trend trades",
            category="Multi-Timeframe",
            indicators_used=["EMA", "ADX", "RSI"],
            complexity_level="Advanced",
            expected_performance={
                "win_rate": "55-65%",
                "avg_trade_duration": "1-4 weeks",
                "best_markets": "Strong trending markets"
            },
            config={
                "entry_conditions": {
                    "buy": {
                        "AND": [
                            {"EMA_20": {"operator": ">", "reference": "EMA_50"}},
                            {"ADX": {"operator": ">", "value": 25}},
                            {"RSI": {"operator": "BETWEEN", "value": [40, 60]}}
                        ]
                    }
                }
            }
        )
        
        return templates
    
    def get_available_indicators(self) -> Dict[str, Dict[str, Any]]:
        """Get list of available indicators with their specifications."""
        return self.available_indicators
    
    def get_strategy_templates(self) -> Dict[str, StrategyTemplate]:
        """Get available strategy templates."""
        return self.strategy_templates
    
    def create_strategy_from_template(self, template_name: str, 
                                    custom_params: Optional[Dict[str, Any]] = None) -> MultiIndicatorStrategy:
        """Create a strategy from a template with optional customization."""
        if template_name not in self.strategy_templates:
            raise ValueError(f"Template '{template_name}' not found")
        
        template = self.strategy_templates[template_name]
        strategy = MultiIndicatorStrategy(template.name, template.description)
        
        # Apply custom parameters if provided
        config = template.config.copy()
        if custom_params:
            config.update(custom_params)
        
        # Build strategy rules from template config
        self._build_rules_from_config(strategy, config)
        
        return strategy
    
    def create_custom_strategy(self, name: str, description: str = "") -> MultiIndicatorStrategy:
        """Create a new empty strategy for custom building."""
        return MultiIndicatorStrategy(name, description)
    
    def add_indicator_condition(self, strategy: MultiIndicatorStrategy,
                              indicator_name: str,
                              indicator_params: Dict[str, Any],
                              comparison_operator: str,
                              threshold_value: Optional[float] = None,
                              threshold_value_2: Optional[float] = None,
                              indicator_field: Optional[str] = None,
                              weight: float = 1.0,
                              description: str = "") -> IndicatorCondition:
        """Add an indicator condition to strategy building."""
        # Validate indicator
        if indicator_name.upper() not in self.available_indicators:
            raise ValueError(f"Unknown indicator: {indicator_name}")
        
        # Validate parameters
        indicator_spec = self.available_indicators[indicator_name.upper()]
        self._validate_indicator_params(indicator_params, indicator_spec['parameters'])
        
        # Create condition
        condition = IndicatorCondition(
            indicator_name=indicator_name.upper(),
            indicator_params=indicator_params,
            comparison_operator=ComparisonOperator(comparison_operator),
            threshold_value=threshold_value,
            threshold_value_2=threshold_value_2,
            indicator_field=indicator_field,
            weight=weight,
            description=description
        )
        
        return condition
    
    def create_condition_group(self, conditions: List[Union[IndicatorCondition, ConditionGroup]],
                             logical_operator: str = "AND",
                             weight: float = 1.0,
                             description: str = "") -> ConditionGroup:
        """Create a group of conditions with logical operators."""
        return ConditionGroup(
            conditions=conditions,
            logical_operator=LogicalOperator(logical_operator),
            weight=weight,
            description=description
        )
    
    def add_strategy_rule(self, strategy: MultiIndicatorStrategy,
                         rule_name: str,
                         entry_conditions: ConditionGroup,
                         signal_type: str = "BUY",
                         exit_conditions: Optional[ConditionGroup] = None,
                         min_confidence: float = 0.5,
                         max_holding_period: Optional[int] = None,
                         stop_loss_pct: Optional[float] = None,
                         take_profit_pct: Optional[float] = None,
                         description: str = "") -> StrategyRule:
        """Add a complete rule to the strategy."""
        rule = StrategyRule(
            name=rule_name,
            entry_conditions=entry_conditions,
            exit_conditions=exit_conditions,
            signal_type=SignalType(signal_type),
            min_confidence=min_confidence,
            max_holding_period=max_holding_period,
            stop_loss_pct=stop_loss_pct,
            take_profit_pct=take_profit_pct,
            description=description
        )
        
        strategy.add_rule(rule)
        return rule
    
    def validate_strategy(self, strategy: MultiIndicatorStrategy) -> Dict[str, Any]:
        """Validate strategy configuration and provide feedback."""
        validation_result = {
            'is_valid': True,
            'warnings': [],
            'errors': [],
            'suggestions': []
        }
        
        # Check if strategy has rules
        if not strategy.rules:
            validation_result['errors'].append("Strategy must have at least one rule")
            validation_result['is_valid'] = False
        
        # Validate each rule
        for rule in strategy.rules:
            rule_validation = self._validate_rule(rule)
            validation_result['warnings'].extend(rule_validation['warnings'])
            validation_result['errors'].extend(rule_validation['errors'])
            validation_result['suggestions'].extend(rule_validation['suggestions'])
            
            if rule_validation['errors']:
                validation_result['is_valid'] = False
        
        # Strategy-level suggestions
        if len(strategy.rules) == 1:
            validation_result['suggestions'].append(
                "Consider adding exit conditions or multiple rules for better performance"
            )
        
        # Check for indicator diversity
        used_indicators = set()
        for rule in strategy.rules:
            used_indicators.update(self._extract_indicators_from_conditions(rule.entry_conditions))
            if rule.exit_conditions:
                used_indicators.update(self._extract_indicators_from_conditions(rule.exit_conditions))
        
        if len(used_indicators) == 1:
            validation_result['suggestions'].append(
                "Consider using multiple indicators for better signal confirmation"
            )
        
        return validation_result
    
    def optimize_strategy_parameters(self, strategy: MultiIndicatorStrategy,
                                   optimization_ranges: Dict[str, Dict[str, Tuple[float, float]]],
                                   optimization_metric: str = "sharpe_ratio") -> Dict[str, Any]:
        """Optimize strategy parameters using grid search."""
        # This would integrate with the backtesting system
        # For now, return a placeholder structure
        return {
            'optimized_parameters': {},
            'performance_improvement': 0.0,
            'optimization_iterations': 0,
            'best_metric_value': 0.0
        }
    
    def get_strategy_complexity_score(self, strategy: MultiIndicatorStrategy) -> Dict[str, Any]:
        """Calculate strategy complexity score and classification."""
        complexity_factors = {
            'total_rules': len(strategy.rules),
            'total_conditions': 0,
            'unique_indicators': set(),
            'logical_operators': 0,
            'multi_value_indicators': 0
        }
        
        for rule in strategy.rules:
            complexity_factors['total_conditions'] += self._count_conditions(rule.entry_conditions)
            if rule.exit_conditions:
                complexity_factors['total_conditions'] += self._count_conditions(rule.exit_conditions)
            
            complexity_factors['unique_indicators'].update(
                self._extract_indicators_from_conditions(rule.entry_conditions)
            )
            if rule.exit_conditions:
                complexity_factors['unique_indicators'].update(
                    self._extract_indicators_from_conditions(rule.exit_conditions)
                )
        
        # Calculate complexity score
        score = 0
        score += complexity_factors['total_rules'] * 2
        score += complexity_factors['total_conditions'] * 1
        score += len(complexity_factors['unique_indicators']) * 3
        score += complexity_factors['logical_operators'] * 1
        
        # Classify complexity
        if score <= 10:
            complexity_level = "Beginner"
        elif score <= 25:
            complexity_level = "Intermediate"
        else:
            complexity_level = "Advanced"
        
        return {
            'complexity_score': score,
            'complexity_level': complexity_level,
            'factors': {
                'total_rules': complexity_factors['total_rules'],
                'total_conditions': complexity_factors['total_conditions'],
                'unique_indicators': len(complexity_factors['unique_indicators']),
                'indicators_used': list(complexity_factors['unique_indicators'])
            }
        }
    
    def _build_rules_from_config(self, strategy: MultiIndicatorStrategy, config: Dict[str, Any]):
        """Build strategy rules from template configuration."""
        # This is a simplified implementation
        # In a full implementation, this would parse the complex config structure
        pass
    
    def _validate_indicator_params(self, params: Dict[str, Any], param_specs: Dict[str, Dict]):
        """Validate indicator parameters against specifications."""
        for param_name, value in params.items():
            if param_name not in param_specs:
                raise ValueError(f"Unknown parameter: {param_name}")
            
            spec = param_specs[param_name]
            
            # Type validation
            expected_type = spec.get('type', type(value))
            if not isinstance(value, expected_type):
                raise ValueError(f"Parameter '{param_name}' must be of type {expected_type.__name__}")
            
            # Range validation
            if 'min' in spec and value < spec['min']:
                raise ValueError(f"Parameter '{param_name}' must be >= {spec['min']}")
            if 'max' in spec and value > spec['max']:
                raise ValueError(f"Parameter '{param_name}' must be <= {spec['max']}")
    
    def _validate_rule(self, rule: StrategyRule) -> Dict[str, List[str]]:
        """Validate a single strategy rule."""
        result = {'warnings': [], 'errors': [], 'suggestions': []}
        
        # Check confidence threshold
        if rule.min_confidence < 0.3:
            result['warnings'].append(f"Rule '{rule.name}': Very low confidence threshold ({rule.min_confidence})")
        elif rule.min_confidence > 0.9:
            result['warnings'].append(f"Rule '{rule.name}': Very high confidence threshold may miss signals")
        
        # Check stop loss and take profit
        if rule.stop_loss_pct and rule.take_profit_pct:
            risk_reward = rule.take_profit_pct / rule.stop_loss_pct
            if risk_reward < 1.5:
                result['suggestions'].append(
                    f"Rule '{rule.name}': Consider improving risk-reward ratio (currently {risk_reward:.2f})"
                )
        
        return result
    
    def _count_conditions(self, condition_group: ConditionGroup) -> int:
        """Count total conditions in a condition group."""
        count = 0
        for condition in condition_group.conditions:
            if isinstance(condition, IndicatorCondition):
                count += 1
            elif isinstance(condition, ConditionGroup):
                count += self._count_conditions(condition)
        return count
    
    def _extract_indicators_from_conditions(self, condition_group: ConditionGroup) -> set:
        """Extract unique indicators from condition group."""
        indicators = set()
        for condition in condition_group.conditions:
            if isinstance(condition, IndicatorCondition):
                indicators.add(condition.indicator_name)
            elif isinstance(condition, ConditionGroup):
                indicators.update(self._extract_indicators_from_conditions(condition))
        return indicators
    
    def export_strategy_json(self, strategy: MultiIndicatorStrategy) -> str:
        """Export strategy to JSON format."""
        return json.dumps(strategy.export_strategy(), indent=2)
    
    def import_strategy_json(self, json_str: str) -> MultiIndicatorStrategy:
        """Import strategy from JSON format."""
        config = json.loads(json_str)
        return MultiIndicatorStrategy.import_strategy(config)
    
    def get_recommended_indicators_for_market_condition(self, market_condition: str) -> List[str]:
        """Get recommended indicators based on market condition."""
        recommendations = {
            'trending': ['EMA', 'MACD', 'ADX', 'SAR'],
            'ranging': ['RSI', 'STOCH', 'BBANDS', 'WILLR'],
            'volatile': ['ATR', 'BBANDS', 'KELTNER', 'DONCHIAN'],
            'low_volume': ['OBV', 'AD', 'VWAP'],
            'breakout': ['DONCHIAN', 'KELTNER', 'ATR', 'SUPERTREND']
        }
        
        return recommendations.get(market_condition.lower(), [])
    
    def suggest_complementary_indicators(self, current_indicators: List[str]) -> List[str]:
        """Suggest complementary indicators based on current selection."""
        current_types = set()
        
        # Categorize current indicators
        for indicator in current_indicators:
            if indicator.upper() in self.available_indicators:
                indicator_type = self.available_indicators[indicator.upper()]['type']
                current_types.add(indicator_type)
        
        # Suggest indicators from missing categories
        suggestions = []
        all_types = {IndicatorType.TREND, IndicatorType.MOMENTUM, IndicatorType.VOLATILITY, IndicatorType.VOLUME}
        
        missing_types = all_types - current_types
        
        for missing_type in missing_types:
            # Find indicators of missing type
            for indicator_name, spec in self.available_indicators.items():
                if spec['type'] == missing_type and indicator_name not in current_indicators:
                    suggestions.append(indicator_name)
                    break  # Add one from each missing category
        
        return suggestions


# Global instance (lazy-loaded)
_strategy_builder = None

def get_strategy_builder():
    """Get or create the global strategy builder instance."""
    global _strategy_builder
    if _strategy_builder is None:
        _strategy_builder = StrategyBuilder()
    return _strategy_builder

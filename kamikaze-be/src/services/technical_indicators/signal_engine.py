"""
Advanced Signal Generation Engine
Sophisticated signal scoring system with multi-timeframe analysis and validation
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from decimal import Decimal
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import statistics

from .strategy_framework import MultiIndicatorStrategy, SignalResult, SignalType
from .indicators_library import TechnicalIndicatorsLibrary
from ...shared.logging_config import setup_logging

logger = setup_logging("signal_engine")


class TimeFrame(Enum):
    """Supported timeframes for multi-timeframe analysis."""
    M1 = "1m"
    M5 = "5m"
    M15 = "15m"
    M30 = "30m"
    H1 = "1h"
    H4 = "4h"
    D1 = "1d"
    W1 = "1w"


class SignalQuality(Enum):
    """Signal quality levels."""
    EXCELLENT = "EXCELLENT"
    GOOD = "GOOD"
    FAIR = "FAIR"
    POOR = "POOR"


@dataclass
class MultiTimeframeSignal:
    """Signal with multi-timeframe analysis."""
    primary_signal: SignalResult
    timeframe_signals: Dict[Time<PERSON>rame, SignalResult]
    consensus_score: float
    quality: SignalQuality
    trend_alignment: float
    momentum_strength: float
    volatility_context: float
    volume_confirmation: float
    risk_reward_ratio: Optional[float] = None


@dataclass
class SignalFilter:
    """Configuration for signal filtering."""
    min_confidence: float = 0.6
    min_consensus_score: float = 0.5
    min_quality: SignalQuality = SignalQuality.FAIR
    require_trend_alignment: bool = True
    min_trend_alignment: float = 0.6
    require_volume_confirmation: bool = False
    min_volume_confirmation: float = 0.5
    max_signals_per_timeframe: int = 3
    cooldown_period_minutes: int = 60


class AdvancedSignalEngine:
    """
    Advanced signal generation engine with sophisticated analysis.
    
    Features:
    - Multi-timeframe signal analysis
    - Signal quality assessment
    - Trend and momentum alignment
    - Volume confirmation
    - Signal filtering and validation
    - Risk-reward analysis
    - Signal convergence scoring
    """
    
    def __init__(self):
        self.indicators_library = TechnicalIndicatorsLibrary()
        self.strategies: Dict[str, MultiIndicatorStrategy] = {}
        self.signal_history: List[MultiTimeframeSignal] = []
        self.timeframe_data_cache: Dict[TimeFrame, Dict[str, np.ndarray]] = {}
        
    def add_strategy(self, strategy: MultiIndicatorStrategy):
        """Add a strategy to the engine."""
        self.strategies[strategy.name] = strategy
        logger.info(f"Added strategy '{strategy.name}' to signal engine")
    
    def generate_multi_timeframe_signals(
        self,
        data: Dict[TimeFrame, Dict[str, np.ndarray]],
        primary_timeframe: TimeFrame = TimeFrame.H1,
        signal_filter: Optional[SignalFilter] = None
    ) -> List[MultiTimeframeSignal]:
        """
        Generate signals with multi-timeframe analysis.
        
        Args:
            data: Market data for different timeframes
            primary_timeframe: Primary timeframe for signal generation
            signal_filter: Filter configuration for signal validation
            
        Returns:
            List of validated multi-timeframe signals
        """
        if signal_filter is None:
            signal_filter = SignalFilter()
        
        multi_timeframe_signals = []
        
        # Generate signals for each strategy
        for strategy_name, strategy in self.strategies.items():
            try:
                # Generate primary signals
                primary_data = data.get(primary_timeframe)
                if not primary_data:
                    logger.warning(f"No data available for primary timeframe {primary_timeframe.value}")
                    continue
                
                primary_signals = strategy.generate_signals(primary_data)
                
                for primary_signal in primary_signals:
                    # Generate signals for other timeframes
                    timeframe_signals = {}
                    
                    for timeframe, tf_data in data.items():
                        if timeframe != primary_timeframe:
                            tf_signals = strategy.generate_signals(tf_data)
                            # Take the best signal for this timeframe
                            if tf_signals:
                                best_signal = max(tf_signals, key=lambda x: x.strength)
                                timeframe_signals[timeframe] = best_signal
                    
                    # Calculate consensus and quality metrics
                    consensus_score = self._calculate_consensus_score(primary_signal, timeframe_signals)
                    quality = self._assess_signal_quality(primary_signal, timeframe_signals, consensus_score)
                    
                    # Calculate additional metrics
                    trend_alignment = self._calculate_trend_alignment(data, primary_timeframe)
                    momentum_strength = self._calculate_momentum_strength(data, primary_timeframe)
                    volatility_context = self._calculate_volatility_context(data, primary_timeframe)
                    volume_confirmation = self._calculate_volume_confirmation(data, primary_timeframe)
                    
                    # Create multi-timeframe signal
                    mtf_signal = MultiTimeframeSignal(
                        primary_signal=primary_signal,
                        timeframe_signals=timeframe_signals,
                        consensus_score=consensus_score,
                        quality=quality,
                        trend_alignment=trend_alignment,
                        momentum_strength=momentum_strength,
                        volatility_context=volatility_context,
                        volume_confirmation=volume_confirmation
                    )
                    
                    # Apply filters
                    if self._passes_filters(mtf_signal, signal_filter):
                        multi_timeframe_signals.append(mtf_signal)
                        logger.debug(f"Generated MTF signal: {primary_signal.signal_type.value} "
                                   f"(Consensus: {consensus_score:.3f}, Quality: {quality.value})")
                
            except Exception as e:
                logger.error(f"Error generating signals for strategy '{strategy_name}': {e}")
        
        # Sort by consensus score and quality
        multi_timeframe_signals.sort(key=lambda x: (x.consensus_score, x.quality.value), reverse=True)
        
        # Apply maximum signals limit
        if len(multi_timeframe_signals) > signal_filter.max_signals_per_timeframe:
            multi_timeframe_signals = multi_timeframe_signals[:signal_filter.max_signals_per_timeframe]
        
        # Store in history
        self.signal_history.extend(multi_timeframe_signals)
        
        return multi_timeframe_signals
    
    def _calculate_consensus_score(self, primary_signal: SignalResult, 
                                 timeframe_signals: Dict[TimeFrame, SignalResult]) -> float:
        """Calculate consensus score across timeframes."""
        if not timeframe_signals:
            return primary_signal.confidence
        
        # Count signals in same direction
        same_direction_count = 0
        total_signals = len(timeframe_signals)
        confidence_sum = primary_signal.confidence
        
        for tf_signal in timeframe_signals.values():
            confidence_sum += tf_signal.confidence
            if tf_signal.signal_type == primary_signal.signal_type:
                same_direction_count += 1
        
        # Calculate consensus
        direction_consensus = same_direction_count / total_signals if total_signals > 0 else 0
        average_confidence = confidence_sum / (total_signals + 1)
        
        # Weighted consensus score
        consensus_score = (direction_consensus * 0.6) + (average_confidence * 0.4)
        
        return min(1.0, max(0.0, consensus_score))
    
    def _assess_signal_quality(self, primary_signal: SignalResult, 
                             timeframe_signals: Dict[TimeFrame, SignalResult],
                             consensus_score: float) -> SignalQuality:
        """Assess overall signal quality."""
        # Quality factors
        confidence_factor = primary_signal.confidence
        consensus_factor = consensus_score
        conditions_factor = len(primary_signal.conditions_met) / max(1, 
            len(primary_signal.conditions_met) + len(primary_signal.conditions_failed))
        
        # Calculate overall quality score
        quality_score = (confidence_factor * 0.4) + (consensus_factor * 0.4) + (conditions_factor * 0.2)
        
        if quality_score >= 0.8:
            return SignalQuality.EXCELLENT
        elif quality_score >= 0.65:
            return SignalQuality.GOOD
        elif quality_score >= 0.5:
            return SignalQuality.FAIR
        else:
            return SignalQuality.POOR
    
    def _calculate_trend_alignment(self, data: Dict[TimeFrame, Dict[str, np.ndarray]], 
                                 primary_timeframe: TimeFrame) -> float:
        """Calculate trend alignment across timeframes."""
        try:
            trend_scores = []
            
            for timeframe, tf_data in data.items():
                if 'close' not in tf_data:
                    continue
                
                # Calculate trend using multiple EMAs
                ema_short = self.indicators_library.ta_lib.ema(tf_data, period=10)
                ema_medium = self.indicators_library.ta_lib.ema(tf_data, period=20)
                ema_long = self.indicators_library.ta_lib.ema(tf_data, period=50)
                
                # Check trend alignment
                latest_short = ema_short.get_latest_value()
                latest_medium = ema_medium.get_latest_value()
                latest_long = ema_long.get_latest_value()
                
                if all(v is not None for v in [latest_short, latest_medium, latest_long]):
                    if latest_short > latest_medium > latest_long:
                        trend_scores.append(1.0)  # Strong uptrend
                    elif latest_short < latest_medium < latest_long:
                        trend_scores.append(-1.0)  # Strong downtrend
                    else:
                        trend_scores.append(0.0)  # Sideways/mixed
            
            if trend_scores:
                # Calculate alignment strength
                avg_trend = statistics.mean(trend_scores)
                alignment_strength = abs(avg_trend)
                return alignment_strength
            
            return 0.5  # Neutral if no data
            
        except Exception as e:
            logger.error(f"Error calculating trend alignment: {e}")
            return 0.5
    
    def _calculate_momentum_strength(self, data: Dict[TimeFrame, Dict[str, np.ndarray]], 
                                   primary_timeframe: TimeFrame) -> float:
        """Calculate momentum strength."""
        try:
            primary_data = data.get(primary_timeframe)
            if not primary_data or 'close' not in primary_data:
                return 0.5
            
            # Calculate RSI and ROC for momentum
            rsi = self.indicators_library.ta_lib.rsi(primary_data, period=14)
            roc = self.indicators_library.indicators_library.roc(primary_data, period=10)
            
            latest_rsi = rsi.get_latest_value()
            latest_roc = roc.get_latest_value()
            
            if latest_rsi is not None and latest_roc is not None:
                # Normalize RSI to 0-1 scale
                rsi_normalized = abs(latest_rsi - 50) / 50
                
                # Normalize ROC (assuming typical range of -10% to +10%)
                roc_normalized = min(1.0, abs(latest_roc) / 10.0)
                
                # Combined momentum strength
                momentum_strength = (rsi_normalized + roc_normalized) / 2
                return min(1.0, momentum_strength)
            
            return 0.5
            
        except Exception as e:
            logger.error(f"Error calculating momentum strength: {e}")
            return 0.5
    
    def _calculate_volatility_context(self, data: Dict[TimeFrame, Dict[str, np.ndarray]], 
                                    primary_timeframe: TimeFrame) -> float:
        """Calculate volatility context for signal timing."""
        try:
            primary_data = data.get(primary_timeframe)
            if not primary_data:
                return 0.5
            
            # Calculate ATR for volatility
            atr = self.indicators_library.ta_lib.atr(primary_data, period=14)
            latest_atr = atr.get_latest_value()
            
            if latest_atr is not None and 'close' in primary_data:
                latest_close = primary_data['close'][-1]
                
                # Calculate ATR as percentage of price
                atr_percentage = (latest_atr / latest_close) * 100
                
                # Normalize volatility (assuming typical range of 0.5% to 5%)
                volatility_normalized = min(1.0, atr_percentage / 5.0)
                
                # Higher volatility can be good for breakout strategies, bad for mean reversion
                # Return normalized value
                return volatility_normalized
            
            return 0.5
            
        except Exception as e:
            logger.error(f"Error calculating volatility context: {e}")
            return 0.5
    
    def _calculate_volume_confirmation(self, data: Dict[TimeFrame, Dict[str, np.ndarray]], 
                                     primary_timeframe: TimeFrame) -> float:
        """Calculate volume confirmation strength."""
        try:
            primary_data = data.get(primary_timeframe)
            if not primary_data or 'volume' not in primary_data:
                return 0.5
            
            # Calculate volume moving average
            volume_sma = self.indicators_library.ta_lib.sma(
                {'close': primary_data['volume']}, period=20
            )
            
            latest_volume = primary_data['volume'][-1]
            avg_volume = volume_sma.get_latest_value()
            
            if avg_volume is not None and avg_volume > 0:
                # Volume ratio
                volume_ratio = latest_volume / avg_volume
                
                # Normalize (assuming typical range of 0.5x to 3x average)
                volume_confirmation = min(1.0, max(0.0, (volume_ratio - 0.5) / 2.5))
                
                return volume_confirmation
            
            return 0.5
            
        except Exception as e:
            logger.error(f"Error calculating volume confirmation: {e}")
            return 0.5
    
    def _passes_filters(self, signal: MultiTimeframeSignal, signal_filter: SignalFilter) -> bool:
        """Check if signal passes all filters."""
        # Confidence filter
        if signal.primary_signal.confidence < signal_filter.min_confidence:
            return False
        
        # Consensus filter
        if signal.consensus_score < signal_filter.min_consensus_score:
            return False
        
        # Quality filter
        quality_levels = {
            SignalQuality.POOR: 0,
            SignalQuality.FAIR: 1,
            SignalQuality.GOOD: 2,
            SignalQuality.EXCELLENT: 3
        }
        
        if quality_levels[signal.quality] < quality_levels[signal_filter.min_quality]:
            return False
        
        # Trend alignment filter
        if signal_filter.require_trend_alignment and signal.trend_alignment < signal_filter.min_trend_alignment:
            return False
        
        # Volume confirmation filter
        if signal_filter.require_volume_confirmation and signal.volume_confirmation < signal_filter.min_volume_confirmation:
            return False
        
        # Cooldown period filter
        if self._is_in_cooldown_period(signal, signal_filter.cooldown_period_minutes):
            return False
        
        return True
    
    def _is_in_cooldown_period(self, signal: MultiTimeframeSignal, cooldown_minutes: int) -> bool:
        """Check if signal is within cooldown period of recent signals."""
        if not self.signal_history:
            return False
        
        current_time = signal.primary_signal.timestamp
        cooldown_delta = timedelta(minutes=cooldown_minutes)
        
        for historical_signal in reversed(self.signal_history[-10:]):  # Check last 10 signals
            time_diff = current_time - historical_signal.primary_signal.timestamp
            
            if time_diff < cooldown_delta:
                # Check if same signal type
                if historical_signal.primary_signal.signal_type == signal.primary_signal.signal_type:
                    return True
        
        return False
    
    def get_signal_statistics(self) -> Dict[str, Any]:
        """Get comprehensive signal statistics."""
        if not self.signal_history:
            return {'total_signals': 0}
        
        # Basic counts
        total_signals = len(self.signal_history)
        signal_types = {}
        quality_distribution = {}
        
        for signal in self.signal_history:
            # Signal type distribution
            signal_type = signal.primary_signal.signal_type.value
            signal_types[signal_type] = signal_types.get(signal_type, 0) + 1
            
            # Quality distribution
            quality = signal.quality.value
            quality_distribution[quality] = quality_distribution.get(quality, 0) + 1
        
        # Average metrics
        avg_confidence = statistics.mean([s.primary_signal.confidence for s in self.signal_history])
        avg_consensus = statistics.mean([s.consensus_score for s in self.signal_history])
        avg_trend_alignment = statistics.mean([s.trend_alignment for s in self.signal_history])
        avg_momentum = statistics.mean([s.momentum_strength for s in self.signal_history])
        
        return {
            'total_signals': total_signals,
            'signal_types': signal_types,
            'quality_distribution': quality_distribution,
            'average_confidence': avg_confidence,
            'average_consensus_score': avg_consensus,
            'average_trend_alignment': avg_trend_alignment,
            'average_momentum_strength': avg_momentum,
            'strategies_count': len(self.strategies)
        }
    
    def clear_history(self):
        """Clear signal history and cache."""
        self.signal_history.clear()
        self.timeframe_data_cache.clear()
        for strategy in self.strategies.values():
            strategy.clear_history()
        logger.info("Cleared signal engine history")


# Global instance
signal_engine = AdvancedSignalEngine()

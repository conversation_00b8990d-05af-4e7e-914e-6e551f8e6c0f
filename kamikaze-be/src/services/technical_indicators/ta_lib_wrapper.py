"""
TA-Lib Integration Wrapper
Professional-grade wrapper around TA-Lib with error handling, validation, and standardized interfaces
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum
import warnings

# Suppress TA-Lib warnings for cleaner output
warnings.filterwarnings('ignore', category=RuntimeWarning)

try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    logging.warning("TA-Lib not available. Please install: pip install TA-Lib")

from ...shared.logging_config import setup_logging

logger = setup_logging("ta_lib_wrapper")


class IndicatorType(Enum):
    """Types of technical indicators."""
    TREND = "trend"
    MOMENTUM = "momentum"
    VOLATILITY = "volatility"
    VOLUME = "volume"
    CYCLE = "cycle"
    PRICE_TRANSFORM = "price_transform"
    OVERLAP = "overlap"
    PATTERN = "pattern"


@dataclass
class IndicatorResult:
    """Standardized result structure for technical indicators."""
    name: str
    type: IndicatorType
    values: np.ndarray
    parameters: Dict[str, Any]
    timestamp: Optional[np.ndarray] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def get_latest_value(self) -> Optional[float]:
        """Get the most recent indicator value."""
        if len(self.values) > 0 and not np.isnan(self.values[-1]):
            return float(self.values[-1])
        return None
    
    def get_value_at_index(self, index: int) -> Optional[float]:
        """Get indicator value at specific index."""
        if 0 <= index < len(self.values) and not np.isnan(self.values[index]):
            return float(self.values[index])
        return None


@dataclass
class MultiValueIndicatorResult:
    """Result structure for indicators that return multiple values (e.g., MACD, Bollinger Bands)."""
    name: str
    type: IndicatorType
    values: Dict[str, np.ndarray]
    parameters: Dict[str, Any]
    timestamp: Optional[np.ndarray] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def get_latest_values(self) -> Dict[str, Optional[float]]:
        """Get the most recent values for all components."""
        result = {}
        for key, array in self.values.items():
            if len(array) > 0 and not np.isnan(array[-1]):
                result[key] = float(array[-1])
            else:
                result[key] = None
        return result


class TALibWrapper:
    """
    Professional wrapper around TA-Lib with standardized interfaces and error handling.
    
    Features:
    - Standardized parameter validation
    - Consistent error handling
    - Performance optimization
    - Memory management
    - Extensible architecture
    """
    
    def __init__(self):
        if not TALIB_AVAILABLE:
            raise ImportError("TA-Lib is required but not installed")
        
        self.indicator_cache = {}
        self.cache_enabled = True
        self.max_cache_size = 1000
    
    def _validate_price_data(self, data: Dict[str, np.ndarray], required_fields: List[str]) -> bool:
        """Validate price data structure and completeness."""
        if not isinstance(data, dict):
            raise ValueError("Price data must be a dictionary")
        
        for field in required_fields:
            if field not in data:
                raise ValueError(f"Required field '{field}' missing from price data")
            
            if not isinstance(data[field], np.ndarray):
                raise ValueError(f"Field '{field}' must be a numpy array")
            
            if len(data[field]) == 0:
                raise ValueError(f"Field '{field}' cannot be empty")
        
        # Check all arrays have same length
        lengths = [len(data[field]) for field in required_fields]
        if len(set(lengths)) > 1:
            raise ValueError("All price data arrays must have the same length")
        
        return True
    
    def _validate_parameters(self, params: Dict[str, Any], param_specs: Dict[str, Dict]) -> Dict[str, Any]:
        """Validate and sanitize indicator parameters."""
        validated_params = {}
        
        for param_name, spec in param_specs.items():
            value = params.get(param_name, spec.get('default'))
            
            if value is None and spec.get('required', False):
                raise ValueError(f"Required parameter '{param_name}' is missing")
            
            if value is not None:
                # Type validation
                expected_type = spec.get('type', type(value))
                if not isinstance(value, expected_type):
                    try:
                        value = expected_type(value)
                    except (ValueError, TypeError):
                        raise ValueError(f"Parameter '{param_name}' must be of type {expected_type.__name__}")
                
                # Range validation
                if 'min' in spec and value < spec['min']:
                    raise ValueError(f"Parameter '{param_name}' must be >= {spec['min']}")
                if 'max' in spec and value > spec['max']:
                    raise ValueError(f"Parameter '{param_name}' must be <= {spec['max']}")
                
                validated_params[param_name] = value
        
        return validated_params
    
    def _create_cache_key(self, indicator_name: str, data_hash: str, params: Dict[str, Any]) -> str:
        """Create a cache key for indicator results."""
        param_str = "_".join([f"{k}:{v}" for k, v in sorted(params.items())])
        return f"{indicator_name}_{data_hash}_{param_str}"
    
    def _get_data_hash(self, data: Dict[str, np.ndarray]) -> str:
        """Generate a hash for price data to enable caching."""
        # Simple hash based on data length and last few values
        try:
            close_data = data.get('close', data.get('price', list(data.values())[0]))
            if len(close_data) > 0:
                return f"{len(close_data)}_{hash(tuple(close_data[-5:]))}"
        except:
            pass
        return str(hash(str(data)))
    
    # ==================== TREND INDICATORS ====================
    
    def sma(self, data: Dict[str, np.ndarray], period: int = 20) -> IndicatorResult:
        """Simple Moving Average."""
        self._validate_price_data(data, ['close'])
        params = self._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 1000, 'default': 20}}
        )
        
        try:
            values = talib.SMA(data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="SMA",
                type=IndicatorType.OVERLAP,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating SMA: {e}")
            raise
    
    def ema(self, data: Dict[str, np.ndarray], period: int = 20) -> IndicatorResult:
        """Exponential Moving Average."""
        self._validate_price_data(data, ['close'])
        params = self._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 1000, 'default': 20}}
        )
        
        try:
            values = talib.EMA(data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="EMA",
                type=IndicatorType.OVERLAP,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating EMA: {e}")
            raise
    
    def wma(self, data: Dict[str, np.ndarray], period: int = 20) -> IndicatorResult:
        """Weighted Moving Average."""
        self._validate_price_data(data, ['close'])
        params = self._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 1000, 'default': 20}}
        )
        
        try:
            values = talib.WMA(data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="WMA",
                type=IndicatorType.OVERLAP,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating WMA: {e}")
            raise
    
    def macd(self, data: Dict[str, np.ndarray], fast_period: int = 12, 
             slow_period: int = 26, signal_period: int = 9) -> MultiValueIndicatorResult:
        """MACD (Moving Average Convergence Divergence)."""
        self._validate_price_data(data, ['close'])
        params = self._validate_parameters(
            {'fast_period': fast_period, 'slow_period': slow_period, 'signal_period': signal_period},
            {
                'fast_period': {'type': int, 'min': 1, 'max': 100, 'default': 12},
                'slow_period': {'type': int, 'min': 1, 'max': 200, 'default': 26},
                'signal_period': {'type': int, 'min': 1, 'max': 100, 'default': 9}
            }
        )
        
        if params['fast_period'] >= params['slow_period']:
            raise ValueError("Fast period must be less than slow period")
        
        try:
            macd_line, macd_signal, macd_histogram = talib.MACD(
                data['close'],
                fastperiod=params['fast_period'],
                slowperiod=params['slow_period'],
                signalperiod=params['signal_period']
            )
            
            return MultiValueIndicatorResult(
                name="MACD",
                type=IndicatorType.MOMENTUM,
                values={
                    'macd': macd_line,
                    'signal': macd_signal,
                    'histogram': macd_histogram
                },
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            raise
    
    # ==================== MOMENTUM INDICATORS ====================
    
    def rsi(self, data: Dict[str, np.ndarray], period: int = 14) -> IndicatorResult:
        """Relative Strength Index."""
        self._validate_price_data(data, ['close'])
        params = self._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 100, 'default': 14}}
        )
        
        try:
            values = talib.RSI(data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="RSI",
                type=IndicatorType.MOMENTUM,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            raise
    
    def stoch(self, data: Dict[str, np.ndarray], k_period: int = 14, 
              k_slowing: int = 3, d_period: int = 3) -> MultiValueIndicatorResult:
        """Stochastic Oscillator."""
        self._validate_price_data(data, ['high', 'low', 'close'])
        params = self._validate_parameters(
            {'k_period': k_period, 'k_slowing': k_slowing, 'd_period': d_period},
            {
                'k_period': {'type': int, 'min': 1, 'max': 100, 'default': 14},
                'k_slowing': {'type': int, 'min': 1, 'max': 10, 'default': 3},
                'd_period': {'type': int, 'min': 1, 'max': 10, 'default': 3}
            }
        )
        
        try:
            slowk, slowd = talib.STOCH(
                data['high'], data['low'], data['close'],
                fastk_period=params['k_period'],
                slowk_period=params['k_slowing'],
                slowd_period=params['d_period']
            )
            
            return MultiValueIndicatorResult(
                name="STOCH",
                type=IndicatorType.MOMENTUM,
                values={
                    'slowk': slowk,
                    'slowd': slowd
                },
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating Stochastic: {e}")
            raise
    
    def williams_r(self, data: Dict[str, np.ndarray], period: int = 14) -> IndicatorResult:
        """Williams %R."""
        self._validate_price_data(data, ['high', 'low', 'close'])
        params = self._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 100, 'default': 14}}
        )
        
        try:
            values = talib.WILLR(data['high'], data['low'], data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="WILLR",
                type=IndicatorType.MOMENTUM,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating Williams %R: {e}")
            raise
    
    # ==================== VOLATILITY INDICATORS ====================
    
    def bollinger_bands(self, data: Dict[str, np.ndarray], period: int = 20, 
                       std_dev: float = 2.0) -> MultiValueIndicatorResult:
        """Bollinger Bands."""
        self._validate_price_data(data, ['close'])
        params = self._validate_parameters(
            {'period': period, 'std_dev': std_dev},
            {
                'period': {'type': int, 'min': 1, 'max': 100, 'default': 20},
                'std_dev': {'type': float, 'min': 0.1, 'max': 5.0, 'default': 2.0}
            }
        )
        
        try:
            upper, middle, lower = talib.BBANDS(
                data['close'],
                timeperiod=params['period'],
                nbdevup=params['std_dev'],
                nbdevdn=params['std_dev']
            )
            
            return MultiValueIndicatorResult(
                name="BBANDS",
                type=IndicatorType.VOLATILITY,
                values={
                    'upper': upper,
                    'middle': middle,
                    'lower': lower
                },
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {e}")
            raise
    
    def atr(self, data: Dict[str, np.ndarray], period: int = 14) -> IndicatorResult:
        """Average True Range."""
        self._validate_price_data(data, ['high', 'low', 'close'])
        params = self._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 100, 'default': 14}}
        )
        
        try:
            values = talib.ATR(data['high'], data['low'], data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="ATR",
                type=IndicatorType.VOLATILITY,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            raise
    
    def adx(self, data: Dict[str, np.ndarray], period: int = 14) -> IndicatorResult:
        """Average Directional Index."""
        self._validate_price_data(data, ['high', 'low', 'close'])
        params = self._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 100, 'default': 14}}
        )

        try:
            values = talib.ADX(data['high'], data['low'], data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="ADX",
                type=IndicatorType.MOMENTUM,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating ADX: {e}")
            raise

    def cci(self, data: Dict[str, np.ndarray], period: int = 14) -> IndicatorResult:
        """Commodity Channel Index."""
        self._validate_price_data(data, ['high', 'low', 'close'])
        params = self._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 100, 'default': 14}}
        )

        try:
            values = talib.CCI(data['high'], data['low'], data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="CCI",
                type=IndicatorType.MOMENTUM,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating CCI: {e}")
            raise

    def parabolic_sar(self, data: Dict[str, np.ndarray], acceleration: float = 0.02,
                      maximum: float = 0.2) -> IndicatorResult:
        """Parabolic SAR."""
        self._validate_price_data(data, ['high', 'low'])
        params = self._validate_parameters(
            {'acceleration': acceleration, 'maximum': maximum},
            {
                'acceleration': {'type': float, 'min': 0.001, 'max': 1.0, 'default': 0.02},
                'maximum': {'type': float, 'min': 0.01, 'max': 1.0, 'default': 0.2}
            }
        )

        try:
            values = talib.SAR(data['high'], data['low'],
                             acceleration=params['acceleration'],
                             maximum=params['maximum'])
            return IndicatorResult(
                name="SAR",
                type=IndicatorType.TREND,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating Parabolic SAR: {e}")
            raise

    # ==================== VOLUME INDICATORS ====================

    def obv(self, data: Dict[str, np.ndarray]) -> IndicatorResult:
        """On Balance Volume."""
        self._validate_price_data(data, ['close', 'volume'])

        try:
            values = talib.OBV(data['close'], data['volume'])
            return IndicatorResult(
                name="OBV",
                type=IndicatorType.VOLUME,
                values=values,
                parameters={}
            )
        except Exception as e:
            logger.error(f"Error calculating OBV: {e}")
            raise

    def vwap(self, data: Dict[str, np.ndarray]) -> IndicatorResult:
        """Volume Weighted Average Price (custom implementation)."""
        self._validate_price_data(data, ['high', 'low', 'close', 'volume'])

        try:
            # VWAP = Cumulative(Price * Volume) / Cumulative(Volume)
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            price_volume = typical_price * data['volume']

            cumulative_pv = np.cumsum(price_volume)
            cumulative_volume = np.cumsum(data['volume'])

            # Avoid division by zero
            vwap_values = np.divide(cumulative_pv, cumulative_volume,
                                  out=np.zeros_like(cumulative_pv),
                                  where=cumulative_volume!=0)

            return IndicatorResult(
                name="VWAP",
                type=IndicatorType.VOLUME,
                values=vwap_values,
                parameters={}
            )
        except Exception as e:
            logger.error(f"Error calculating VWAP: {e}")
            raise

    def ad_line(self, data: Dict[str, np.ndarray]) -> IndicatorResult:
        """Accumulation/Distribution Line."""
        self._validate_price_data(data, ['high', 'low', 'close', 'volume'])

        try:
            values = talib.AD(data['high'], data['low'], data['close'], data['volume'])
            return IndicatorResult(
                name="AD",
                type=IndicatorType.VOLUME,
                values=values,
                parameters={}
            )
        except Exception as e:
            logger.error(f"Error calculating A/D Line: {e}")
            raise

    # ==================== ICHIMOKU CLOUD ====================

    def ichimoku_cloud(self, data: Dict[str, np.ndarray],
                      conversion_period: int = 9, base_period: int = 26,
                      leading_span_b_period: int = 52, displacement: int = 26) -> MultiValueIndicatorResult:
        """Ichimoku Cloud (custom implementation)."""
        self._validate_price_data(data, ['high', 'low', 'close'])
        params = self._validate_parameters(
            {
                'conversion_period': conversion_period,
                'base_period': base_period,
                'leading_span_b_period': leading_span_b_period,
                'displacement': displacement
            },
            {
                'conversion_period': {'type': int, 'min': 1, 'max': 50, 'default': 9},
                'base_period': {'type': int, 'min': 1, 'max': 100, 'default': 26},
                'leading_span_b_period': {'type': int, 'min': 1, 'max': 200, 'default': 52},
                'displacement': {'type': int, 'min': 1, 'max': 100, 'default': 26}
            }
        )

        try:
            high = data['high']
            low = data['low']
            close = data['close']

            # Conversion Line (Tenkan-sen)
            conversion_line = (talib.MAX(high, timeperiod=params['conversion_period']) +
                             talib.MIN(low, timeperiod=params['conversion_period'])) / 2

            # Base Line (Kijun-sen)
            base_line = (talib.MAX(high, timeperiod=params['base_period']) +
                        talib.MIN(low, timeperiod=params['base_period'])) / 2

            # Leading Span A (Senkou Span A)
            leading_span_a = (conversion_line + base_line) / 2

            # Leading Span B (Senkou Span B)
            leading_span_b = (talib.MAX(high, timeperiod=params['leading_span_b_period']) +
                             talib.MIN(low, timeperiod=params['leading_span_b_period'])) / 2

            # Lagging Span (Chikou Span)
            lagging_span = np.roll(close, -params['displacement'])

            return MultiValueIndicatorResult(
                name="ICHIMOKU",
                type=IndicatorType.TREND,
                values={
                    'conversion_line': conversion_line,
                    'base_line': base_line,
                    'leading_span_a': leading_span_a,
                    'leading_span_b': leading_span_b,
                    'lagging_span': lagging_span
                },
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating Ichimoku Cloud: {e}")
            raise

    # ==================== FIBONACCI TOOLS ====================

    def fibonacci_retracements(self, data: Dict[str, np.ndarray],
                              lookback_period: int = 50) -> MultiValueIndicatorResult:
        """Fibonacci Retracement Levels (custom implementation)."""
        self._validate_price_data(data, ['high', 'low'])
        params = self._validate_parameters(
            {'lookback_period': lookback_period},
            {'lookback_period': {'type': int, 'min': 10, 'max': 500, 'default': 50}}
        )

        try:
            high = data['high']
            low = data['low']

            # Find swing high and low over lookback period
            swing_high = talib.MAX(high, timeperiod=params['lookback_period'])
            swing_low = talib.MIN(low, timeperiod=params['lookback_period'])

            # Calculate Fibonacci levels
            diff = swing_high - swing_low

            fib_levels = {
                'level_0': swing_high,
                'level_236': swing_high - (diff * 0.236),
                'level_382': swing_high - (diff * 0.382),
                'level_500': swing_high - (diff * 0.500),
                'level_618': swing_high - (diff * 0.618),
                'level_786': swing_high - (diff * 0.786),
                'level_100': swing_low
            }

            return MultiValueIndicatorResult(
                name="FIBONACCI",
                type=IndicatorType.TREND,
                values=fib_levels,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating Fibonacci Retracements: {e}")
            raise

    # ==================== UTILITY METHODS ====================
    
    def get_available_indicators(self) -> Dict[str, Dict[str, Any]]:
        """Get list of all available indicators with their specifications."""
        return {
            'SMA': {
                'name': 'Simple Moving Average',
                'type': IndicatorType.OVERLAP,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 1000, 'default': 20}},
                'required_data': ['close']
            },
            'EMA': {
                'name': 'Exponential Moving Average',
                'type': IndicatorType.OVERLAP,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 1000, 'default': 20}},
                'required_data': ['close']
            },
            'WMA': {
                'name': 'Weighted Moving Average',
                'type': IndicatorType.OVERLAP,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 1000, 'default': 20}},
                'required_data': ['close']
            },
            'MACD': {
                'name': 'Moving Average Convergence Divergence',
                'type': IndicatorType.MOMENTUM,
                'parameters': {
                    'fast_period': {'type': int, 'min': 1, 'max': 100, 'default': 12},
                    'slow_period': {'type': int, 'min': 1, 'max': 200, 'default': 26},
                    'signal_period': {'type': int, 'min': 1, 'max': 100, 'default': 9}
                },
                'required_data': ['close'],
                'multi_value': True
            },
            'RSI': {
                'name': 'Relative Strength Index',
                'type': IndicatorType.MOMENTUM,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 100, 'default': 14}},
                'required_data': ['close']
            },
            'STOCH': {
                'name': 'Stochastic Oscillator',
                'type': IndicatorType.MOMENTUM,
                'parameters': {
                    'k_period': {'type': int, 'min': 1, 'max': 100, 'default': 14},
                    'k_slowing': {'type': int, 'min': 1, 'max': 10, 'default': 3},
                    'd_period': {'type': int, 'min': 1, 'max': 10, 'default': 3}
                },
                'required_data': ['high', 'low', 'close'],
                'multi_value': True
            },
            'WILLR': {
                'name': 'Williams %R',
                'type': IndicatorType.MOMENTUM,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 100, 'default': 14}},
                'required_data': ['high', 'low', 'close']
            },
            'BBANDS': {
                'name': 'Bollinger Bands',
                'type': IndicatorType.VOLATILITY,
                'parameters': {
                    'period': {'type': int, 'min': 1, 'max': 100, 'default': 20},
                    'std_dev': {'type': float, 'min': 0.1, 'max': 5.0, 'default': 2.0}
                },
                'required_data': ['close'],
                'multi_value': True
            },
            'ATR': {
                'name': 'Average True Range',
                'type': IndicatorType.VOLATILITY,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 100, 'default': 14}},
                'required_data': ['high', 'low', 'close']
            },
            'ADX': {
                'name': 'Average Directional Index',
                'type': IndicatorType.MOMENTUM,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 100, 'default': 14}},
                'required_data': ['high', 'low', 'close']
            },
            'CCI': {
                'name': 'Commodity Channel Index',
                'type': IndicatorType.MOMENTUM,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 100, 'default': 14}},
                'required_data': ['high', 'low', 'close']
            },
            'SAR': {
                'name': 'Parabolic SAR',
                'type': IndicatorType.TREND,
                'parameters': {
                    'acceleration': {'type': float, 'min': 0.001, 'max': 1.0, 'default': 0.02},
                    'maximum': {'type': float, 'min': 0.01, 'max': 1.0, 'default': 0.2}
                },
                'required_data': ['high', 'low']
            },
            'OBV': {
                'name': 'On Balance Volume',
                'type': IndicatorType.VOLUME,
                'parameters': {},
                'required_data': ['close', 'volume']
            },
            'VWAP': {
                'name': 'Volume Weighted Average Price',
                'type': IndicatorType.VOLUME,
                'parameters': {},
                'required_data': ['high', 'low', 'close', 'volume']
            },
            'AD': {
                'name': 'Accumulation/Distribution Line',
                'type': IndicatorType.VOLUME,
                'parameters': {},
                'required_data': ['high', 'low', 'close', 'volume']
            },
            'ICHIMOKU': {
                'name': 'Ichimoku Cloud',
                'type': IndicatorType.TREND,
                'parameters': {
                    'conversion_period': {'type': int, 'min': 1, 'max': 50, 'default': 9},
                    'base_period': {'type': int, 'min': 1, 'max': 100, 'default': 26},
                    'leading_span_b_period': {'type': int, 'min': 1, 'max': 200, 'default': 52},
                    'displacement': {'type': int, 'min': 1, 'max': 100, 'default': 26}
                },
                'required_data': ['high', 'low', 'close'],
                'multi_value': True
            },
            'FIBONACCI': {
                'name': 'Fibonacci Retracements',
                'type': IndicatorType.TREND,
                'parameters': {'lookback_period': {'type': int, 'min': 10, 'max': 500, 'default': 50}},
                'required_data': ['high', 'low'],
                'multi_value': True
            }
        }
    
    def clear_cache(self):
        """Clear the indicator cache."""
        self.indicator_cache.clear()
        logger.info("Indicator cache cleared")


# Global instance
ta_lib = TALibWrapper() if TALIB_AVAILABLE else None

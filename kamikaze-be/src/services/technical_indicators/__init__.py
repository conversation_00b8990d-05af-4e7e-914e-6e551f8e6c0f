"""
Technical Indicators Trading System
Production-ready technical indicator trading system with advanced features

This module provides a comprehensive technical indicator trading system with:
- 25+ professional-grade technical indicators
- Multi-indicator strategy framework
- Advanced signal generation with multi-timeframe analysis
- Sophisticated risk management
- Real-time trading engine
- Complete API integration

Usage:
    from src.services.technical_indicators import (
        TechnicalIndicatorsLibrary,
        StrategyBuilder,
        AdvancedSignalEngine,
        TechnicalIndicatorTradingEngine
    )
    
    # Create indicators library
    indicators = TechnicalIndicatorsLibrary()
    
    # Build a strategy
    builder = StrategyBuilder()
    strategy = builder.create_strategy_from_template("rsi_mean_reversion")
    
    # Generate signals
    signal_engine = AdvancedSignalEngine()
    signal_engine.add_strategy(strategy)
    
    # Start trading engine
    trading_engine = TechnicalIndicatorTradingEngine(config, risk_config)
    await trading_engine.start()
"""

# Core components
from .indicators_library import TechnicalIndicatorsLibrary
from .strategy_framework import (
    MultiIndicatorStrategy,
    IndicatorCondition,
    ConditionGroup,
    StrategyRule,
    LogicalOperator,
    ComparisonOperator,
    SignalType,
    SignalResult
)
from .strategy_builder import StrategyBuilder, get_strategy_builder
from .signal_engine import (
    AdvancedSignalEngine,
    TimeFrame,
    SignalFilter,
    SignalQuality,
    MultiTimeframeSignal
)
from .advanced_risk_management import (
    AdvancedRiskManager,
    RiskManagementConfig,
    RiskLevel,
    PositionSizingMethod,
    RiskMetrics,
    PositionRisk
)
from .trading_engine import (
    TechnicalIndicatorTradingEngine,
    TradingEngineConfig,
    EngineState,
    OrderType,
    OrderStatus,
    TradingOrder,
    Position
)
from .data_provider import (
    DataProviderInterface,
    BinanceDataProvider,
    PaperDataProvider,
    MarketData,
    HistoricalData,
    create_data_provider,
    get_data_provider
)
from .config import (
    TradingSystemConfig,
    DataProviderConfig,
    BrokerConfig,
    get_config,
    set_config
)

# TA-Lib wrapper
from .ta_lib_wrapper import TALibWrapper, IndicatorType

__version__ = "1.0.0"
__author__ = "Kamikaze Trading System"
__description__ = "Professional Technical Indicator Trading System"

# Export all public components
__all__ = [
    # Core libraries
    "TechnicalIndicatorsLibrary",
    "TALibWrapper",
    "IndicatorType",
    
    # Strategy framework
    "MultiIndicatorStrategy",
    "IndicatorCondition", 
    "ConditionGroup",
    "StrategyRule",
    "LogicalOperator",
    "ComparisonOperator",
    "SignalType",
    "SignalResult",
    
    # Strategy builder
    "StrategyBuilder",
    "get_strategy_builder",
    
    # Signal engine
    "AdvancedSignalEngine",
    "TimeFrame",
    "SignalFilter",
    "SignalQuality",
    "MultiTimeframeSignal",
    
    # Risk management
    "AdvancedRiskManager",
    "RiskManagementConfig",
    "RiskLevel",
    "PositionSizingMethod",
    "RiskMetrics",
    "PositionRisk",
    
    # Trading engine
    "TechnicalIndicatorTradingEngine",
    "TradingEngineConfig",
    "EngineState",
    "OrderType",
    "OrderStatus",
    "TradingOrder",
    "Position",
    
    # Data providers
    "DataProviderInterface",
    "BinanceDataProvider", 
    "PaperDataProvider",
    "MarketData",
    "HistoricalData",
    "create_data_provider",
    "get_data_provider",
    
    # Configuration
    "TradingSystemConfig",
    "DataProviderConfig",
    "BrokerConfig",
    "get_config",
    "set_config"
]


def get_system_info() -> dict:
    """Get technical indicator system information."""
    return {
        "name": "Technical Indicator Trading System",
        "version": __version__,
        "description": __description__,
        "author": __author__,
        "components": {
            "indicators": "25+ professional technical indicators",
            "strategies": "Multi-indicator strategy framework",
            "signals": "Advanced multi-timeframe signal generation",
            "risk_management": "Sophisticated portfolio risk controls",
            "trading_engine": "Real-time trading execution",
            "data_providers": "Multiple market data integrations"
        },
        "features": [
            "TA-Lib integration with extended indicators",
            "Flexible strategy building with logical operators",
            "Multi-timeframe signal consensus analysis",
            "Dynamic position sizing with correlation analysis",
            "Real-time trading with paper trading support",
            "Comprehensive risk management",
            "Professional-grade architecture",
            "Complete API integration",
            "Extensible and modular design"
        ]
    }


def validate_system_requirements() -> dict:
    """Validate system requirements and dependencies."""
    requirements = {
        "status": "checking",
        "dependencies": {},
        "issues": []
    }
    
    try:
        # Check TA-Lib
        import talib
        requirements["dependencies"]["talib"] = {
            "status": "ok",
            "version": getattr(talib, "__version__", "unknown")
        }
    except ImportError:
        requirements["dependencies"]["talib"] = {
            "status": "missing",
            "error": "TA-Lib not installed"
        }
        requirements["issues"].append("TA-Lib is required but not installed")
    
    try:
        # Check NumPy
        import numpy as np
        requirements["dependencies"]["numpy"] = {
            "status": "ok", 
            "version": np.__version__
        }
    except ImportError:
        requirements["dependencies"]["numpy"] = {
            "status": "missing",
            "error": "NumPy not installed"
        }
        requirements["issues"].append("NumPy is required but not installed")
    
    try:
        # Check configuration
        config = get_config()
        config_issues = config.validate()
        
        requirements["configuration"] = {
            "status": "ok" if not config_issues else "issues",
            "issues": config_issues
        }
        
        if config_issues:
            requirements["issues"].extend(config_issues)
            
    except Exception as e:
        requirements["configuration"] = {
            "status": "error",
            "error": str(e)
        }
        requirements["issues"].append(f"Configuration error: {e}")
    
    # Overall status
    if requirements["issues"]:
        requirements["status"] = "issues_found"
    else:
        requirements["status"] = "ok"
    
    return requirements


def initialize_system(config: TradingSystemConfig = None) -> dict:
    """Initialize the technical indicator trading system."""
    if config:
        set_config(config)
    
    # Validate requirements
    validation = validate_system_requirements()
    
    if validation["status"] != "ok":
        return {
            "status": "failed",
            "message": "System validation failed",
            "validation": validation
        }
    
    try:
        # Initialize core components
        indicators_library = TechnicalIndicatorsLibrary()
        strategy_builder = StrategyBuilder()
        signal_engine = AdvancedSignalEngine()
        
        # Get configuration
        system_config = get_config()
        
        return {
            "status": "success",
            "message": "Technical indicator trading system initialized successfully",
            "config": {
                "environment": system_config.environment,
                "data_provider": system_config.data_provider.provider,
                "broker": system_config.broker.broker,
                "live_trading_enabled": system_config.is_live_trading_enabled(),
                "supported_symbols": len(system_config.supported_symbols),
                "default_timeframes": [tf.value for tf in system_config.default_timeframes]
            },
            "components": {
                "indicators_available": len(indicators_library.get_all_available_indicators()),
                "strategy_templates": len(strategy_builder.get_strategy_templates()),
                "signal_engine_ready": True
            }
        }
        
    except Exception as e:
        return {
            "status": "failed",
            "message": f"System initialization failed: {e}",
            "error": str(e)
        }


# System metadata
SYSTEM_METADATA = {
    "name": "Technical Indicator Trading System",
    "version": __version__,
    "description": __description__,
    "capabilities": [
        "Technical Analysis",
        "Strategy Development", 
        "Signal Generation",
        "Risk Management",
        "Automated Trading",
        "Multi-Timeframe Analysis",
        "Portfolio Management"
    ],
    "supported_indicators": [
        "SMA", "EMA", "WMA", "RSI", "MACD", "BBANDS", "STOCH", "WILLR",
        "ADX", "CCI", "SAR", "OBV", "VWAP", "AD", "ATR", "MOM", "ROC",
        "STOCHRSI", "ULTOSC", "DEMA", "TEMA", "KAMA", "PPO", "SUPERTREND",
        "DONCHIAN", "KELTNER", "ICHIMOKU", "FIBONACCI"
    ],
    "supported_timeframes": [
        "1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"
    ],
    "risk_management_features": [
        "Dynamic Position Sizing",
        "Correlation Analysis", 
        "Portfolio Risk Limits",
        "Stop Loss Management",
        "Drawdown Protection",
        "Volatility Adjustment"
    ]
}

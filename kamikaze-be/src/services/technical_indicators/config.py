"""
Technical Indicators Trading System Configuration
Production configuration settings for the technical indicator system
"""

import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from .advanced_risk_management import RiskLevel, PositionSizingMethod
from .signal_engine import TimeFrame


@dataclass
class DataProviderConfig:
    """Configuration for market data providers."""
    provider: str = "binance"  # binance, alpaca, polygon, etc.
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    base_url: Optional[str] = None
    rate_limit_requests_per_minute: int = 1200
    timeout_seconds: int = 30
    retry_attempts: int = 3
    
    @classmethod
    def from_env(cls) -> 'DataProviderConfig':
        """Create configuration from environment variables."""
        return cls(
            provider=os.getenv('DATA_PROVIDER', 'binance'),
            api_key=os.getenv('DATA_PROVIDER_API_KEY'),
            api_secret=os.getenv('DATA_PROVIDER_API_SECRET'),
            base_url=os.getenv('DATA_PROVIDER_BASE_URL'),
            rate_limit_requests_per_minute=int(os.getenv('DATA_PROVIDER_RATE_LIMIT', '1200')),
            timeout_seconds=int(os.getenv('DATA_PROVIDER_TIMEOUT', '30')),
            retry_attempts=int(os.getenv('DATA_PROVIDER_RETRY_ATTEMPTS', '3'))
        )


@dataclass
class BrokerConfig:
    """Configuration for trading broker integration."""
    broker: str = "paper"  # paper, binance, alpaca, interactive_brokers, etc.
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    sandbox_mode: bool = True
    base_url: Optional[str] = None
    
    @classmethod
    def from_env(cls) -> 'BrokerConfig':
        """Create configuration from environment variables."""
        return cls(
            broker=os.getenv('BROKER', 'paper'),
            api_key=os.getenv('BROKER_API_KEY'),
            api_secret=os.getenv('BROKER_API_SECRET'),
            sandbox_mode=os.getenv('BROKER_SANDBOX_MODE', 'true').lower() == 'true',
            base_url=os.getenv('BROKER_BASE_URL')
        )


@dataclass
class TradingSystemConfig:
    """Main configuration for the technical indicator trading system."""
    
    # System settings
    environment: str = "development"  # development, staging, production
    debug_mode: bool = True
    log_level: str = "INFO"
    
    # Data settings
    data_provider: DataProviderConfig = None
    supported_symbols: List[str] = None
    default_timeframes: List[TimeFrame] = None
    data_cache_ttl_seconds: int = 300  # 5 minutes
    
    # Trading settings
    broker: BrokerConfig = None
    enable_live_trading: bool = False
    paper_trading_initial_balance: float = 100000.0
    
    # Risk management defaults
    default_max_portfolio_risk: float = 2.0
    default_max_position_risk: float = 1.0
    default_max_correlation: float = 0.7
    default_position_sizing_method: PositionSizingMethod = PositionSizingMethod.VOLATILITY_ADJUSTED
    default_risk_level: RiskLevel = RiskLevel.MODERATE
    
    # Signal generation defaults
    default_min_confidence: float = 0.6
    default_min_consensus_score: float = 0.5
    default_require_trend_alignment: bool = True
    default_min_trend_alignment: float = 0.6
    
    # Performance settings
    max_concurrent_strategies: int = 10
    max_concurrent_signals: int = 50
    indicator_calculation_timeout: int = 30
    
    # Database settings
    enable_signal_history: bool = True
    enable_performance_tracking: bool = True
    max_signal_history_days: int = 90
    
    def __post_init__(self):
        """Initialize default values after creation."""
        if self.data_provider is None:
            self.data_provider = DataProviderConfig.from_env()
        
        if self.broker is None:
            self.broker = BrokerConfig.from_env()
        
        if self.supported_symbols is None:
            self.supported_symbols = [
                'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT',
                'LTCUSDT', 'BCHUSDT', 'XLMUSDT', 'EOSUSDT', 'TRXUSDT'
            ]
        
        if self.default_timeframes is None:
            self.default_timeframes = [
                TimeFrame.M15, TimeFrame.H1, TimeFrame.H4, TimeFrame.D1
            ]
    
    @classmethod
    def from_env(cls) -> 'TradingSystemConfig':
        """Create configuration from environment variables."""
        return cls(
            environment=os.getenv('TRADING_ENVIRONMENT', 'development'),
            debug_mode=os.getenv('DEBUG_MODE', 'true').lower() == 'true',
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
            enable_live_trading=os.getenv('ENABLE_LIVE_TRADING', 'false').lower() == 'true',
            paper_trading_initial_balance=float(os.getenv('PAPER_TRADING_BALANCE', '100000.0')),
            default_max_portfolio_risk=float(os.getenv('MAX_PORTFOLIO_RISK', '2.0')),
            default_max_position_risk=float(os.getenv('MAX_POSITION_RISK', '1.0')),
            max_concurrent_strategies=int(os.getenv('MAX_CONCURRENT_STRATEGIES', '10')),
            max_concurrent_signals=int(os.getenv('MAX_CONCURRENT_SIGNALS', '50'))
        )
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []
        
        # Validate environment
        if self.environment not in ['development', 'staging', 'production']:
            issues.append(f"Invalid environment: {self.environment}")
        
        # Validate risk settings
        if self.default_max_portfolio_risk <= 0 or self.default_max_portfolio_risk > 100:
            issues.append(f"Invalid max portfolio risk: {self.default_max_portfolio_risk}")
        
        if self.default_max_position_risk <= 0 or self.default_max_position_risk > self.default_max_portfolio_risk:
            issues.append(f"Invalid max position risk: {self.default_max_position_risk}")
        
        # Validate live trading requirements
        if self.enable_live_trading:
            if self.broker.broker == "paper":
                issues.append("Live trading enabled but broker is set to 'paper'")
            
            if not self.broker.api_key or not self.broker.api_secret:
                issues.append("Live trading enabled but broker credentials not provided")
        
        # Validate data provider
        if not self.data_provider.api_key and self.data_provider.provider != "yahoo":
            issues.append(f"API key required for data provider: {self.data_provider.provider}")
        
        # Validate symbols
        if not self.supported_symbols:
            issues.append("No supported symbols configured")
        
        return issues
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == "production"
    
    def is_live_trading_enabled(self) -> bool:
        """Check if live trading is enabled and properly configured."""
        return (
            self.enable_live_trading and 
            self.broker.broker != "paper" and
            self.broker.api_key and 
            self.broker.api_secret
        )


# Global configuration instance
_config: Optional[TradingSystemConfig] = None


def get_config() -> TradingSystemConfig:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = TradingSystemConfig.from_env()
        
        # Validate configuration
        issues = _config.validate()
        if issues:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Configuration issues found: {issues}")
    
    return _config


def set_config(config: TradingSystemConfig):
    """Set the global configuration instance."""
    global _config
    _config = config


# Configuration constants
DEFAULT_INDICATOR_PERIODS = {
    'SMA': [10, 20, 50, 200],
    'EMA': [12, 26, 50, 200],
    'RSI': [14, 21],
    'MACD': [(12, 26, 9)],
    'BBANDS': [(20, 2.0)],
    'STOCH': [(14, 3, 3)],
    'ADX': [14],
    'ATR': [14],
    'CCI': [20],
    'WILLR': [14]
}

SUPPORTED_EXCHANGES = {
    'binance': {
        'name': 'Binance',
        'base_url': 'https://api.binance.com',
        'websocket_url': 'wss://stream.binance.com:9443',
        'rate_limit': 1200,
        'supports_futures': True
    },
    'coinbase': {
        'name': 'Coinbase Pro',
        'base_url': 'https://api.pro.coinbase.com',
        'websocket_url': 'wss://ws-feed.pro.coinbase.com',
        'rate_limit': 10,
        'supports_futures': False
    },
    'kraken': {
        'name': 'Kraken',
        'base_url': 'https://api.kraken.com',
        'websocket_url': 'wss://ws.kraken.com',
        'rate_limit': 15,
        'supports_futures': True
    }
}

TIMEFRAME_INTERVALS = {
    TimeFrame.M1: '1m',
    TimeFrame.M5: '5m',
    TimeFrame.M15: '15m',
    TimeFrame.M30: '30m',
    TimeFrame.H1: '1h',
    TimeFrame.H4: '4h',
    TimeFrame.D1: '1d',
    TimeFrame.W1: '1w'
}

# Risk management constants
MAX_PORTFOLIO_RISK_LIMIT = 10.0  # Maximum allowed portfolio risk percentage
MAX_POSITION_RISK_LIMIT = 5.0   # Maximum allowed single position risk percentage
MIN_RISK_REWARD_RATIO = 1.5     # Minimum acceptable risk-reward ratio
MAX_CORRELATION_THRESHOLD = 0.8  # Maximum correlation between positions
MAX_CONCENTRATION_LIMIT = 25.0   # Maximum concentration in single asset percentage

# Performance thresholds
MIN_SHARPE_RATIO = 1.0          # Minimum acceptable Sharpe ratio
MAX_DRAWDOWN_THRESHOLD = 15.0    # Maximum acceptable drawdown percentage
MIN_WIN_RATE = 40.0             # Minimum acceptable win rate percentage
MIN_PROFIT_FACTOR = 1.2         # Minimum acceptable profit factor

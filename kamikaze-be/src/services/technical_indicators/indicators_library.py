"""
Comprehensive Technical Indicators Library
Extended collection of technical indicators with professional implementations
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum
import warnings

from .ta_lib_wrapper import TALibWrapper, IndicatorResult, MultiValueIndicatorResult, IndicatorType, TALIB_AVAILABLE
from ...shared.logging_config import setup_logging

logger = setup_logging("indicators_library")


class TechnicalIndicatorsLibrary:
    """
    Comprehensive library of technical indicators with professional implementations.
    
    Features:
    - 20+ technical indicators
    - Multi-timeframe support
    - Signal generation capabilities
    - Performance optimization
    - Extensible architecture
    """
    
    def __init__(self):
        self.ta_lib = None
        self.custom_indicators = {}
        self._ta_lib_available = TALIB_AVAILABLE

        if not TALIB_AVAILABLE:
            logger.warning("TA-Lib is not available. Technical indicators will have limited functionality.")
            logger.info("To install TA-Lib:")
            logger.info("  1. Install TA-Lib C library:")
            logger.info("     - macOS: brew install ta-lib")
            logger.info("     - Ubuntu: sudo apt-get install libta-lib-dev")
            logger.info("     - Windows: Download from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib")
            logger.info("  2. Install Python wrapper: pip install TA-Lib")
        else:
            self.ta_lib = TALibWrapper()
            logger.info("Technical indicators library initialized with TA-Lib")

    def is_ta_lib_available(self) -> bool:
        """Check if TA-Lib is available."""
        return self._ta_lib_available

    def get_ta_lib_installation_help(self) -> Dict[str, str]:
        """Get TA-Lib installation instructions."""
        return {
            "status": "not_available" if not self._ta_lib_available else "available",
            "instructions": {
                "macOS": "brew install ta-lib && pip install TA-Lib",
                "ubuntu": "sudo apt-get install libta-lib-dev && pip install TA-Lib",
                "windows": "Download wheel from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib",
                "general": "pip install TA-Lib"
            },
            "help_url": "https://github.com/mrjbq7/ta-lib#installation"
        }

    # ==================== EXTENDED MOMENTUM INDICATORS ====================
    
    def momentum(self, data: Dict[str, np.ndarray], period: int = 10) -> IndicatorResult:
        """Momentum indicator."""
        if not self._ta_lib_available:
            raise ImportError("TA-Lib is required for momentum indicator. Please install TA-Lib.")

        self.ta_lib._validate_price_data(data, ['close'])
        params = self.ta_lib._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 100, 'default': 10}}
        )
        
        try:
            import talib
            values = talib.MOM(data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="MOM",
                type=IndicatorType.MOMENTUM,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating Momentum: {e}")
            raise
    
    def roc(self, data: Dict[str, np.ndarray], period: int = 10) -> IndicatorResult:
        """Rate of Change."""
        self.ta_lib._validate_price_data(data, ['close'])
        params = self.ta_lib._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 100, 'default': 10}}
        )
        
        try:
            import talib
            values = talib.ROC(data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="ROC",
                type=IndicatorType.MOMENTUM,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating ROC: {e}")
            raise
    
    def stochastic_rsi(self, data: Dict[str, np.ndarray], period: int = 14, 
                      k_period: int = 3, d_period: int = 3) -> MultiValueIndicatorResult:
        """Stochastic RSI."""
        self.ta_lib._validate_price_data(data, ['close'])
        params = self.ta_lib._validate_parameters(
            {'period': period, 'k_period': k_period, 'd_period': d_period},
            {
                'period': {'type': int, 'min': 1, 'max': 100, 'default': 14},
                'k_period': {'type': int, 'min': 1, 'max': 10, 'default': 3},
                'd_period': {'type': int, 'min': 1, 'max': 10, 'default': 3}
            }
        )
        
        try:
            import talib
            fastk, fastd = talib.STOCHRSI(
                data['close'],
                timeperiod=params['period'],
                fastk_period=params['k_period'],
                fastd_period=params['d_period']
            )
            
            return MultiValueIndicatorResult(
                name="STOCHRSI",
                type=IndicatorType.MOMENTUM,
                values={
                    'fastk': fastk,
                    'fastd': fastd
                },
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating Stochastic RSI: {e}")
            raise
    
    def ultimate_oscillator(self, data: Dict[str, np.ndarray], 
                           period1: int = 7, period2: int = 14, period3: int = 28) -> IndicatorResult:
        """Ultimate Oscillator."""
        self.ta_lib._validate_price_data(data, ['high', 'low', 'close'])
        params = self.ta_lib._validate_parameters(
            {'period1': period1, 'period2': period2, 'period3': period3},
            {
                'period1': {'type': int, 'min': 1, 'max': 50, 'default': 7},
                'period2': {'type': int, 'min': 1, 'max': 50, 'default': 14},
                'period3': {'type': int, 'min': 1, 'max': 100, 'default': 28}
            }
        )
        
        try:
            import talib
            values = talib.ULTOSC(
                data['high'], data['low'], data['close'],
                timeperiod1=params['period1'],
                timeperiod2=params['period2'],
                timeperiod3=params['period3']
            )
            
            return IndicatorResult(
                name="ULTOSC",
                type=IndicatorType.MOMENTUM,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating Ultimate Oscillator: {e}")
            raise
    
    # ==================== EXTENDED TREND INDICATORS ====================
    
    def dema(self, data: Dict[str, np.ndarray], period: int = 20) -> IndicatorResult:
        """Double Exponential Moving Average."""
        self.ta_lib._validate_price_data(data, ['close'])
        params = self.ta_lib._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 1000, 'default': 20}}
        )
        
        try:
            import talib
            values = talib.DEMA(data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="DEMA",
                type=IndicatorType.OVERLAP,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating DEMA: {e}")
            raise
    
    def tema(self, data: Dict[str, np.ndarray], period: int = 20) -> IndicatorResult:
        """Triple Exponential Moving Average."""
        self.ta_lib._validate_price_data(data, ['close'])
        params = self.ta_lib._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 1000, 'default': 20}}
        )
        
        try:
            import talib
            values = talib.TEMA(data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="TEMA",
                type=IndicatorType.OVERLAP,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating TEMA: {e}")
            raise
    
    def kama(self, data: Dict[str, np.ndarray], period: int = 30) -> IndicatorResult:
        """Kaufman Adaptive Moving Average."""
        self.ta_lib._validate_price_data(data, ['close'])
        params = self.ta_lib._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 1000, 'default': 30}}
        )
        
        try:
            import talib
            values = talib.KAMA(data['close'], timeperiod=params['period'])
            return IndicatorResult(
                name="KAMA",
                type=IndicatorType.OVERLAP,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating KAMA: {e}")
            raise
    
    def ppo(self, data: Dict[str, np.ndarray], fast_period: int = 12, 
            slow_period: int = 26) -> IndicatorResult:
        """Percentage Price Oscillator."""
        self.ta_lib._validate_price_data(data, ['close'])
        params = self.ta_lib._validate_parameters(
            {'fast_period': fast_period, 'slow_period': slow_period},
            {
                'fast_period': {'type': int, 'min': 1, 'max': 100, 'default': 12},
                'slow_period': {'type': int, 'min': 1, 'max': 200, 'default': 26}
            }
        )
        
        if params['fast_period'] >= params['slow_period']:
            raise ValueError("Fast period must be less than slow period")
        
        try:
            import talib
            values = talib.PPO(
                data['close'],
                fastperiod=params['fast_period'],
                slowperiod=params['slow_period']
            )
            
            return IndicatorResult(
                name="PPO",
                type=IndicatorType.MOMENTUM,
                values=values,
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating PPO: {e}")
            raise
    
    # ==================== CUSTOM INDICATORS ====================
    
    def supertrend(self, data: Dict[str, np.ndarray], period: int = 10, 
                   multiplier: float = 3.0) -> MultiValueIndicatorResult:
        """SuperTrend indicator (custom implementation)."""
        self.ta_lib._validate_price_data(data, ['high', 'low', 'close'])
        params = self.ta_lib._validate_parameters(
            {'period': period, 'multiplier': multiplier},
            {
                'period': {'type': int, 'min': 1, 'max': 100, 'default': 10},
                'multiplier': {'type': float, 'min': 0.1, 'max': 10.0, 'default': 3.0}
            }
        )
        
        try:
            import talib
            
            # Calculate ATR
            atr = talib.ATR(data['high'], data['low'], data['close'], timeperiod=params['period'])
            
            # Calculate HL2 (typical price)
            hl2 = (data['high'] + data['low']) / 2
            
            # Calculate basic upper and lower bands
            upper_band = hl2 + (params['multiplier'] * atr)
            lower_band = hl2 - (params['multiplier'] * atr)
            
            # Initialize arrays
            final_upper_band = np.zeros_like(upper_band)
            final_lower_band = np.zeros_like(lower_band)
            supertrend = np.zeros_like(data['close'])
            direction = np.ones_like(data['close'])  # 1 for up, -1 for down
            
            for i in range(1, len(data['close'])):
                # Final upper band
                if upper_band[i] < final_upper_band[i-1] or data['close'][i-1] > final_upper_band[i-1]:
                    final_upper_band[i] = upper_band[i]
                else:
                    final_upper_band[i] = final_upper_band[i-1]
                
                # Final lower band
                if lower_band[i] > final_lower_band[i-1] or data['close'][i-1] < final_lower_band[i-1]:
                    final_lower_band[i] = lower_band[i]
                else:
                    final_lower_band[i] = final_lower_band[i-1]
                
                # SuperTrend calculation
                if supertrend[i-1] == final_upper_band[i-1] and data['close'][i] <= final_upper_band[i]:
                    supertrend[i] = final_upper_band[i]
                    direction[i] = -1
                elif supertrend[i-1] == final_upper_band[i-1] and data['close'][i] > final_upper_band[i]:
                    supertrend[i] = final_lower_band[i]
                    direction[i] = 1
                elif supertrend[i-1] == final_lower_band[i-1] and data['close'][i] >= final_lower_band[i]:
                    supertrend[i] = final_lower_band[i]
                    direction[i] = 1
                elif supertrend[i-1] == final_lower_band[i-1] and data['close'][i] < final_lower_band[i]:
                    supertrend[i] = final_upper_band[i]
                    direction[i] = -1
                else:
                    supertrend[i] = supertrend[i-1]
                    direction[i] = direction[i-1]
            
            return MultiValueIndicatorResult(
                name="SUPERTREND",
                type=IndicatorType.TREND,
                values={
                    'supertrend': supertrend,
                    'direction': direction,
                    'upper_band': final_upper_band,
                    'lower_band': final_lower_band
                },
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating SuperTrend: {e}")
            raise
    
    def donchian_channels(self, data: Dict[str, np.ndarray], period: int = 20) -> MultiValueIndicatorResult:
        """Donchian Channels."""
        self.ta_lib._validate_price_data(data, ['high', 'low', 'close'])
        params = self.ta_lib._validate_parameters(
            {'period': period},
            {'period': {'type': int, 'min': 1, 'max': 100, 'default': 20}}
        )
        
        try:
            import talib
            
            upper_channel = talib.MAX(data['high'], timeperiod=params['period'])
            lower_channel = talib.MIN(data['low'], timeperiod=params['period'])
            middle_channel = (upper_channel + lower_channel) / 2
            
            return MultiValueIndicatorResult(
                name="DONCHIAN",
                type=IndicatorType.VOLATILITY,
                values={
                    'upper': upper_channel,
                    'middle': middle_channel,
                    'lower': lower_channel
                },
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating Donchian Channels: {e}")
            raise
    
    def keltner_channels(self, data: Dict[str, np.ndarray], period: int = 20, 
                        multiplier: float = 2.0) -> MultiValueIndicatorResult:
        """Keltner Channels."""
        self.ta_lib._validate_price_data(data, ['high', 'low', 'close'])
        params = self.ta_lib._validate_parameters(
            {'period': period, 'multiplier': multiplier},
            {
                'period': {'type': int, 'min': 1, 'max': 100, 'default': 20},
                'multiplier': {'type': float, 'min': 0.1, 'max': 10.0, 'default': 2.0}
            }
        )
        
        try:
            import talib
            
            # Middle line (EMA of typical price)
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            middle_line = talib.EMA(typical_price, timeperiod=params['period'])
            
            # ATR for channel width
            atr = talib.ATR(data['high'], data['low'], data['close'], timeperiod=params['period'])
            
            # Upper and lower channels
            upper_channel = middle_line + (params['multiplier'] * atr)
            lower_channel = middle_line - (params['multiplier'] * atr)
            
            return MultiValueIndicatorResult(
                name="KELTNER",
                type=IndicatorType.VOLATILITY,
                values={
                    'upper': upper_channel,
                    'middle': middle_line,
                    'lower': lower_channel
                },
                parameters=params
            )
        except Exception as e:
            logger.error(f"Error calculating Keltner Channels: {e}")
            raise
    
    # ==================== UTILITY METHODS ====================
    
    def get_all_available_indicators(self) -> Dict[str, Dict[str, Any]]:
        """Get comprehensive list of all available indicators."""
        if not self._ta_lib_available:
            return {
                'ERROR': {
                    'name': 'TA-Lib Not Available',
                    'type': 'ERROR',
                    'message': 'TA-Lib is required for technical indicators',
                    'installation_help': self.get_ta_lib_installation_help()
                }
            }

        base_indicators = self.ta_lib.get_available_indicators()
        
        extended_indicators = {
            'MOM': {
                'name': 'Momentum',
                'type': IndicatorType.MOMENTUM,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 100, 'default': 10}},
                'required_data': ['close']
            },
            'ROC': {
                'name': 'Rate of Change',
                'type': IndicatorType.MOMENTUM,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 100, 'default': 10}},
                'required_data': ['close']
            },
            'STOCHRSI': {
                'name': 'Stochastic RSI',
                'type': IndicatorType.MOMENTUM,
                'parameters': {
                    'period': {'type': int, 'min': 1, 'max': 100, 'default': 14},
                    'k_period': {'type': int, 'min': 1, 'max': 10, 'default': 3},
                    'd_period': {'type': int, 'min': 1, 'max': 10, 'default': 3}
                },
                'required_data': ['close'],
                'multi_value': True
            },
            'ULTOSC': {
                'name': 'Ultimate Oscillator',
                'type': IndicatorType.MOMENTUM,
                'parameters': {
                    'period1': {'type': int, 'min': 1, 'max': 50, 'default': 7},
                    'period2': {'type': int, 'min': 1, 'max': 50, 'default': 14},
                    'period3': {'type': int, 'min': 1, 'max': 100, 'default': 28}
                },
                'required_data': ['high', 'low', 'close']
            },
            'DEMA': {
                'name': 'Double Exponential Moving Average',
                'type': IndicatorType.OVERLAP,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 1000, 'default': 20}},
                'required_data': ['close']
            },
            'TEMA': {
                'name': 'Triple Exponential Moving Average',
                'type': IndicatorType.OVERLAP,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 1000, 'default': 20}},
                'required_data': ['close']
            },
            'KAMA': {
                'name': 'Kaufman Adaptive Moving Average',
                'type': IndicatorType.OVERLAP,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 1000, 'default': 30}},
                'required_data': ['close']
            },
            'PPO': {
                'name': 'Percentage Price Oscillator',
                'type': IndicatorType.MOMENTUM,
                'parameters': {
                    'fast_period': {'type': int, 'min': 1, 'max': 100, 'default': 12},
                    'slow_period': {'type': int, 'min': 1, 'max': 200, 'default': 26}
                },
                'required_data': ['close']
            },
            'SUPERTREND': {
                'name': 'SuperTrend',
                'type': IndicatorType.TREND,
                'parameters': {
                    'period': {'type': int, 'min': 1, 'max': 100, 'default': 10},
                    'multiplier': {'type': float, 'min': 0.1, 'max': 10.0, 'default': 3.0}
                },
                'required_data': ['high', 'low', 'close'],
                'multi_value': True
            },
            'DONCHIAN': {
                'name': 'Donchian Channels',
                'type': IndicatorType.VOLATILITY,
                'parameters': {'period': {'type': int, 'min': 1, 'max': 100, 'default': 20}},
                'required_data': ['high', 'low', 'close'],
                'multi_value': True
            },
            'KELTNER': {
                'name': 'Keltner Channels',
                'type': IndicatorType.VOLATILITY,
                'parameters': {
                    'period': {'type': int, 'min': 1, 'max': 100, 'default': 20},
                    'multiplier': {'type': float, 'min': 0.1, 'max': 10.0, 'default': 2.0}
                },
                'required_data': ['high', 'low', 'close'],
                'multi_value': True
            }
        }
        
        # Merge base and extended indicators
        all_indicators = {**base_indicators, **extended_indicators}
        return all_indicators
    
    def calculate_indicator(self, indicator_name: str, data: Dict[str, np.ndarray],
                          **params) -> Union[IndicatorResult, MultiValueIndicatorResult]:
        """Calculate any indicator by name with parameters."""
        if not self._ta_lib_available:
            raise ImportError(f"TA-Lib is required to calculate {indicator_name} indicator. Please install TA-Lib.")

        indicator_name = indicator_name.upper()
        
        # Map indicator names to methods
        indicator_methods = {
            # Base indicators from TALibWrapper
            'SMA': self.ta_lib.sma,
            'EMA': self.ta_lib.ema,
            'WMA': self.ta_lib.wma,
            'MACD': self.ta_lib.macd,
            'RSI': self.ta_lib.rsi,
            'STOCH': self.ta_lib.stoch,
            'WILLR': self.ta_lib.williams_r,
            'BBANDS': self.ta_lib.bollinger_bands,
            'ATR': self.ta_lib.atr,
            'ADX': self.ta_lib.adx,
            'CCI': self.ta_lib.cci,
            'SAR': self.ta_lib.parabolic_sar,
            'OBV': self.ta_lib.obv,
            'VWAP': self.ta_lib.vwap,
            'AD': self.ta_lib.ad_line,
            'ICHIMOKU': self.ta_lib.ichimoku_cloud,
            'FIBONACCI': self.ta_lib.fibonacci_retracements,
            # Extended indicators
            'MOM': self.momentum,
            'ROC': self.roc,
            'STOCHRSI': self.stochastic_rsi,
            'ULTOSC': self.ultimate_oscillator,
            'DEMA': self.dema,
            'TEMA': self.tema,
            'KAMA': self.kama,
            'PPO': self.ppo,
            'SUPERTREND': self.supertrend,
            'DONCHIAN': self.donchian_channels,
            'KELTNER': self.keltner_channels
        }
        
        if indicator_name not in indicator_methods:
            raise ValueError(f"Unknown indicator: {indicator_name}")
        
        method = indicator_methods[indicator_name]
        return method(data, **params)


# Global instance
indicators_library = TechnicalIndicatorsLibrary() if TALIB_AVAILABLE else None

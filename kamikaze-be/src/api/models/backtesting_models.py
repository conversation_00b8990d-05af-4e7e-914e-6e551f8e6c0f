"""
Backtesting API Models
Pydantic models for backtesting API requests and responses
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, field_validator
from decimal import Decimal


class BacktestingStrategy(BaseModel):
    """Trading strategy configuration."""
    id: str = Field(..., description="Strategy identifier")
    name: str = Field(..., description="Strategy name")
    description: Optional[str] = Field(None, description="Strategy description")
    category: str = Field(..., description="Strategy category")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Strategy parameters")


class RiskManagementConfig(BaseModel):
    """Risk management configuration."""
    stop_loss: Optional[float] = Field(None, description="Stop loss percentage")
    take_profit: Optional[float] = Field(None, description="Take profit percentage")
    position_sizing: str = Field(default="percentage", description="Position sizing method")
    max_position_size: float = Field(default=10.0, description="Maximum position size percentage")
    max_drawdown: float = Field(default=20.0, description="Maximum drawdown percentage")
    max_portfolio_risk: Optional[float] = Field(None, description="Maximum portfolio risk percentage")
    enable_dynamic_sizing: bool = Field(default=True, description="Enable dynamic position sizing")


class BacktestingConfigRequest(BaseModel):
    """Request model for starting a backtest."""
    strategy: BacktestingStrategy = Field(..., description="Trading strategy configuration")
    trading_pairs: List[str] = Field(..., description="List of trading pairs to backtest")
    timeframe: str = Field(..., description="Timeframe for backtesting")
    start_date: datetime = Field(..., description="Backtest start date")
    end_date: datetime = Field(..., description="Backtest end date")
    initial_capital: float = Field(..., gt=0, description="Initial capital amount")
    commission: float = Field(default=0.001, ge=0, le=0.1, description="Commission rate")
    slippage: float = Field(default=0.001, ge=0, le=0.1, description="Slippage rate")
    risk_management: Optional[RiskManagementConfig] = Field(None, description="Risk management settings")
    benchmark: Optional[str] = Field(None, description="Benchmark symbol for comparison")
    name: Optional[str] = Field(None, description="Custom name for the backtest")
    
    @field_validator('timeframe')
    @classmethod
    def validate_timeframe(cls, v):
        valid_timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
        if v not in valid_timeframes:
            raise ValueError(f'Timeframe must be one of: {valid_timeframes}')
        return v
    
    @field_validator('trading_pairs')
    @classmethod
    def validate_trading_pairs(cls, v):
        if not v or len(v) == 0:
            raise ValueError('At least one trading pair is required')
        return v
    
    @field_validator('end_date')
    @classmethod
    def validate_date_range(cls, v, info):
        if info.data and 'start_date' in info.data and v <= info.data['start_date']:
            raise ValueError('End date must be after start date')
        return v


class BacktestingResponse(BaseModel):
    """Response model for backtest start request."""
    success: bool = Field(..., description="Request success status")
    message: str = Field(..., description="Response message")
    backtest_id: Optional[str] = Field(None, description="Unique backtest identifier")
    status: Optional[str] = Field(None, description="Current backtest status")


class BacktestingProgressResponse(BaseModel):
    """Response model for backtest progress."""
    backtest_id: str = Field(..., description="Backtest identifier")
    status: str = Field(..., description="Current status")
    progress: int = Field(..., ge=0, le=100, description="Progress percentage")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")


class TradeResult(BaseModel):
    """Individual trade result."""
    id: str = Field(..., description="Trade identifier")
    symbol: str = Field(..., description="Trading pair symbol")
    side: str = Field(..., description="Trade side (BUY/SELL)")
    entry_time: str = Field(..., description="Entry timestamp")
    exit_time: Optional[str] = Field(None, description="Exit timestamp")
    entry_price: float = Field(..., description="Entry price")
    exit_price: Optional[float] = Field(None, description="Exit price")
    quantity: float = Field(..., description="Trade quantity")
    pnl: float = Field(..., description="Profit/Loss amount")
    pnl_percent: float = Field(..., description="Profit/Loss percentage")
    duration_minutes: Optional[int] = Field(None, description="Trade duration in minutes")
    entry_reason: Optional[str] = Field(None, description="Entry reason")
    exit_reason: Optional[str] = Field(None, description="Exit reason")


class EquityPoint(BaseModel):
    """Equity curve data point."""
    timestamp: str = Field(..., description="Timestamp")
    value: float = Field(..., description="Portfolio value")
    drawdown: float = Field(..., description="Drawdown amount")
    drawdown_percent: float = Field(..., description="Drawdown percentage")


class MonthlyReturn(BaseModel):
    """Monthly return data."""
    month: str = Field(..., description="Month name")
    return_value: float = Field(..., alias="return", description="Monthly return percentage")


class PerformanceMetrics(BaseModel):
    """Comprehensive performance metrics."""
    total_return: float = Field(..., description="Total return amount")
    total_return_percent: float = Field(..., description="Total return percentage")
    annualized_return: float = Field(..., description="Annualized return percentage")
    sharpe_ratio: float = Field(..., description="Sharpe ratio")
    sortino_ratio: float = Field(..., description="Sortino ratio")
    max_drawdown: float = Field(..., description="Maximum drawdown amount")
    max_drawdown_percent: float = Field(..., description="Maximum drawdown percentage")
    win_rate: float = Field(..., description="Win rate percentage")
    profit_factor: float = Field(..., description="Profit factor")
    total_trades: int = Field(..., description="Total number of trades")
    winning_trades: int = Field(..., description="Number of winning trades")
    losing_trades: int = Field(..., description="Number of losing trades")
    average_win: float = Field(..., description="Average winning trade amount")
    average_loss: float = Field(..., description="Average losing trade amount")
    largest_win: float = Field(..., description="Largest winning trade")
    largest_loss: float = Field(..., description="Largest losing trade")
    average_trade_duration: float = Field(..., description="Average trade duration in hours")
    volatility: float = Field(..., description="Annualized volatility percentage")
    calmar_ratio: float = Field(..., description="Calmar ratio")
    recovery_factor: float = Field(..., description="Recovery factor")
    payoff_ratio: float = Field(..., description="Payoff ratio")
    expected_value: float = Field(..., description="Expected value per trade")


class BenchmarkComparison(BaseModel):
    """Benchmark comparison metrics."""
    benchmark: str = Field(..., description="Benchmark symbol")
    benchmark_return: float = Field(..., description="Benchmark return percentage")
    alpha: float = Field(..., description="Alpha (excess return)")
    beta: float = Field(..., description="Beta (market sensitivity)")
    correlation: float = Field(..., description="Correlation with benchmark")
    information_ratio: float = Field(..., description="Information ratio")


class BacktestingResultsResponse(BaseModel):
    """Complete backtest results response."""
    id: str = Field(..., description="Backtest identifier")
    config: Dict[str, Any] = Field(..., description="Backtest configuration")
    start_time: str = Field(..., description="Backtest start time")
    end_time: Optional[str] = Field(None, description="Backtest end time")
    status: str = Field(..., description="Backtest status")
    progress: int = Field(..., description="Progress percentage")
    trades: List[TradeResult] = Field(default_factory=list, description="List of trades")
    metrics: PerformanceMetrics = Field(..., description="Performance metrics")
    equity: List[EquityPoint] = Field(default_factory=list, description="Equity curve")
    monthly_returns: List[MonthlyReturn] = Field(default_factory=list, description="Monthly returns")
    benchmark_comparison: Optional[BenchmarkComparison] = Field(None, description="Benchmark comparison")


class BacktestSummary(BaseModel):
    """Summary of a backtest for history listing."""
    id: str = Field(..., description="Backtest identifier")
    name: str = Field(..., description="Backtest name")
    strategy_name: str = Field(..., description="Strategy name")
    trading_pairs: List[str] = Field(..., description="Trading pairs")
    timeframe: str = Field(..., description="Timeframe")
    start_date: str = Field(..., description="Start date")
    end_date: str = Field(..., description="End date")
    initial_capital: float = Field(..., description="Initial capital")
    status: str = Field(..., description="Status")
    progress: int = Field(..., description="Progress percentage")
    created_at: str = Field(..., description="Creation timestamp")
    completed_at: Optional[str] = Field(None, description="Completion timestamp")
    total_return: Optional[float] = Field(None, description="Total return")
    total_return_percent: Optional[float] = Field(None, description="Total return percentage")
    sharpe_ratio: Optional[float] = Field(None, description="Sharpe ratio")
    max_drawdown_percent: Optional[float] = Field(None, description="Maximum drawdown percentage")
    total_trades: Optional[int] = Field(None, description="Total trades")


class BacktestingHistoryResponse(BaseModel):
    """Response model for backtest history."""
    backtests: List[BacktestSummary] = Field(..., description="List of backtests")
    total: int = Field(..., description="Total number of backtests")
    limit: int = Field(..., description="Results limit")
    offset: int = Field(..., description="Results offset")


class StrategyParameter(BaseModel):
    """Strategy parameter definition."""
    type: str = Field(..., description="Parameter type")
    default: Union[int, float, str, bool] = Field(..., description="Default value")
    min: Optional[Union[int, float]] = Field(None, description="Minimum value")
    max: Optional[Union[int, float]] = Field(None, description="Maximum value")
    options: Optional[List[str]] = Field(None, description="Valid options")
    description: Optional[str] = Field(None, description="Parameter description")


class AvailableStrategy(BaseModel):
    """Available strategy definition."""
    id: str = Field(..., description="Strategy identifier")
    name: str = Field(..., description="Strategy name")
    description: str = Field(..., description="Strategy description")
    category: str = Field(..., description="Strategy category")
    parameters: Dict[str, StrategyParameter] = Field(..., description="Strategy parameters")


class StrategiesResponse(BaseModel):
    """Response model for available strategies."""
    success: bool = Field(..., description="Request success")
    strategies: List[AvailableStrategy] = Field(..., description="Available strategies")


class BacktestingStatsResponse(BaseModel):
    """Response model for backtesting statistics."""
    total_backtests: int = Field(..., description="Total number of backtests")
    completed_backtests: int = Field(..., description="Number of completed backtests")
    running_backtests: int = Field(..., description="Number of running backtests")
    failed_backtests: int = Field(..., description="Number of failed backtests")
    average_duration_minutes: float = Field(..., description="Average backtest duration")
    most_used_strategy: Optional[str] = Field(None, description="Most frequently used strategy")
    best_performing_strategy: Optional[str] = Field(None, description="Best performing strategy")


class BacktestComparisonRequest(BaseModel):
    """Request model for comparing multiple backtests."""
    backtest_ids: List[str] = Field(..., min_length=2, max_length=10, description="Backtest IDs to compare")
    metrics: Optional[List[str]] = Field(None, description="Specific metrics to compare")


class BacktestComparisonResponse(BaseModel):
    """Response model for backtest comparison."""
    backtests: List[BacktestSummary] = Field(..., description="Compared backtests")
    comparison_metrics: Dict[str, List[float]] = Field(..., description="Comparison metrics")
    rankings: Dict[str, List[str]] = Field(..., description="Rankings by metric")


class OptimizationRequest(BaseModel):
    """Request model for strategy optimization."""
    base_config: BacktestingConfigRequest = Field(..., description="Base configuration")
    parameter_ranges: Dict[str, Dict[str, Union[int, float]]] = Field(..., description="Parameter ranges to optimize")
    optimization_metric: str = Field(default="sharpe_ratio", description="Metric to optimize")
    max_iterations: int = Field(default=100, le=1000, description="Maximum optimization iterations")


class OptimizationResult(BaseModel):
    """Single optimization result."""
    parameters: Dict[str, Any] = Field(..., description="Parameter values")
    metric_value: float = Field(..., description="Optimization metric value")
    backtest_id: str = Field(..., description="Associated backtest ID")


class OptimizationResponse(BaseModel):
    """Response model for strategy optimization."""
    success: bool = Field(..., description="Optimization success")
    best_parameters: Dict[str, Any] = Field(..., description="Best parameter combination")
    best_metric_value: float = Field(..., description="Best metric value achieved")
    results: List[OptimizationResult] = Field(..., description="All optimization results")
    optimization_id: str = Field(..., description="Optimization run identifier")

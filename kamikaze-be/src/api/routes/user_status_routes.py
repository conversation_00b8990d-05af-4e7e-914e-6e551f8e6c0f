"""
User Status API Routes
Manages user connection status for live trading
"""

import logging
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field

from ...infrastructure.credentials_database import credentials_db
from ...services.binance_connection_service import binance_service
from .auth_routes import get_current_user

# Setup logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/user", tags=["User Status"])

# ============================================================================
# Pydantic Models
# ============================================================================


class UserStatusResponse(BaseModel):
    """Response model for user status."""

    success: bool
    message: str
    status: Optional[Dict[str, Any]] = None


class EnvironmentSwitchRequest(BaseModel):
    """Request model for environment switching."""

    environment: str = Field(..., description="Environment: 'live' only")


class ConnectionStatusResponse(BaseModel):
    """Response model for connection status."""

    success: bool
    message: str
    connection_status: Optional[Dict[str, Any]] = None


# ============================================================================
# User Status Routes
# ============================================================================


@router.get("/status", response_model=UserStatusResponse)
async def get_user_status(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Get comprehensive user status including credentials and connection info."""
    try:
        if not await credentials_db.ensure_connected():
            raise HTTPException(
                status_code=503, detail="Database service not available"
            )

        user_id = current_user["id"]

        # Get Binance credentials status
        binance_creds = await credentials_db.get_user_binance_credentials(user_id)

        # Determine current environment preference (mainnet only)
        current_environment = "disconnected"
        if binance_creds["mainnet"]:
            current_environment = "live"

        # Build status response
        status = {
            "user_info": {
                "id": current_user["id"],
                "email": current_user["email"],
                "username": current_user["username"],
                "full_name": current_user.get("full_name"),
            },
            "connection_status": {
                "current_environment": current_environment,
                "is_connected": current_environment != "disconnected",
            },
            "credentials": {
                "binance_mainnet": {
                    "configured": binance_creds["mainnet"] is not None,
                    "created_at": (
                        binance_creds["mainnet"]["created_at"]
                        if binance_creds["mainnet"]
                        else None
                    ),
                    "updated_at": (
                        binance_creds["mainnet"]["updated_at"]
                        if binance_creds["mainnet"]
                        else None
                    ),
                },
            },
            "available_environments": [],
        }

        # Determine available environments (mainnet only)
        if binance_creds["mainnet"]:
            status["available_environments"].append("live")

        return UserStatusResponse(
            success=True, message="User status retrieved successfully", status=status
        )

    except Exception as e:
        logger.error(f"Error getting user status: {e}")
        return UserStatusResponse(
            success=False, message="An error occurred while retrieving user status"
        )


@router.get("/connection-status", response_model=ConnectionStatusResponse)
async def get_connection_status(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Get detailed connection status for all configured exchanges."""
    try:
        if not await credentials_db.ensure_connected():
            raise HTTPException(
                status_code=503, detail="Database service not available"
            )

        user_id = current_user["id"]

        # Get all credentials (mainnet only)
        binance_creds = await credentials_db.get_user_binance_credentials(user_id)

        connection_status = {
            "binance_mainnet": {
                "configured": False,
                "connected": False,
                "status": "not_configured",
            },
            "overall_status": "disconnected",
            "active_environment": None,
            "last_tested": None,
        }

        # Test Binance mainnet connection
        if binance_creds["mainnet"]:
            connection_status["binance_mainnet"]["configured"] = True
            try:
                mainnet_creds = await credentials_db.get_binance_credentials(
                    user_id, is_mainnet=True
                )
                if mainnet_creds:
                    async with binance_service as service:
                        test_result = await service.test_connection(
                            mainnet_creds["api_key"],
                            mainnet_creds["secret_key"],
                            is_testnet=False,
                        )
                        connection_status["binance_mainnet"]["connected"] = test_result[
                            "success"
                        ]
                        connection_status["binance_mainnet"]["status"] = (
                            "connected" if test_result["success"] else "error"
                        )
                        connection_status["binance_mainnet"]["error"] = (
                            test_result.get("error")
                            if not test_result["success"]
                            else None
                        )

                        if test_result["success"]:
                            connection_status["overall_status"] = "connected"
                            connection_status["active_environment"] = "live"
            except Exception as e:
                connection_status["binance_mainnet"]["status"] = "error"
                connection_status["binance_mainnet"]["error"] = str(e)



        return ConnectionStatusResponse(
            success=True,
            message="Connection status retrieved successfully",
            connection_status=connection_status,
        )

    except Exception as e:
        logger.error(f"Error getting connection status: {e}")
        return ConnectionStatusResponse(
            success=False, message="An error occurred while checking connection status"
        )


@router.post("/switch-environment", response_model=UserStatusResponse)
async def switch_environment(
    request: EnvironmentSwitchRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Switch user's active trading environment."""
    try:
        if not await credentials_db.ensure_connected():
            raise HTTPException(
                status_code=503, detail="Database service not available"
            )

        user_id = current_user["id"]

        # Validate environment (live only)
        if request.environment not in ["live"]:
            return UserStatusResponse(
                success=False,
                message="Invalid environment. Only 'live' is supported",
            )

        # Check if user has credentials for the requested environment
        binance_creds = await credentials_db.get_user_binance_credentials(user_id)

        if request.environment == "live":
            if not binance_creds["mainnet"]:
                return UserStatusResponse(
                    success=False,
                    message="No mainnet credentials configured. Please add Binance mainnet credentials first.",
                )

            # Test mainnet connection
            mainnet_creds = await credentials_db.get_binance_credentials(
                user_id, is_mainnet=True
            )
            async with binance_service as service:
                test_result = await service.test_connection(
                    mainnet_creds["api_key"],
                    mainnet_creds["secret_key"],
                    is_testnet=False,
                )

                if not test_result["success"]:
                    return UserStatusResponse(
                        success=False,
                        message=f"Cannot switch to live environment: {test_result['message']}",
                    )



        # Environment switch successful
        return UserStatusResponse(
            success=True,
            message=f"Successfully switched to {request.environment} environment",
            status={
                "active_environment": request.environment,
                "switched_at": "now",  # In a real implementation, you might store this in the database
            },
        )

    except Exception as e:
        logger.error(f"Error switching environment: {e}")
        return UserStatusResponse(
            success=False, message="An error occurred while switching environment"
        )


@router.get("/available-environments")
async def get_available_environments(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Get list of available environments for the user."""
    try:
        if not await credentials_db.ensure_connected():
            raise HTTPException(
                status_code=503, detail="Database service not available"
            )

        user_id = current_user["id"]

        # Get credentials (mainnet only)
        binance_creds = await credentials_db.get_user_binance_credentials(user_id)

        environments = []

        # Check mainnet availability
        if binance_creds["mainnet"]:
            environments.append(
                {
                    "environment": "live",
                    "display_name": "Live Trading",
                    "description": "Real Binance account with actual funds",
                    "status": "available",
                }
            )
        else:
            environments.append(
                {
                    "environment": "live",
                    "display_name": "Live Trading",
                    "description": "Real Binance account with actual funds",
                    "status": "not_configured",
                }
            )

        return {
            "success": True,
            "message": "Available environments retrieved successfully",
            "environments": environments,
        }

    except Exception as e:
        logger.error(f"Error getting available environments: {e}")
        return {
            "success": False,
            "message": "An error occurred while retrieving environments",
        }

"""
Technical Indicators Trading API Routes
FastAPI endpoints for technical indicator trading system
"""

import asyncio
import logging
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, WebSocket, WebSocketDisconnect
from pydantic import BaseModel, Field, validator

from ...services.technical_indicators import (
    TechnicalIndicatorsLibrary,
    StrategyBuilder,
    get_strategy_builder,
    AdvancedSignalEngine,
    SignalFilter,
    TimeFrame,
    TechnicalIndicatorTradingEngine,
    TradingEngineConfig,
    EngineState,
    AdvancedRiskManager,
    RiskManagementConfig,
    RiskLevel,
    PositionSizingMethod,
    get_config,
    validate_system_requirements,
    get_system_info
)
from ...shared.logging_config import setup_logging
from ..routes.auth_routes import get_current_user

logger = setup_logging("technical_indicators_routes")

# Create router
router = APIRouter(prefix="/api/v1/technical-indicators", tags=["Technical Indicators"])

# Global instances
indicators_library: Optional[TechnicalIndicatorsLibrary] = None
strategy_builder: Optional[StrategyBuilder] = None
signal_engine: Optional[AdvancedSignalEngine] = None
trading_engines: Dict[str, TechnicalIndicatorTradingEngine] = {}
websocket_connections: Dict[str, List[WebSocket]] = {}


# ==================== REQUEST/RESPONSE MODELS ====================

class IndicatorCalculationRequest(BaseModel):
    """Request model for indicator calculation."""
    indicator_name: str = Field(..., description="Name of the technical indicator")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Indicator parameters")
    market_data: Dict[str, List[float]] = Field(..., description="Market data (OHLCV)")


class IndicatorCalculationResponse(BaseModel):
    """Response model for indicator calculation."""
    indicator_name: str
    values: List[Optional[float]]
    parameters: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = None


class StrategyConditionRequest(BaseModel):
    """Request model for strategy condition."""
    indicator_name: str
    indicator_params: Dict[str, Any]
    comparison_operator: str
    threshold_value: Optional[float] = None
    threshold_value_2: Optional[float] = None
    indicator_field: Optional[str] = None
    weight: float = 1.0
    description: str = ""


class StrategyRuleRequest(BaseModel):
    """Request model for strategy rule."""
    name: str
    entry_conditions: List[StrategyConditionRequest]
    logical_operator: str = "AND"
    signal_type: str = "BUY"
    exit_conditions: Optional[List[StrategyConditionRequest]] = None
    min_confidence: float = 0.5
    stop_loss_pct: Optional[float] = None
    take_profit_pct: Optional[float] = None
    description: str = ""


class CreateStrategyRequest(BaseModel):
    """Request model for creating a strategy."""
    name: str
    description: str = ""
    rules: List[StrategyRuleRequest]


class TradingEngineConfigRequest(BaseModel):
    """Request model for trading engine configuration."""
    max_concurrent_orders: int = 10
    enable_paper_trading: bool = True
    max_daily_trades: int = 50
    max_daily_loss_pct: float = 5.0
    commission_rate: float = 0.001
    risk_management: Dict[str, Any] = Field(default_factory=dict)


class SignalFilterRequest(BaseModel):
    """Request model for signal filter."""
    min_confidence: float = 0.6
    min_consensus_score: float = 0.5
    min_quality: str = "FAIR"
    require_trend_alignment: bool = True
    min_trend_alignment: float = 0.6
    max_signals_per_timeframe: int = 3


# ==================== INITIALIZATION ====================

async def get_indicators_library():
    """Get or create indicators library instance."""
    global indicators_library
    if indicators_library is None:
        try:
            indicators_library = TechnicalIndicatorsLibrary()
        except ImportError as e:
            # Return a placeholder that indicates TA-Lib is not available
            class MockIndicatorsLibrary:
                def get_all_available_indicators(self):
                    return {
                        'ERROR': {
                            'name': 'TA-Lib Not Available',
                            'type': 'ERROR',
                            'message': str(e),
                            'installation_help': {
                                'macOS': 'brew install ta-lib && pip install TA-Lib',
                                'ubuntu': 'sudo apt-get install libta-lib-dev && pip install TA-Lib',
                                'windows': 'Download wheel from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib'
                            }
                        }
                    }

                def calculate_indicator(self, *args, **kwargs):
                    raise ImportError(str(e))

            indicators_library = MockIndicatorsLibrary()
    return indicators_library


async def get_strategy_builder_instance():
    """Get or create strategy builder instance."""
    global strategy_builder
    if strategy_builder is None:
        strategy_builder = get_strategy_builder()
    return strategy_builder


async def get_signal_engine():
    """Get or create signal engine instance."""
    global signal_engine
    if signal_engine is None:
        signal_engine = AdvancedSignalEngine()
    return signal_engine


# ==================== INDICATOR ENDPOINTS ====================

@router.get("/indicators")
async def get_available_indicators():
    """Get list of all available technical indicators."""
    try:
        library = await get_indicators_library()
        indicators = library.get_all_available_indicators()

        # Check if TA-Lib is available
        ta_lib_available = 'ERROR' not in indicators

        return {
            "success": True,
            "ta_lib_available": ta_lib_available,
            "indicators": indicators,
            "total_count": len(indicators)
        }
    except Exception as e:
        logger.error(f"Error getting available indicators: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/indicators/installation-help")
async def get_talib_installation_help():
    """Get TA-Lib installation instructions."""
    return {
        "success": True,
        "ta_lib_installation": {
            "description": "TA-Lib is required for technical indicators functionality",
            "steps": {
                "1": "Install TA-Lib C library",
                "2": "Install Python wrapper"
            },
            "instructions": {
                "macOS": {
                    "c_library": "brew install ta-lib",
                    "python_wrapper": "pip install TA-Lib"
                },
                "ubuntu": {
                    "c_library": "sudo apt-get install libta-lib-dev",
                    "python_wrapper": "pip install TA-Lib"
                },
                "windows": {
                    "c_library": "Download from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib",
                    "python_wrapper": "pip install downloaded_wheel.whl"
                },
                "general": {
                    "python_wrapper": "pip install TA-Lib"
                }
            },
            "help_urls": [
                "https://github.com/mrjbq7/ta-lib#installation",
                "https://ta-lib.org/hdr_dw.html"
            ],
            "troubleshooting": {
                "common_issues": [
                    "Make sure C library is installed before Python wrapper",
                    "On Windows, use pre-compiled wheels from Christoph Gohlke",
                    "On macOS, you may need to install Xcode command line tools"
                ]
            }
        }
    }


@router.post("/indicators/calculate")
async def calculate_indicator(
    request: IndicatorCalculationRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Calculate a technical indicator with given parameters."""
    try:
        library = await get_indicators_library()
        
        # Convert market data to numpy arrays
        import numpy as np
        market_data = {}
        for key, values in request.market_data.items():
            if not values:
                raise HTTPException(status_code=400, detail=f"Empty data for field '{key}'")
            market_data[key] = np.array(values, dtype=float)
        
        # Calculate indicator
        result = library.calculate_indicator(
            request.indicator_name,
            market_data,
            **request.parameters
        )
        
        # Format response
        if hasattr(result, 'values') and hasattr(result.values, 'tolist'):
            # Single value indicator
            values = [float(v) if not np.isnan(v) else None for v in result.values.tolist()]
            response = IndicatorCalculationResponse(
                indicator_name=result.name,
                values=values,
                parameters=result.parameters,
                metadata={'type': result.type.value}
            )
        else:
            # Multi-value indicator
            values = {}
            for key, array in result.values.items():
                values[key] = [float(v) if not np.isnan(v) else None for v in array.tolist()]
            
            response = IndicatorCalculationResponse(
                indicator_name=result.name,
                values=values,
                parameters=result.parameters,
                metadata={'type': result.type.value, 'multi_value': True}
            )
        
        return {"success": True, "result": response}
        
    except Exception as e:
        logger.error(f"Error calculating indicator {request.indicator_name}: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# ==================== STRATEGY ENDPOINTS ====================

@router.get("/strategies/templates")
async def get_strategy_templates():
    """Get available strategy templates."""
    try:
        builder = await get_strategy_builder_instance()
        templates = builder.get_strategy_templates()
        
        return {
            "success": True,
            "templates": {
                name: {
                    "name": template.name,
                    "description": template.description,
                    "category": template.category,
                    "complexity_level": template.complexity_level,
                    "indicators_used": template.indicators_used,
                    "expected_performance": template.expected_performance
                }
                for name, template in templates.items()
            }
        }
    except Exception as e:
        logger.error(f"Error getting strategy templates: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/strategies/create")
async def create_strategy(
    request: CreateStrategyRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Create a new trading strategy."""
    try:
        builder = await get_strategy_builder_instance()

        # Create strategy
        strategy = builder.create_custom_strategy(request.name, request.description)
        
        # Add rules
        for rule_request in request.rules:
            # Create entry conditions
            entry_conditions = []
            for condition_req in rule_request.entry_conditions:
                condition = builder.add_indicator_condition(
                    strategy,
                    condition_req.indicator_name,
                    condition_req.indicator_params,
                    condition_req.comparison_operator,
                    condition_req.threshold_value,
                    condition_req.threshold_value_2,
                    condition_req.indicator_field,
                    condition_req.weight,
                    condition_req.description
                )
                entry_conditions.append(condition)
            
            entry_group = builder.create_condition_group(
                entry_conditions,
                rule_request.logical_operator
            )
            
            # Create exit conditions if provided
            exit_group = None
            if rule_request.exit_conditions:
                exit_conditions = []
                for condition_req in rule_request.exit_conditions:
                    condition = builder.add_indicator_condition(
                        strategy,
                        condition_req.indicator_name,
                        condition_req.indicator_params,
                        condition_req.comparison_operator,
                        condition_req.threshold_value,
                        condition_req.threshold_value_2,
                        condition_req.indicator_field,
                        condition_req.weight,
                        condition_req.description
                    )
                    exit_conditions.append(condition)
                
                exit_group = builder.create_condition_group(
                    exit_conditions,
                    rule_request.logical_operator
                )
            
            # Add rule to strategy
            builder.add_strategy_rule(
                strategy,
                rule_request.name,
                entry_group,
                rule_request.signal_type,
                exit_group,
                rule_request.min_confidence,
                None,  # max_holding_period
                rule_request.stop_loss_pct,
                rule_request.take_profit_pct,
                rule_request.description
            )
        
        # Validate strategy
        validation = builder.validate_strategy(strategy)
        
        # Get complexity score
        complexity = builder.get_strategy_complexity_score(strategy)
        
        return {
            "success": True,
            "strategy": {
                "name": strategy.name,
                "description": strategy.description,
                "rules_count": len(strategy.rules),
                "validation": validation,
                "complexity": complexity
            },
            "strategy_export": strategy.export_strategy()
        }
        
    except Exception as e:
        logger.error(f"Error creating strategy: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/strategies/validate")
async def validate_strategy(
    strategy_config: Dict[str, Any],
    current_user: Dict = Depends(get_current_user)
):
    """Validate a strategy configuration."""
    try:
        builder = await get_strategy_builder_instance()

        # Import strategy from config
        strategy = builder.import_strategy_json(json.dumps(strategy_config))
        
        # Validate
        validation = builder.validate_strategy(strategy)
        complexity = builder.get_strategy_complexity_score(strategy)
        
        return {
            "success": True,
            "validation": validation,
            "complexity": complexity,
            "summary": strategy.get_strategy_summary()
        }
        
    except Exception as e:
        logger.error(f"Error validating strategy: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# ==================== SIGNAL GENERATION ENDPOINTS ====================

@router.post("/signals/generate")
async def generate_signals(
    market_data: Dict[str, Dict[str, List[float]]],
    strategy_config: Dict[str, Any],
    signal_filter: Optional[SignalFilterRequest] = None,
    current_user: Dict = Depends(get_current_user)
):
    """Generate trading signals for a strategy."""
    try:
        engine = await get_signal_engine()
        builder = await get_strategy_builder_instance()

        # Import strategy
        strategy = builder.import_strategy_json(json.dumps(strategy_config))
        
        # Add strategy to engine
        engine.add_strategy(strategy)
        
        # Convert market data
        import numpy as np
        converted_data = {}
        for timeframe_str, data in market_data.items():
            timeframe = TimeFrame(timeframe_str)
            converted_data[timeframe] = {}
            for key, values in data.items():
                converted_data[timeframe][key] = np.array(values, dtype=float)
        
        # Create signal filter
        filter_config = SignalFilter()
        if signal_filter:
            filter_config.min_confidence = signal_filter.min_confidence
            filter_config.min_consensus_score = signal_filter.min_consensus_score
            filter_config.require_trend_alignment = signal_filter.require_trend_alignment
            filter_config.min_trend_alignment = signal_filter.min_trend_alignment
            filter_config.max_signals_per_timeframe = signal_filter.max_signals_per_timeframe
        
        # Generate signals
        signals = engine.generate_multi_timeframe_signals(
            converted_data,
            primary_timeframe=TimeFrame.H1,
            signal_filter=filter_config
        )
        
        # Format response
        formatted_signals = []
        for signal in signals:
            formatted_signals.append({
                "signal_type": signal.primary_signal.signal_type.value,
                "confidence": signal.primary_signal.confidence,
                "strength": signal.primary_signal.strength,
                "consensus_score": signal.consensus_score,
                "quality": signal.quality.value,
                "trend_alignment": signal.trend_alignment,
                "momentum_strength": signal.momentum_strength,
                "conditions_met": signal.primary_signal.conditions_met,
                "conditions_failed": signal.primary_signal.conditions_failed,
                "indicator_values": signal.primary_signal.indicator_values,
                "timestamp": signal.primary_signal.timestamp.isoformat()
            })
        
        return {
            "success": True,
            "signals": formatted_signals,
            "total_signals": len(formatted_signals),
            "engine_stats": engine.get_signal_statistics()
        }
        
    except Exception as e:
        logger.error(f"Error generating signals: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# ==================== TRADING ENGINE ENDPOINTS ====================

@router.post("/trading-engine/create")
async def create_trading_engine(
    config: TradingEngineConfigRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Create a new trading engine instance."""
    try:
        user_id = str(current_user.get('id'))
        
        if user_id in trading_engines:
            raise HTTPException(status_code=400, detail="Trading engine already exists for user")
        
        # Create engine config
        engine_config = TradingEngineConfig(
            max_concurrent_orders=config.max_concurrent_orders,
            enable_paper_trading=config.enable_paper_trading,
            max_daily_trades=config.max_daily_trades,
            max_daily_loss_pct=config.max_daily_loss_pct,
            commission_rate=config.commission_rate
        )
        
        # Create risk management config
        risk_config = RiskManagementConfig()
        if config.risk_management:
            risk_config.max_portfolio_risk = config.risk_management.get('max_portfolio_risk', 2.0)
            risk_config.max_position_risk = config.risk_management.get('max_position_risk', 1.0)
            risk_config.position_sizing_method = PositionSizingMethod(
                config.risk_management.get('position_sizing_method', 'VOLATILITY_ADJUSTED')
            )
            risk_config.risk_level = RiskLevel(
                config.risk_management.get('risk_level', 'MODERATE')
            )
        
        # Create trading engine
        trading_engine = TechnicalIndicatorTradingEngine(
            config=engine_config,
            risk_config=risk_config
        )
        
        trading_engines[user_id] = trading_engine
        
        return {
            "success": True,
            "message": "Trading engine created successfully",
            "engine_id": user_id,
            "status": trading_engine.get_engine_status()
        }
        
    except Exception as e:
        logger.error(f"Error creating trading engine: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/trading-engine/start")
async def start_trading_engine(
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user)
):
    """Start the trading engine."""
    try:
        user_id = str(current_user.get('id'))
        
        if user_id not in trading_engines:
            raise HTTPException(status_code=404, detail="Trading engine not found")
        
        engine = trading_engines[user_id]
        
        if engine.state != EngineState.STOPPED:
            raise HTTPException(status_code=400, detail=f"Engine is not stopped (current state: {engine.state.value})")
        
        # Start engine in background
        background_tasks.add_task(engine.start)
        
        return {
            "success": True,
            "message": "Trading engine start initiated",
            "status": engine.get_engine_status()
        }
        
    except Exception as e:
        logger.error(f"Error starting trading engine: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/trading-engine/stop")
async def stop_trading_engine(
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user)
):
    """Stop the trading engine."""
    try:
        user_id = str(current_user.get('id'))
        
        if user_id not in trading_engines:
            raise HTTPException(status_code=404, detail="Trading engine not found")
        
        engine = trading_engines[user_id]
        
        # Stop engine in background
        background_tasks.add_task(engine.stop)
        
        return {
            "success": True,
            "message": "Trading engine stop initiated",
            "status": engine.get_engine_status()
        }
        
    except Exception as e:
        logger.error(f"Error stopping trading engine: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/trading-engine/status")
async def get_trading_engine_status(
    current_user: Dict = Depends(get_current_user)
):
    """Get trading engine status."""
    try:
        user_id = str(current_user.get('id'))
        
        if user_id not in trading_engines:
            raise HTTPException(status_code=404, detail="Trading engine not found")
        
        engine = trading_engines[user_id]
        status = engine.get_engine_status()
        
        return {
            "success": True,
            "status": status,
            "positions": {
                symbol: {
                    "symbol": pos.symbol,
                    "quantity": float(pos.quantity),
                    "entry_price": float(pos.entry_price),
                    "current_price": float(pos.current_price),
                    "unrealized_pnl": float(pos.unrealized_pnl),
                    "market_value": float(pos.market_value)
                }
                for symbol, pos in engine.positions.items()
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting trading engine status: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/trading-engine/add-strategy")
async def add_strategy_to_engine(
    strategy_config: Dict[str, Any],
    current_user: Dict = Depends(get_current_user)
):
    """Add a strategy to the trading engine."""
    try:
        user_id = str(current_user.get('id'))
        
        if user_id not in trading_engines:
            raise HTTPException(status_code=404, detail="Trading engine not found")
        
        engine = trading_engines[user_id]
        builder = await get_strategy_builder_instance()

        # Import strategy
        strategy = builder.import_strategy_json(json.dumps(strategy_config))
        
        # Add to engine
        engine.add_strategy(strategy)
        
        return {
            "success": True,
            "message": f"Strategy '{strategy.name}' added to trading engine",
            "strategies_count": len(engine.signal_engine.strategies)
        }
        
    except Exception as e:
        logger.error(f"Error adding strategy to engine: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# ==================== WEBSOCKET ENDPOINTS ====================

@router.websocket("/trading-engine/ws")
async def trading_engine_websocket(
    websocket: WebSocket,
    current_user: Dict = Depends(get_current_user)
):
    """WebSocket endpoint for real-time trading engine updates."""
    await websocket.accept()
    
    user_id = str(current_user.get('id'))
    
    # Add to connections
    if user_id not in websocket_connections:
        websocket_connections[user_id] = []
    websocket_connections[user_id].append(websocket)
    
    try:
        # Send initial status
        if user_id in trading_engines:
            engine = trading_engines[user_id]
            await websocket.send_json({
                "type": "status",
                "data": engine.get_engine_status()
            })
        
        # Keep connection alive
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                if message.get('type') == 'ping':
                    await websocket.send_json({'type': 'pong'})
                    
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
                break
                
    except WebSocketDisconnect:
        pass
    finally:
        # Remove from connections
        if user_id in websocket_connections:
            websocket_connections[user_id] = [
                ws for ws in websocket_connections[user_id] if ws != websocket
            ]
            if not websocket_connections[user_id]:
                del websocket_connections[user_id]


# ==================== UTILITY ENDPOINTS ====================

@router.get("/recommendations/indicators")
async def get_indicator_recommendations(
    market_condition: str,
    current_indicators: Optional[List[str]] = None
):
    """Get indicator recommendations based on market conditions."""
    try:
        builder = await get_strategy_builder_instance()

        # Get recommendations for market condition
        recommended = builder.get_recommended_indicators_for_market_condition(market_condition)
        
        # Get complementary indicators if current indicators provided
        complementary = []
        if current_indicators:
            complementary = builder.suggest_complementary_indicators(current_indicators)
        
        return {
            "success": True,
            "market_condition": market_condition,
            "recommended_indicators": recommended,
            "complementary_indicators": complementary
        }
        
    except Exception as e:
        logger.error(f"Error getting indicator recommendations: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/system/info")
async def get_system_info_endpoint():
    """Get technical indicator system information."""
    try:
        system_info = get_system_info()
        return {
            "success": True,
            "system_info": system_info
        }
    except Exception as e:
        logger.error(f"Error getting system info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system/requirements")
async def check_system_requirements():
    """Check system requirements and dependencies."""
    try:
        requirements = validate_system_requirements()
        return {
            "success": True,
            "requirements": requirements
        }
    except Exception as e:
        logger.error(f"Error checking system requirements: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system/config")
async def get_system_config():
    """Get current system configuration."""
    try:
        config = get_config()
        return {
            "success": True,
            "config": {
                "environment": config.environment,
                "data_provider": config.data_provider.provider,
                "broker": config.broker.broker,
                "live_trading_enabled": config.is_live_trading_enabled(),
                "supported_symbols": config.supported_symbols,
                "default_timeframes": [tf.value for tf in config.default_timeframes],
                "risk_settings": {
                    "max_portfolio_risk": config.default_max_portfolio_risk,
                    "max_position_risk": config.default_max_position_risk,
                    "max_correlation": config.default_max_correlation
                }
            }
        }
    except Exception as e:
        logger.error(f"Error getting system config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check system requirements
        requirements = validate_system_requirements()

        return {
            "success": True,
            "status": "healthy" if requirements["status"] == "ok" else "degraded",
            "active_engines": len(trading_engines),
            "websocket_connections": sum(len(conns) for conns in websocket_connections.values()),
            "system_status": requirements["status"],
            "issues": requirements.get("issues", [])
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "success": False,
            "status": "unhealthy",
            "error": str(e)
        }


# ==================== TRADING BOT MANAGEMENT ENDPOINTS ====================

# Global bot storage (in production, this would be in a database)
trading_bots: Dict[str, Dict[str, Any]] = {}

class TradingBotConfigRequest(BaseModel):
    """Request model for trading bot configuration."""
    name: str = Field(..., description="Bot name")
    description: str = Field(..., description="Bot description")
    strategy: Dict[str, Any] = Field(..., description="Strategy configuration")
    risk_management: Dict[str, Any] = Field(..., description="Risk management settings")
    trading_engine: Dict[str, Any] = Field(..., description="Trading engine settings")
    signal_filter: Dict[str, Any] = Field(..., description="Signal filter settings")
    symbols: List[str] = Field(..., description="Trading symbols")
    initial_capital: float = Field(..., description="Initial capital")


@router.post("/bots")
async def create_trading_bot(
    config: TradingBotConfigRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Create a new trading bot."""
    try:
        import uuid
        from datetime import datetime

        user_id = str(current_user.get('id'))
        bot_id = str(uuid.uuid4())

        # Create bot configuration
        bot_config = {
            "id": bot_id,
            "user_id": user_id,
            "config": config.dict(),
            "status": "stopped",
            "created_at": datetime.utcnow().isoformat(),
            "metrics": {
                "performance": {
                    "total_return": 0.0,
                    "total_return_pct": 0.0,
                    "annualized_return": 0.0,
                    "sharpe_ratio": 0.0,
                    "sortino_ratio": 0.0,
                    "max_drawdown": 0.0,
                    "max_drawdown_pct": 0.0,
                    "win_rate": 0.0,
                    "profit_factor": 0.0,
                    "total_trades": 0,
                    "winning_trades": 0,
                    "losing_trades": 0,
                    "avg_trade_return": 0.0,
                    "avg_winning_trade": 0.0,
                    "avg_losing_trade": 0.0,
                    "avg_trade_duration": 0.0,
                    "volatility": 0.0,
                    "beta": 0.0,
                    "alpha": 0.0,
                    "var_95": 0.0,
                    "calmar_ratio": 0.0,
                    "information_ratio": 0.0,
                    "treynor_ratio": 0.0
                },
                "current_positions": [],
                "recent_trades": [],
                "recent_signals": [],
                "balance": config.initial_capital,
                "equity": config.initial_capital,
                "margin_used": 0.0,
                "margin_available": config.initial_capital,
                "unrealized_pnl": 0.0,
                "realized_pnl": 0.0,
                "timestamp": datetime.utcnow().isoformat()
            }
        }

        # Store bot
        trading_bots[bot_id] = bot_config

        return {
            "success": True,
            "bot": bot_config,
            "message": f"Trading bot '{config.name}' created successfully"
        }

    except Exception as e:
        logger.error(f"Error creating trading bot: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/bots")
async def get_trading_bots(
    current_user: Dict = Depends(get_current_user)
):
    """Get all trading bots for the current user."""
    try:
        user_id = str(current_user.get('id'))

        # Filter bots by user
        user_bots = [
            bot for bot in trading_bots.values()
            if bot.get('user_id') == user_id
        ]

        return {
            "success": True,
            "bots": user_bots,
            "total": len(user_bots)
        }

    except Exception as e:
        logger.error(f"Error getting trading bots: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bots/{bot_id}")
async def get_trading_bot(
    bot_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """Get a specific trading bot."""
    try:
        user_id = str(current_user.get('id'))

        if bot_id not in trading_bots:
            raise HTTPException(status_code=404, detail="Bot not found")

        bot = trading_bots[bot_id]

        # Check ownership
        if bot.get('user_id') != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        return {
            "success": True,
            "bot": bot
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting trading bot {bot_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/bots/{bot_id}")
async def update_trading_bot(
    bot_id: str,
    config: Dict[str, Any],
    current_user: Dict = Depends(get_current_user)
):
    """Update trading bot configuration."""
    try:
        user_id = str(current_user.get('id'))

        if bot_id not in trading_bots:
            raise HTTPException(status_code=404, detail="Bot not found")

        bot = trading_bots[bot_id]

        # Check ownership
        if bot.get('user_id') != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        # Update configuration
        bot['config'].update(config)
        bot['updated_at'] = datetime.utcnow().isoformat()

        return {
            "success": True,
            "bot": bot
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating trading bot {bot_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/bots/{bot_id}")
async def delete_trading_bot(
    bot_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """Delete a trading bot."""
    try:
        user_id = str(current_user.get('id'))

        if bot_id not in trading_bots:
            raise HTTPException(status_code=404, detail="Bot not found")

        bot = trading_bots[bot_id]

        # Check ownership
        if bot.get('user_id') != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        # Stop bot if running
        if bot['status'] in ['running', 'starting']:
            bot['status'] = 'stopped'

        # Remove from storage
        del trading_bots[bot_id]

        return {
            "success": True,
            "message": "Bot deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting trading bot {bot_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bots/{bot_id}/start")
async def start_trading_bot(
    bot_id: str,
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user)
):
    """Start a trading bot."""
    try:
        user_id = str(current_user.get('id'))

        if bot_id not in trading_bots:
            raise HTTPException(status_code=404, detail="Bot not found")

        bot = trading_bots[bot_id]

        # Check ownership
        if bot.get('user_id') != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        # Check if bot can be started
        if bot['status'] not in ['stopped', 'error']:
            raise HTTPException(
                status_code=400,
                detail=f"Bot cannot be started from {bot['status']} state"
            )

        # Update status
        bot['status'] = 'starting'
        bot['started_at'] = datetime.utcnow().isoformat()

        # Start bot logic in background (simulate)
        async def start_bot_logic():
            await asyncio.sleep(2)  # Simulate startup time
            bot['status'] = 'running'

            # Notify WebSocket clients
            await notify_bot_status_change(bot_id, 'running')

        background_tasks.add_task(start_bot_logic)

        return {
            "success": True,
            "bot": bot,
            "message": "Bot start initiated"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting trading bot {bot_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bots/{bot_id}/stop")
async def stop_trading_bot(
    bot_id: str,
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user)
):
    """Stop a trading bot."""
    try:
        user_id = str(current_user.get('id'))

        if bot_id not in trading_bots:
            raise HTTPException(status_code=404, detail="Bot not found")

        bot = trading_bots[bot_id]

        # Check ownership
        if bot.get('user_id') != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        # Check if bot can be stopped
        if bot['status'] in ['stopped', 'stopping']:
            raise HTTPException(
                status_code=400,
                detail=f"Bot is already {bot['status']}"
            )

        # Update status
        bot['status'] = 'stopping'

        # Stop bot logic in background (simulate)
        async def stop_bot_logic():
            await asyncio.sleep(1)  # Simulate stop time
            bot['status'] = 'stopped'
            bot['stopped_at'] = datetime.utcnow().isoformat()

            # Notify WebSocket clients
            await notify_bot_status_change(bot_id, 'stopped')

        background_tasks.add_task(stop_bot_logic)

        return {
            "success": True,
            "bot": bot,
            "message": "Bot stop initiated"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping trading bot {bot_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bots/{bot_id}/pause")
async def pause_trading_bot(
    bot_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """Pause a trading bot."""
    try:
        user_id = str(current_user.get('id'))

        if bot_id not in trading_bots:
            raise HTTPException(status_code=404, detail="Bot not found")

        bot = trading_bots[bot_id]

        # Check ownership
        if bot.get('user_id') != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        # Check if bot can be paused
        if bot['status'] != 'running':
            raise HTTPException(
                status_code=400,
                detail=f"Bot cannot be paused from {bot['status']} state"
            )

        # Update status
        bot['status'] = 'paused'

        # Notify WebSocket clients
        await notify_bot_status_change(bot_id, 'paused')

        return {
            "success": True,
            "bot": bot,
            "message": "Bot paused successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error pausing trading bot {bot_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bots/{bot_id}/resume")
async def resume_trading_bot(
    bot_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """Resume a trading bot."""
    try:
        user_id = str(current_user.get('id'))

        if bot_id not in trading_bots:
            raise HTTPException(status_code=404, detail="Bot not found")

        bot = trading_bots[bot_id]

        # Check ownership
        if bot.get('user_id') != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        # Check if bot can be resumed
        if bot['status'] != 'paused':
            raise HTTPException(
                status_code=400,
                detail=f"Bot cannot be resumed from {bot['status']} state"
            )

        # Update status
        bot['status'] = 'running'

        # Notify WebSocket clients
        await notify_bot_status_change(bot_id, 'running')

        return {
            "success": True,
            "bot": bot,
            "message": "Bot resumed successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resuming trading bot {bot_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bots/{bot_id}/metrics")
async def get_bot_metrics(
    bot_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """Get trading bot performance metrics."""
    try:
        user_id = str(current_user.get('id'))

        if bot_id not in trading_bots:
            raise HTTPException(status_code=404, detail="Bot not found")

        bot = trading_bots[bot_id]

        # Check ownership
        if bot.get('user_id') != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        return {
            "success": True,
            "metrics": bot['metrics']
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting bot metrics {bot_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/strategies/templates")
async def get_strategy_templates():
    """Get available strategy templates."""
    try:
        templates = [
            {
                "name": "Mean Reversion Pro",
                "description": "Professional mean reversion strategy using Bollinger Bands, RSI, and volume confirmation",
                "strategy_type": "mean_reversion",
                "risk_level": "medium",
                "timeframes": ["15m", "1h"],
                "indicators": [
                    {"name": "BBANDS", "parameters": {"period": 20, "std": 2}, "weight": 0.3, "enabled": True},
                    {"name": "RSI", "parameters": {"period": 14}, "weight": 0.4, "enabled": True},
                    {"name": "STOCHRSI", "parameters": {"period": 14, "k": 3, "d": 3}, "weight": 0.3, "enabled": True}
                ],
                "rules": [{
                    "name": "Mean Reversion Entry",
                    "entry_conditions": {
                        "conditions": [
                            {"indicator": "BBANDS_LOWER", "operator": "LT", "value": "close"},
                            {"indicator": "RSI", "operator": "LT", "value": 30},
                            {"indicator": "STOCHRSI_K", "operator": "LT", "value": 20}
                        ],
                        "operator": "AND"
                    },
                    "signal_type": "BUY",
                    "min_confidence": 0.7,
                    "stop_loss_pct": 1.5,
                    "take_profit_pct": 2.5
                }],
                "parameters": {
                    "max_holding_period": 24,
                    "volume_confirmation": True,
                    "trend_filter": False
                }
            },
            {
                "name": "Momentum Breakout",
                "description": "Aggressive momentum strategy with EMA crossover, MACD, and ADX confirmation",
                "strategy_type": "momentum",
                "risk_level": "high",
                "timeframes": ["15m", "1h", "4h"],
                "indicators": [
                    {"name": "EMA", "parameters": {"period": 12}, "weight": 0.25, "enabled": True},
                    {"name": "EMA", "parameters": {"period": 26}, "weight": 0.25, "enabled": True},
                    {"name": "MACD", "parameters": {"fast": 12, "slow": 26, "signal": 9}, "weight": 0.3, "enabled": True},
                    {"name": "ADX", "parameters": {"period": 14}, "weight": 0.2, "enabled": True}
                ],
                "rules": [{
                    "name": "Momentum Entry",
                    "entry_conditions": {
                        "conditions": [
                            {"indicator": "EMA_12", "operator": "CROSS_ABOVE", "value": "EMA_26"},
                            {"indicator": "MACD", "operator": "CROSS_ABOVE", "value": "MACD_SIGNAL"},
                            {"indicator": "ADX", "operator": "GT", "value": 25}
                        ],
                        "operator": "AND"
                    },
                    "signal_type": "BUY",
                    "min_confidence": 0.75,
                    "stop_loss_pct": 3.0,
                    "take_profit_pct": 8.0
                }],
                "parameters": {
                    "max_holding_period": 48,
                    "trailing_stop": True,
                    "trend_confirmation": True
                }
            },
            {
                "name": "Multi-Timeframe Analysis",
                "description": "Professional multi-timeframe strategy with Ichimoku, EMA, and RSI convergence",
                "strategy_type": "multi_timeframe",
                "risk_level": "medium",
                "timeframes": ["15m", "1h", "4h"],
                "indicators": [
                    {"name": "ICHIMOKU", "parameters": {"tenkan": 9, "kijun": 26, "senkou": 52}, "weight": 0.4, "enabled": True},
                    {"name": "EMA", "parameters": {"period": 20}, "weight": 0.2, "enabled": True},
                    {"name": "EMA", "parameters": {"period": 50}, "weight": 0.2, "enabled": True},
                    {"name": "RSI", "parameters": {"period": 14}, "weight": 0.2, "enabled": True}
                ],
                "rules": [{
                    "name": "Multi-Timeframe Entry",
                    "entry_conditions": {
                        "conditions": [
                            {"indicator": "ICHIMOKU_ABOVE_CLOUD", "operator": "EQ", "value": True},
                            {"indicator": "EMA_20", "operator": "GT", "value": "EMA_50"},
                            {"indicator": "RSI", "operator": "GT", "value": 30},
                            {"indicator": "RSI", "operator": "LT", "value": 70}
                        ],
                        "operator": "AND"
                    },
                    "signal_type": "BUY",
                    "min_confidence": 0.8,
                    "stop_loss_pct": 2.0,
                    "take_profit_pct": 6.0
                }],
                "parameters": {
                    "max_holding_period": 72,
                    "multi_timeframe_confirmation": True,
                    "consensus_threshold": 0.7
                }
            }
        ]

        return {
            "success": True,
            "strategies": templates
        }

    except Exception as e:
        logger.error(f"Error getting strategy templates: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/strategies/backtest")
async def backtest_strategy(
    strategy: Dict[str, Any],
    config: Dict[str, Any],
    current_user: Dict = Depends(get_current_user)
):
    """Backtest a strategy configuration."""
    try:
        # Mock backtesting results for now
        # In production, this would run actual backtesting

        import random
        from datetime import datetime, timedelta

        # Generate mock performance data
        start_date = datetime.fromisoformat(config['start_date'])
        end_date = datetime.fromisoformat(config['end_date'])
        days = (end_date - start_date).days

        # Mock results
        total_return = random.uniform(-20, 50)  # -20% to +50%
        win_rate = random.uniform(45, 85)  # 45% to 85%
        total_trades = random.randint(50, 500)
        sharpe_ratio = random.uniform(0.5, 2.5)
        max_drawdown = random.uniform(-25, -5)  # -25% to -5%

        results = {
            "success": True,
            "backtest_id": str(uuid.uuid4()),
            "strategy_name": strategy.get('name', 'Unknown Strategy'),
            "period": {
                "start_date": config['start_date'],
                "end_date": config['end_date'],
                "days": days
            },
            "performance": {
                "total_return": total_return,
                "total_return_pct": total_return,
                "annualized_return": (total_return / days) * 365 if days > 0 else 0,
                "sharpe_ratio": sharpe_ratio,
                "sortino_ratio": sharpe_ratio * 1.2,
                "max_drawdown": max_drawdown,
                "max_drawdown_pct": max_drawdown,
                "win_rate": win_rate,
                "profit_factor": random.uniform(1.1, 3.0),
                "total_trades": total_trades,
                "winning_trades": int(total_trades * win_rate / 100),
                "losing_trades": int(total_trades * (100 - win_rate) / 100),
                "avg_trade_return": total_return / total_trades if total_trades > 0 else 0,
                "volatility": random.uniform(15, 35),
                "calmar_ratio": abs(total_return / max_drawdown) if max_drawdown != 0 else 0
            },
            "risk_metrics": {
                "var_95": random.uniform(-10, -2),
                "beta": random.uniform(0.8, 1.5),
                "alpha": random.uniform(-2, 5)
            },
            "trade_analysis": {
                "avg_winning_trade": random.uniform(2, 8),
                "avg_losing_trade": random.uniform(-5, -1),
                "largest_win": random.uniform(10, 25),
                "largest_loss": random.uniform(-15, -5),
                "avg_trade_duration": random.uniform(2, 48)  # hours
            }
        }

        return results

    except Exception as e:
        logger.error(f"Error backtesting strategy: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# WebSocket notification helper
async def notify_bot_status_change(bot_id: str, status: str):
    """Notify WebSocket clients about bot status changes."""
    try:
        message = {
            "type": "bot_status",
            "bot_id": bot_id,
            "status": status,
            "timestamp": datetime.utcnow().isoformat()
        }

        # Send to all connected WebSocket clients
        # In production, this would filter by user permissions
        for user_connections in websocket_connections.values():
            for websocket in user_connections:
                try:
                    await websocket.send_json(message)
                except Exception as e:
                    logger.warning(f"Failed to send WebSocket message: {e}")

    except Exception as e:
        logger.error(f"Error sending bot status notification: {e}")

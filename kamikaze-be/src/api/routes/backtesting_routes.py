"""
Backtesting API Routes
FastAPI endpoints for backtesting functionality
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from decimal import Decimal

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query, WebSocket, WebSocketDisconnect
from pydantic import BaseModel, Field, validator
import asyncpg
import json

from ..models.backtesting_models import (
    BacktestingConfigRequest,
    BacktestingResponse,
    BacktestingResultsResponse,
    BacktestingHistoryResponse,
    BacktestingProgressResponse
)
from ...services.backtesting_engine import BacktestingEngine, BacktestConfig
from ...services.trading_strategies import StrategyFactory
from ...services.risk_management import RiskManager
from ...infrastructure.database_config import DatabaseConfig
from ...shared.logging_config import setup_logging
from .auth_routes import get_current_user

logger = setup_logging("backtesting_routes")

# Create router
router = APIRouter(prefix="/api/v1/backtesting", tags=["Backtesting"])

# Global backtesting engine
backtesting_engine: Optional[BacktestingEngine] = None
active_backtests: Dict[str, Dict] = {}
websocket_connections: Dict[str, List[WebSocket]] = {}  # backtest_id -> list of websockets


async def get_backtesting_engine():
    """Get or create backtesting engine instance."""
    global backtesting_engine
    if backtesting_engine is None:
        backtesting_engine = BacktestingEngine()
        await backtesting_engine.connect()
    return backtesting_engine


@router.post("/start", response_model=BacktestingResponse)
async def start_backtest(
    config: BacktestingConfigRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user)
):
    """Start a new backtesting run."""
    try:
        engine = await get_backtesting_engine()
        
        # Validate configuration
        if not config.trading_pairs:
            raise HTTPException(status_code=400, detail="At least one trading pair is required")
        
        if config.start_date >= config.end_date:
            raise HTTPException(status_code=400, detail="Start date must be before end date")
        
        if config.initial_capital <= 0:
            raise HTTPException(status_code=400, detail="Initial capital must be positive")
        
        # Create strategy instance
        try:
            strategy = StrategyFactory.create_strategy(
                config.strategy.id, 
                config.strategy.parameters
            )
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid strategy: {str(e)}")
        
        # Create backtest configuration
        backtest_config = BacktestConfig(
            strategy_id=config.strategy.id,
            strategy_name=config.strategy.name,
            strategy_parameters=config.strategy.parameters,
            trading_pairs=config.trading_pairs,
            timeframe=config.timeframe,
            start_date=config.start_date,
            end_date=config.end_date,
            initial_capital=Decimal(str(config.initial_capital)),
            commission=Decimal(str(config.commission)),
            slippage=Decimal(str(config.slippage)),
            risk_management=config.risk_management.dict() if config.risk_management else {},
            benchmark=config.benchmark
        )
        
        # Create progress tracking
        backtest_id = str(uuid.uuid4())
        active_backtests[backtest_id] = {
            'status': 'starting',
            'progress': 0,
            'start_time': datetime.utcnow(),
            'config': backtest_config
        }
        
        # Start backtest in background
        background_tasks.add_task(
            run_backtest_task,
            backtest_id,
            backtest_config,
            strategy,
            current_user.get('id')
        )
        
        logger.info(f"Started backtest {backtest_id} for user {current_user.get('id')}")
        
        return BacktestingResponse(
            success=True,
            message="Backtest started successfully",
            backtest_id=backtest_id,
            status="starting"
        )
        
    except Exception as e:
        logger.error(f"Error starting backtest: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start backtest: {str(e)}")


async def run_backtest_task(
    backtest_id: str,
    config: BacktestConfig,
    strategy: Any,
    user_id: Optional[int] = None
):
    """Background task to run the backtest."""
    try:
        # Update status
        active_backtests[backtest_id]['status'] = 'running'
        
        # Progress callback with WebSocket broadcasting
        async def progress_callback(progress: int):
            if backtest_id in active_backtests:
                active_backtests[backtest_id]['progress'] = progress

                # Broadcast progress to connected WebSockets
                await broadcast_progress_update(backtest_id, {
                    'type': 'progress',
                    'backtest_id': backtest_id,
                    'progress': progress,
                    'status': active_backtests[backtest_id]['status']
                })
        
        # Run the backtest
        engine = await get_backtesting_engine()
        
        # Create strategy function wrapper
        async def strategy_wrapper(market_data, portfolio, config):
            return await strategy.generate_signals(market_data, portfolio, config)
        
        result_id = await engine.run_backtest(config, strategy_wrapper, progress_callback)
        
        # Update status
        active_backtests[backtest_id]['status'] = 'completed'
        active_backtests[backtest_id]['result_id'] = result_id
        active_backtests[backtest_id]['end_time'] = datetime.utcnow()
        
        logger.info(f"Backtest {backtest_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Backtest {backtest_id} failed: {e}")
        if backtest_id in active_backtests:
            active_backtests[backtest_id]['status'] = 'failed'
            active_backtests[backtest_id]['error'] = str(e)


@router.get("/status/{backtest_id}", response_model=BacktestingProgressResponse)
async def get_backtest_status(backtest_id: str):
    """Get the status and progress of a running backtest."""
    if backtest_id not in active_backtests:
        # Check database for completed backtests
        try:
            config = DatabaseConfig()
            pool = await asyncpg.create_pool(**config.connection_params)
            
            async with pool.acquire() as conn:
                row = await conn.fetchrow(
                    "SELECT status, progress, error_message FROM backtesting_runs WHERE id = $1",
                    backtest_id
                )
                
                if row:
                    return BacktestingProgressResponse(
                        backtest_id=backtest_id,
                        status=row['status'],
                        progress=row['progress'],
                        error_message=row['error_message']
                    )
            
            await pool.close()
            
        except Exception as e:
            logger.error(f"Error checking backtest status: {e}")
        
        raise HTTPException(status_code=404, detail="Backtest not found")
    
    backtest_info = active_backtests[backtest_id]
    
    return BacktestingProgressResponse(
        backtest_id=backtest_id,
        status=backtest_info['status'],
        progress=backtest_info['progress'],
        error_message=backtest_info.get('error')
    )


@router.get("/results/{backtest_id}", response_model=BacktestingResultsResponse)
async def get_backtest_results(backtest_id: str):
    """Get the results of a completed backtest."""
    try:
        config = DatabaseConfig()
        pool = await asyncpg.create_pool(**config.connection_params)
        
        async with pool.acquire() as conn:
            # Get backtest run info
            run_row = await conn.fetchrow(
                """
                SELECT br.*, bres.* FROM backtesting_runs br
                LEFT JOIN backtesting_results bres ON br.id = bres.backtest_id
                WHERE br.id = $1
                """,
                backtest_id
            )
            
            if not run_row:
                raise HTTPException(status_code=404, detail="Backtest not found")
            
            if run_row['status'] != 'completed':
                raise HTTPException(status_code=400, detail="Backtest not completed yet")
            
            # Get trades
            trade_rows = await conn.fetch(
                """
                SELECT * FROM backtesting_trades 
                WHERE backtest_id = $1 
                ORDER BY entry_time ASC
                """,
                backtest_id
            )
            
            # Get equity curve
            equity_rows = await conn.fetch(
                """
                SELECT * FROM backtesting_equity_curve 
                WHERE backtest_id = $1 
                ORDER BY timestamp ASC
                """,
                backtest_id
            )
            
            # Get monthly returns
            monthly_rows = await conn.fetch(
                """
                SELECT * FROM backtesting_monthly_returns 
                WHERE backtest_id = $1 
                ORDER BY year ASC, month ASC
                """,
                backtest_id
            )
        
        await pool.close()
        
        # Format response
        trades = []
        for trade in trade_rows:
            trades.append({
                'id': str(trade['id']),
                'symbol': trade['symbol'],
                'side': trade['side'],
                'entry_time': trade['entry_time'].isoformat(),
                'exit_time': trade['exit_time'].isoformat() if trade['exit_time'] else None,
                'entry_price': float(trade['entry_price']),
                'exit_price': float(trade['exit_price']) if trade['exit_price'] else None,
                'quantity': float(trade['quantity']),
                'pnl': float(trade['pnl']) if trade['pnl'] else 0,
                'pnl_percent': float(trade['pnl_percent']) if trade['pnl_percent'] else 0,
                'duration_minutes': trade['duration_minutes'],
                'entry_reason': trade['entry_reason'],
                'exit_reason': trade['exit_reason']
            })
        
        equity_curve = []
        for point in equity_rows:
            equity_curve.append({
                'timestamp': point['timestamp'].isoformat(),
                'value': float(point['portfolio_value']),
                'drawdown': float(point['drawdown']),
                'drawdown_percent': float(point['drawdown_percent'])
            })
        
        monthly_returns = []
        for month in monthly_rows:
            monthly_returns.append({
                'month': month['month_name'],
                'return': float(month['return_value'])
            })
        
        # Build metrics
        metrics = {
            'total_return': float(run_row['total_return']),
            'total_return_percent': float(run_row['total_return_percent']),
            'annualized_return': float(run_row['annualized_return']),
            'sharpe_ratio': float(run_row['sharpe_ratio']) if run_row['sharpe_ratio'] else 0,
            'sortino_ratio': float(run_row['sortino_ratio']) if run_row['sortino_ratio'] else 0,
            'max_drawdown': float(run_row['max_drawdown']),
            'max_drawdown_percent': float(run_row['max_drawdown_percent']),
            'win_rate': float(run_row['win_rate']),
            'profit_factor': float(run_row['profit_factor']) if run_row['profit_factor'] else 0,
            'total_trades': run_row['total_trades'],
            'winning_trades': run_row['winning_trades'],
            'losing_trades': run_row['losing_trades'],
            'average_win': float(run_row['average_win']) if run_row['average_win'] else 0,
            'average_loss': float(run_row['average_loss']) if run_row['average_loss'] else 0,
            'largest_win': float(run_row['largest_win']) if run_row['largest_win'] else 0,
            'largest_loss': float(run_row['largest_loss']) if run_row['largest_loss'] else 0,
            'average_trade_duration': float(run_row['average_trade_duration']) if run_row['average_trade_duration'] else 0,
            'volatility': float(run_row['volatility']) if run_row['volatility'] else 0,
            'calmar_ratio': float(run_row['calmar_ratio']) if run_row['calmar_ratio'] else 0,
            'recovery_factor': float(run_row['recovery_factor']) if run_row['recovery_factor'] else 0,
            'payoff_ratio': float(run_row['payoff_ratio']) if run_row['payoff_ratio'] else 0,
            'expected_value': float(run_row['expected_value']) if run_row['expected_value'] else 0
        }
        
        return BacktestingResultsResponse(
            id=backtest_id,
            config={
                'strategy': {
                    'id': run_row['strategy_id'],
                    'name': run_row['strategy_name'],
                    'parameters': run_row['strategy_parameters']
                },
                'trading_pairs': run_row['trading_pairs'],
                'timeframe': run_row['timeframe'],
                'start_date': run_row['start_date'].isoformat(),
                'end_date': run_row['end_date'].isoformat(),
                'initial_capital': float(run_row['initial_capital']),
                'commission': float(run_row['commission']),
                'slippage': float(run_row['slippage']),
                'risk_management': run_row['risk_management']
            },
            start_time=run_row['started_at'].isoformat(),
            end_time=run_row['completed_at'].isoformat() if run_row['completed_at'] else None,
            status=run_row['status'],
            progress=run_row['progress'],
            trades=trades,
            metrics=metrics,
            equity=equity_curve,
            monthly_returns=monthly_returns
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting backtest results: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get results: {str(e)}")


@router.get("/history", response_model=BacktestingHistoryResponse)
async def get_backtest_history(
    limit: int = Query(default=20, le=100),
    offset: int = Query(default=0, ge=0),
    current_user: Dict = Depends(get_current_user)
):
    """Get backtesting history for the current user."""
    try:
        config = DatabaseConfig()
        pool = await asyncpg.create_pool(**config.connection_params)
        
        async with pool.acquire() as conn:
            # Get total count
            count_row = await conn.fetchrow(
                "SELECT COUNT(*) as total FROM backtesting_runs WHERE user_id = $1",
                current_user.get('id')
            )
            total = count_row['total'] if count_row else 0
            
            # Get backtest runs
            rows = await conn.fetch(
                """
                SELECT br.*, bres.total_return, bres.total_return_percent, 
                       bres.sharpe_ratio, bres.max_drawdown_percent, bres.total_trades
                FROM backtesting_runs br
                LEFT JOIN backtesting_results bres ON br.id = bres.backtest_id
                WHERE br.user_id = $1
                ORDER BY br.created_at DESC
                LIMIT $2 OFFSET $3
                """,
                current_user.get('id'), limit, offset
            )
        
        await pool.close()
        
        backtests = []
        for row in rows:
            backtests.append({
                'id': str(row['id']),
                'name': row['name'] or f"{row['strategy_name']} - {row['created_at'].strftime('%Y-%m-%d')}",
                'strategy_name': row['strategy_name'],
                'trading_pairs': row['trading_pairs'],
                'timeframe': row['timeframe'],
                'start_date': row['start_date'].isoformat(),
                'end_date': row['end_date'].isoformat(),
                'initial_capital': float(row['initial_capital']),
                'status': row['status'],
                'progress': row['progress'],
                'created_at': row['created_at'].isoformat(),
                'completed_at': row['completed_at'].isoformat() if row['completed_at'] else None,
                'total_return': float(row['total_return']) if row['total_return'] else None,
                'total_return_percent': float(row['total_return_percent']) if row['total_return_percent'] else None,
                'sharpe_ratio': float(row['sharpe_ratio']) if row['sharpe_ratio'] else None,
                'max_drawdown_percent': float(row['max_drawdown_percent']) if row['max_drawdown_percent'] else None,
                'total_trades': row['total_trades'] if row['total_trades'] else None
            })
        
        return BacktestingHistoryResponse(
            backtests=backtests,
            total=total,
            limit=limit,
            offset=offset
        )
        
    except Exception as e:
        logger.error(f"Error getting backtest history: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get history: {str(e)}")


@router.delete("/{backtest_id}")
async def delete_backtest(
    backtest_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """Delete a backtest and all its data."""
    try:
        config = DatabaseConfig()
        pool = await asyncpg.create_pool(**config.connection_params)
        
        async with pool.acquire() as conn:
            # Check if backtest exists and belongs to user
            row = await conn.fetchrow(
                "SELECT user_id FROM backtesting_runs WHERE id = $1",
                backtest_id
            )
            
            if not row:
                raise HTTPException(status_code=404, detail="Backtest not found")
            
            if row['user_id'] != current_user.get('id'):
                raise HTTPException(status_code=403, detail="Access denied")
            
            # Delete backtest (cascade will handle related tables)
            await conn.execute(
                "DELETE FROM backtesting_runs WHERE id = $1",
                backtest_id
            )
        
        await pool.close()
        
        # Remove from active backtests if present
        if backtest_id in active_backtests:
            del active_backtests[backtest_id]
        
        return {"success": True, "message": "Backtest deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting backtest: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete backtest: {str(e)}")


@router.get("/strategies")
async def get_available_strategies():
    """Get list of available trading strategies."""
    try:
        strategies = StrategyFactory.get_available_strategies()
        return {
            "success": True,
            "strategies": strategies
        }
    except Exception as e:
        logger.error(f"Error getting strategies: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get strategies: {str(e)}")


@router.websocket("/ws/{backtest_id}")
async def websocket_backtest_progress(websocket: WebSocket, backtest_id: str):
    """WebSocket endpoint for real-time backtest progress updates."""
    await websocket.accept()

    # Add to connections
    if backtest_id not in websocket_connections:
        websocket_connections[backtest_id] = []
    websocket_connections[backtest_id].append(websocket)

    try:
        # Send initial status if backtest exists
        if backtest_id in active_backtests:
            await websocket.send_json({
                'type': 'status',
                'backtest_id': backtest_id,
                'status': active_backtests[backtest_id]['status'],
                'progress': active_backtests[backtest_id]['progress']
            })

        # Keep connection alive and handle client messages
        while True:
            try:
                # Wait for client messages (ping/pong, etc.)
                data = await websocket.receive_text()
                message = json.loads(data)

                if message.get('type') == 'ping':
                    await websocket.send_json({'type': 'pong'})

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
                break

    except WebSocketDisconnect:
        pass
    finally:
        # Remove from connections
        if backtest_id in websocket_connections:
            websocket_connections[backtest_id] = [
                ws for ws in websocket_connections[backtest_id] if ws != websocket
            ]
            if not websocket_connections[backtest_id]:
                del websocket_connections[backtest_id]


async def broadcast_progress_update(backtest_id: str, message: Dict):
    """Broadcast progress update to all connected WebSockets for a backtest."""
    if backtest_id not in websocket_connections:
        return

    # Remove disconnected websockets
    active_connections = []

    for websocket in websocket_connections[backtest_id]:
        try:
            await websocket.send_json(message)
            active_connections.append(websocket)
        except Exception as e:
            logger.debug(f"Removing disconnected WebSocket: {e}")

    # Update connections list
    if active_connections:
        websocket_connections[backtest_id] = active_connections
    else:
        del websocket_connections[backtest_id]


# Cleanup function for application shutdown
async def cleanup_backtesting():
    """Cleanup backtesting resources."""
    global backtesting_engine, websocket_connections

    # Close all WebSocket connections
    for backtest_id, connections in websocket_connections.items():
        for websocket in connections:
            try:
                await websocket.close()
            except Exception:
                pass
    websocket_connections.clear()

    # Cleanup engine
    if backtesting_engine:
        await backtesting_engine.disconnect()
        backtesting_engine = None
